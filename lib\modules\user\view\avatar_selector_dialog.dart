import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/user/controller/user_controller.dart';

/// 头像选择对话框
/// 
/// 在用户页面中打开的头像选择对话框
class AvatarSelectorDialog extends GetWidget<UserController> {
  const AvatarSelectorDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Select Avatar',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
          ),
          const Divider(color: Colors.grey),
          Obx(() {
            if (controller.isUpdating.value) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
            }
            
            final selectedAvatar = controller.selectedAvatar.value;
            
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: controller.avatarUrls.length,
                itemBuilder: (context, index) {
                  final avatarUrl = controller.avatarUrls[index];
                  final isSelected = selectedAvatar == avatarUrl;
                  
                  return GestureDetector(
                    onTap: () {
                      // 只选择头像，不立即更新
                      controller.selectAvatar(avatarUrl);
                    },
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: isSelected
                                ? Border.all(color: Colors.blue, width: 4)
                                : Border.all(color: Colors.grey.shade800, width: 1),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: Colors.blue.withOpacity(0.5),
                                spreadRadius: 2,
                                blurRadius: 8,
                              )
                            ] : null,
                          ),
                          padding: isSelected ? const EdgeInsets.all(4) : null,
                          child: ClipOval(
                            child: Image.network(
                              avatarUrl,
                              fit: BoxFit.cover,
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return const Center(child: CircularProgressIndicator());
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[850],
                                  child: const Icon(
                                    Icons.error_outline,
                                    color: Colors.white,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        // 添加选中的勾选标记
                        if (isSelected)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 24,
                              height: 24,
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
            );
          }),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Obx(() => ElevatedButton(
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onPressed: controller.isUpdating.value 
                ? null 
                : () async {
                  final success = await controller.updateAvatar();
                  if (success) {
                    Get.back();
                  }
                },
              child: const Text('Save'),
            )),
          ),
        ],
      ),
    );
  }
} 