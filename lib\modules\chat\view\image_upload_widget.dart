import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:image_picker/image_picker.dart';

class ImageUploadWidget extends StatelessWidget {
  final List<String> imageUrls;
  final Function(List<String>) onImagesChanged;

  const ImageUploadWidget({
    Key? key,
    required this.imageUrls,
    required this.onImagesChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView(
      scrollDirection: Axis.horizontal,
      children: [
        // Image list
        ...List.generate(imageUrls.length, (index) => _buildImagePreview(index)),
        // Add image button (if not reached max count)
        if (imageUrls.length < StringsConsts.reportMaxImageCount)
          _buildAddImageButton(),
      ],
    );
  }

  Widget _buildImagePreview(int index) {
    return Padding(
      padding: const EdgeInsets.only(right: 6.0),
      child: Stack(
        children: [
          Container(
            width: 70,  // 减小尺寸
            height: 70, // 减小尺寸
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade600),
              borderRadius: BorderRadius.circular(6),  // 更小的圆角
              image: DecorationImage(
                image: imageUrls[index].startsWith('http')
                    ? NetworkImage(imageUrls[index]) as ImageProvider
                    : FileImage(File(imageUrls[index])),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 2,
            right: 2,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(153), // 0.6转换为Alpha值
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1),
                ),
                child: const Icon(Icons.close, size: 12, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        width: 70,  // 减小尺寸
        height: 70, // 减小尺寸
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade600, width: 1),
          borderRadius: BorderRadius.circular(6),  // 更小的圆角
          color: Colors.grey.shade900,
        ),
        child: const Center(
          child: Icon(
            Icons.add_photo_alternate_outlined,
            size: 24,  // 更小的图标
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );
      
      if (image != null) {
        final newImageUrls = [...imageUrls, image.path];
        onImagesChanged(newImageUrls);
      }
    } catch (e) {
      LogUtil.error('Image selection failed: $e');
      ToastUtil.error('Failed to select image. Please try again.');
    }
  }

  void _removeImage(int index) {
    final newImageUrls = List<String>.from(imageUrls);
    newImageUrls.removeAt(index);
    onImagesChanged(newImageUrls);
  }
} 