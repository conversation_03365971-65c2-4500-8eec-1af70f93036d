import 'package:get/get.dart';
import 'package:rolio/modules/role/controller/search_controller.dart';
import 'package:rolio/modules/role/service/search_service.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';

/// 搜索模块绑定
class SearchBinding implements Bindings {
  @override
  void dependencies() {
    // 注册搜索仓库
    Get.lazyPut<SearchRepository>(() => SearchRepository());
    
    // 注册搜索服务
    Get.lazyPut<SearchService>(() => SearchService());
    
    // 注册搜索控制器
    Get.lazyPut<SearchController>(() => SearchController());
  }
} 