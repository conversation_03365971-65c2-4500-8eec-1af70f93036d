import "dart:convert";

import "package:rolio/generated/json/account_server_bind_email_resp_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

@JsonSerializable()
class AccountServerBindEmailRespEntity {
  late int status;
  late int userId;
  late String email;
  late String token;

  AccountServerBindEmailRespEntity();

  factory AccountServerBindEmailRespEntity.fromJson(
          Map<String, dynamic> json) =>
      $AccountServerBindEmailRespEntityFromJson(json);

  Map<String, dynamic> toJson() =>
      $AccountServerBindEmailRespEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
