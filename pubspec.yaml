name: rolio
description: AI chat app.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">3.4.0"
  flutter: ">=3.24.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 添加GetX依赖
  get: ^4.6.6
  # Dio依赖
  dio: ^5.8.0+1
  # 可选：添加dio_cache_interceptor用于缓存
  dio_cache_interceptor: ^3.5.0
  # 安全存储
  flutter_secure_storage: ^9.0.0

  cupertino_icons: ^1.0.8
  page_route_animator: ^1.0.4
  google_fonts: ^6.2.1
  # firebase
  firebase_core: ^3.14.0
  firebase_analytics: ^11.4.0
  firebase_auth: ^5.6.0
  firebase_crashlytics: ^4.3.0
  firebase_messaging: ^15.2.1
  # 登录认证相关
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^6.1.1
  country_picker: ^2.0.27
  image_picker: ^1.1.2
  flutter_contacts: ^1.1.9+2
  equatable: ^2.0.7
  uuid: ^4.5.1
  cached_network_image: ^3.4.1
  emoji_picker_flutter: ^4.3.0
  giphy_picker: ^3.0.2
  permission_handler: ^12.0.0+1
  path_provider: ^2.1.5
  audioplayers: ^6.5.0
  story_view: ^0.16.5
  web_socket_channel: ^3.0.1
  intl: ^0.20.2
  flutter_spinkit: ^5.2.1
  # 持久化缓存依赖
  shared_preferences: ^2.2.2
  connectivity_plus: ^6.1.4
  # 同步锁依赖
  synchronized: ^3.1.0
  flutter_native_splash: ^2.4.6
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
