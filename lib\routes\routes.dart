import 'package:get/get.dart';

/// 路由定义
class Routes {
  // 路由常量定义
  static const String splashScreen = '/splash'; // 添加启动页路由
  static const String homeScreen = '/';
  static const String chatScreen = '/chat';
  static const String recommendScreen = '/role';
  static const String roleDetailScreen = '/role/detail'; // 角色详情页
  static const String favoriteScreen = '/role/favorite'; // 收藏角色页
  static const String searchScreen = '/role/search'; // 搜索角色页
  static const String sessionsScreen = '/sessions';
  static const String loginScreen = '/login'; // 登录页面
  static const String userScreen = '/user'; // 用户页面

  /// 路由参数验证规则
  static Map<String, List<String>> requiredParams = {
    splashScreen: [], // 添加启动页参数规则
    chatScreen: ['aiRoleId'],
    recommendScreen: [],
    roleDetailScreen: ['roleId'], // 角色详情页需要roleId参数
    favoriteScreen: [], // 收藏角色页不需要参数
    searchScreen: [], // 搜索角色页不需要参数
    sessionsScreen: [],
    loginScreen: [],
    userScreen: [],
  };

  /// 路由权限要求
  static Map<String, List<String>> routePermissions = {
    splashScreen: [], // 添加启动页权限规则
    chatScreen: ['auth'],
    recommendScreen: ['auth'],
    roleDetailScreen: ['auth'], // 角色详情页需要auth权限
    favoriteScreen: ['auth'], // 收藏角色页需要auth权限
    searchScreen: ['auth'], // 搜索角色页需要auth权限
    sessionsScreen: ['auth'],
    homeScreen: [],
    loginScreen: [],
    userScreen: [], // 用户页面不需要权限，未登录时显示登录按钮
  };

  /// 验证路由参数
  static bool validateParams(String routeName, Map<String, dynamic>? arguments) {
    if (!requiredParams.containsKey(routeName) || arguments == null) {
      return requiredParams[routeName]?.isEmpty ?? true;
    }

    final required = requiredParams[routeName]!;
    return required.every((param) => arguments.containsKey(param) && arguments[param] != null);
  }

  /// 检查路由权限
  static bool checkPermissions(String routeName, List<String> userPermissions) {
    if (!routePermissions.containsKey(routeName)) {
      return true;
    }

    final required = routePermissions[routeName]!;
    if (required.isEmpty) return true;
    
    return required.every((permission) => userPermissions.contains(permission));
  }
}