import 'package:get/get.dart';
import 'package:rolio/modules/sessions/model/session.dart';
import 'package:rolio/common/models/page_data.dart';
import 'package:rolio/common/models/page_request.dart';

/// 会话提供者接口
///
/// 提供获取聊天会话信息的方法，用于解耦不同模块间的依赖
abstract class ISessionProvider {
  /// 加载状态
  RxBool get isLoading;
  
  /// 会话列表
  List<Session> get sessions;
  
  /// 分页数据
  PageData<Session> get pageData;
  
  /// 获取所有会话信息的流
  Stream<List<dynamic>> getSessions();
  
  /// 刷新会话列表
  /// [refresh] - 是否强制刷新数据（不使用缓存）
  Future<void> refreshSessions({bool refresh = false});
  
  /// 加载会话列表
  /// [page] - 页码
  /// [pageSize] - 每页数量
  Future<void> loadSessions({int page = 1, int pageSize = 10});
  
  /// 加载更多会话
  /// [page] - 页码
  /// [pageSize] - 每页数量
  Future<void> loadMoreSessions({required int page, required int pageSize});
  
  /// 删除会话
  /// [sessionId] - 会话ID
  Future<bool> deleteSession(int sessionId);
  
  /// 置顶会话
  /// [sessionId] - 会话ID
  Future<bool> pinSession(int sessionId);
  
  /// 取消置顶会话
  /// [sessionId] - 会话ID
  Future<bool> unpinSession(int sessionId);
  
    /// 隐藏会话
  /// [sessionId] - 会话ID
  Future<bool> hideSession(int sessionId);
}  