import 'dart:convert';
import 'package:rolio/common/utils/logger.dart';

/// 数据转换器
///
/// 负责处理各种数据转换操作，如JSON解析、对象映射等
class DataConverter {
  /// 构造函数
  DataConverter();
  
  /// 将JSON数据转换为指定类型的对象
  T? fromJson<T>(Map<String, dynamic>? json, T Function(Map<String, dynamic>) fromMap) {
    if (json == null) {
      return null;
    }
    
    try {
      return fromMap(json);
    } catch (e) {
      LogUtil.error('数据转换失败: $e');
      return null;
    }
  }
  
  /// 将JSON数据列表转换为指定类型的对象列表
  List<T> fromJsonList<T>(List<dynamic>? jsonList, T Function(Map<String, dynamic>) fromMap) {
    if (jsonList == null) {
      return [];
    }
    
    try {
      return jsonList
          .map((json) => fromMap(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      LogUtil.error('数据列表转换失败: $e');
      return [];
    }
  }
  
  /// 将对象转换为JSON数据
  Map<String, dynamic>? toJson<T>(T? obj, Map<String, dynamic> Function(T) toMap) {
    if (obj == null) {
      return null;
    }
    
    try {
      return toMap(obj);
    } catch (e) {
      LogUtil.error('对象转换为JSON失败: $e');
      return null;
    }
  }
  
  /// 将对象列表转换为JSON数据列表
  List<Map<String, dynamic>> toJsonList<T>(List<T>? objList, Map<String, dynamic> Function(T) toMap) {
    if (objList == null) {
      return [];
    }
    
    try {
      return objList
          .map((obj) => toMap(obj))
          .toList();
    } catch (e) {
      LogUtil.error('对象列表转换为JSON失败: $e');
      return [];
    }
  }
  
  /// 从JSON字符串解析对象
  /// 
  /// [jsonString] JSON字符串
  /// [fromJson] 从JSON Map创建对象的函数
  static T? parseJson<T>(String? jsonString, T Function(Map<String, dynamic>) fromJson) {
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    
    try {
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return fromJson(jsonMap);
    } catch (e) {
      LogUtil.error('解析JSON失败: $e');
      return null;
    }
  }
  
  /// 从JSON字符串解析对象列表
  /// 
  /// [jsonString] JSON字符串
  /// [fromJson] 从JSON Map创建对象的函数
  static List<T> parseJsonList<T>(String? jsonString, T Function(Map<String, dynamic>) fromJson) {
    if (jsonString == null || jsonString.isEmpty) {
      return [];
    }
    
    try {
      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      LogUtil.error('解析JSON列表失败: $e');
      return [];
    }
  }
  
  /// 将对象转换为JSON字符串
  /// 
  /// [object] 要转换的对象
  /// [toJson] 将对象转换为JSON Map的函数
  static String? toJsonString<T>(T? object, Map<String, dynamic> Function(T) toJson) {
    if (object == null) {
      return null;
    }
    
    try {
      final Map<String, dynamic> jsonMap = toJson(object);
      return json.encode(jsonMap);
    } catch (e) {
      LogUtil.error('转换为JSON字符串失败: $e');
      return null;
    }
  }
  
  /// 将对象列表转换为JSON字符串
  /// 
  /// [objects] 要转换的对象列表
  /// [toJson] 将对象转换为JSON Map的函数
  static String? toJsonStringList<T>(List<T>? objects, Map<String, dynamic> Function(T) toJson) {
    if (objects == null || objects.isEmpty) {
      return null;
    }
    
    try {
      final List<Map<String, dynamic>> jsonList = objects.map((item) => toJson(item)).toList();
      return json.encode(jsonList);
    } catch (e) {
      LogUtil.error('转换对象列表为JSON字符串失败: $e');
      return null;
    }
  }
  
  /// 将Map转换为Model对象
  /// 
  /// [map] 源Map
  /// [fromMap] 从Map创建对象的函数
  static T? mapToModel<T>(Map<String, dynamic>? map, T Function(Map<String, dynamic>) fromMap) {
    if (map == null) {
      return null;
    }
    
    try {
      return fromMap(map);
    } catch (e) {
      LogUtil.error('Map转换为Model失败: $e');
      return null;
    }
  }
  
  /// 将Model对象转换为Map
  /// 
  /// [model] 源Model对象
  /// [toMap] 将对象转换为Map的函数
  static Map<String, dynamic>? modelToMap<T>(T? model, Map<String, dynamic> Function(T) toMap) {
    if (model == null) {
      return null;
    }
    
    try {
      return toMap(model);
    } catch (e) {
      LogUtil.error('Model转换为Map失败: $e');
      return null;
    }
  }
} 