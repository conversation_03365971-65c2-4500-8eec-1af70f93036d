import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:rolio/common/enums/message_type.dart';
import 'package:rolio/common/enums/swipe_direction.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/chat/view/display_message.dart';
import 'package:rolio/widgets/helper_widgets.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/modules/chat/controller/report_controller.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/role_constants.dart';
import 'package:rolio/widgets/context_menu.dart';
import 'package:rolio/modules/chat/controller/chat_controller.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';

class MessageCard extends StatefulWidget {
  const MessageCard({
    Key? key,
    required this.isSender,
    required this.message,
    required this.time,
    required this.messageType,
    required this.onSwipe,
    required this.repliedText,
    required this.username,
    required this.repliedMessageType,
    required this.swipeDirection,
    required this.isSeen,
    required this.avatarUrl,
    this.aiRoleId,
    this.messageStatus,
  }) : super(key: key);

  final bool isSender;
  final String message;
  final String time;
  final MessageType messageType;
  final Function(SwipeDirection) onSwipe;
  final String repliedText;
  final String username;
  final MessageType repliedMessageType;
  final SwipeDirection swipeDirection;
  final bool isSeen;
  final String? avatarUrl;
  final int? aiRoleId;
  final MessageStatus? messageStatus;

  @override
  State<MessageCard> createState() => _MessageCardState();
}

class _MessageCardState extends State<MessageCard> with AutomaticKeepAliveClientMixin {
  // 使用GetX依赖注入的图片预加载器
  ImagePreloader get _imagePreloader => Get.find<ImagePreloader>();

  // 标记头像是否已预加载
  bool _isAvatarPreloaded = false;

  // 消息气泡的全局键，用于获取气泡位置
  final GlobalKey _bubbleKey = GlobalKey();
  
  // 添加一个opacity控制变量
  double _opacity = 0.0;

  // 当前角色ID
  late int _currentRoleId;

  @override
  void initState() {
    super.initState();
    // 预加载头像
    _preloadAvatar();
    
    // 延迟设置透明度，实现淡入效果
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {
          _opacity = 1.0;
        });
      }
    });

    // 获取角色ID
    _currentRoleId = widget.aiRoleId ?? 0;
    if (_currentRoleId <= 0) {
      try {
        // 尝试从ChatController中获取当前角色ID
        final ChatController chatController = Get.find<ChatController>();
        _currentRoleId = chatController.currentAiRoleId;
      } catch (e) {
        LogUtil.error('获取当前角色ID失败: $e');
        _currentRoleId = RoleConstants.DEFAULT_AI_ROLE_ID;
      }
    }
  }

  // 预加载头像
  void _preloadAvatar() {
    if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty) {
      _isAvatarPreloaded = _imagePreloader.isImagePreloaded(widget.avatarUrl!);
      if (!_isAvatarPreloaded) {
        _imagePreloader.preloadImage(
          widget.avatarUrl!,
          width: 36,
          height: 36,
          priority: ImagePreloadPriority.high,
        );
        _isAvatarPreloaded = true;
      }
    }
  }

  // 导航到角色详情页
  void _navigateToRoleDetail() {
    // 在点击时重新获取最新的角色ID，确保使用当前活跃的角色
    try {
      final ChatController chatController = Get.find<ChatController>();
      _currentRoleId = chatController.currentAiRoleId;
      LogUtil.debug('点击时获取最新角色ID: $_currentRoleId');
    } catch (e) {
      LogUtil.error('点击时获取当前角色ID失败: $e');
      // 如果获取失败，尝试使用初始化时的角色ID
      if (_currentRoleId <= 0) {
        _currentRoleId = RoleConstants.DEFAULT_AI_ROLE_ID;
      }
    }

    if (_currentRoleId <= 0) {
      LogUtil.error('无法导航到角色详情页：无效的角色ID $_currentRoleId');
      return;
    }

    LogUtil.info('导航到角色详情页，角色ID: $_currentRoleId');
    RouterManager.navigateTo(
      Routes.roleDetailScreen,
      arguments: {'roleId': _currentRoleId}
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用super.build

    try {
      Size size = MediaQuery.of(context).size;
      return _buildBody(size, context);
    } catch (e) {
      // 出现异常时返回一个错误提示组件
      return Container(
        margin: const EdgeInsets.all(8.0),
        padding: const EdgeInsets.all(12.0),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: const Text('无法显示此消息', style: TextStyle(color: Colors.grey)),
      );
    }
  }

  Widget _buildBody(Size size, BuildContext context) {
    return AnimatedOpacity(
      opacity: _opacity,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Row(
          mainAxisAlignment: widget.isSender ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end, // 改为底部对齐
          children: [
            if (!widget.isSender && widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: GestureDetector(
                  onTap: _navigateToRoleDetail, // 添加点击头像跳转到角色详情页
                  child: _buildAvatar(widget.avatarUrl!, size: 32),
                ),
              ),
            Flexible(
              child: Align(
                alignment: widget.isSender ? Alignment.centerRight : Alignment.centerLeft,
                child: GestureDetector(
                  // 长按显示菜单
                  onLongPress: () => _showBubbleMenu(context),
                  // 添加点击气泡跳转到角色详情页（仅对AI消息有效）
                  onTap: !widget.isSender ? _navigateToRoleDetail : null,
                  child: Container(
                    key: _bubbleKey,
                    constraints: BoxConstraints(
                      maxWidth: size.width * 0.7,
                      minWidth: 0.0,
                    ),
                    decoration: BoxDecoration(
                      color: widget.isSender 
                          ? const Color(0xFF6C6C70) // 用户气泡使用深灰色背景，确保白色字体可读
                          : const Color(0xFF2A2A2A), // AI气泡使用深色背景
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(20.0),
                        topRight: const Radius.circular(20.0),
                        bottomLeft: widget.isSender ? const Radius.circular(20.0) : const Radius.circular(4.0),
                        bottomRight: widget.isSender ? const Radius.circular(4.0) : const Radius.circular(20.0),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 14.0,
                        vertical: 10.0,
                      ),
                      child: _buildMessageContent(context, widget.repliedText.isNotEmpty),
                    ),
                  ),
                ),
              ),
            ),
            if (widget.isSender && widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: _buildAvatar(widget.avatarUrl!, size: 32),
              ),
          ],
        ),
      ),
    );
  }

  // 构建头像，使用缓存优化加载
  Widget _buildAvatar(String url, {double size = 32}) {
    return CircleAvatar(
      radius: size / 2,
      backgroundColor: Colors.transparent, // 修改为透明背景
      child: ClipOval(
        child: CachedNetworkImage(
          imageUrl: url,
          fit: BoxFit.cover,
          width: size,
          height: size,
          placeholder: (context, url) => Container(
            color: Colors.grey[200], // 修改为浅色背景
            width: size,
            height: size,
          ),
          errorWidget: (context, url, error) => Icon(
            Icons.person,
            size: size * 0.6,
            color: Colors.grey[400],
          ),
          // 如果图片已预加载，加速显示
          fadeInDuration: _isAvatarPreloaded
              ? const Duration(milliseconds: 0)
              : const Duration(milliseconds: 300),
          // 添加内存缓存
          memCacheHeight: size.toInt() * 2,
          memCacheWidth: size.toInt() * 2,
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, bool isReplying) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isReplying) ...[
          Text(
            widget.username,
            style: widget.isSender
                ? textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            )
                : textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          addVerticalSpace(8.0),
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: widget.isSender
                  ? Colors.white.withOpacity(0.15) // 更淡的深色
                  : Colors.grey[800],
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: DisplayMessage(
              isSender: widget.isSender,
              message: widget.repliedText,
              messageType: widget.repliedMessageType,
            ),
          ),
        ],
        addVerticalSpace(isReplying ? 8.0 : 0),
        DisplayMessage(
          message: widget.message,
          isSender: widget.isSender,
          messageType: widget.messageType,
        ),
        addVerticalSpace(4.0),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.time,
              style: textTheme.labelSmall?.copyWith(
                fontSize: 10.0,
                color: widget.isSender
                    ? Colors.white.withOpacity(0.7)
                    : Colors.white.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 显示气泡菜单
  void _showBubbleMenu(BuildContext context) {
    // 如果是用户自己发送的消息，不显示举报选项
    if (widget.isSender) return;

    // 使用GlobalKey获取消息气泡的RenderBox
    final RenderBox? bubbleBox = _bubbleKey.currentContext?.findRenderObject() as RenderBox?;
    if (bubbleBox == null) {
      LogUtil.error('无法获取消息气泡的位置');
      return;
    }

    // 获取气泡在屏幕上的位置
    final Offset bubblePosition = bubbleBox.localToGlobal(Offset.zero);
    final Size bubbleSize = bubbleBox.size;

    // 确保有最小宽度，防止短消息导致的问题
    final double effectiveBubbleWidth = bubbleSize.width;

    // 记录日志，帮助调试位置计算
    LogUtil.debug('气泡位置：左=${bubblePosition.dx}, 上=${bubblePosition.dy}, 宽=${bubbleSize.width}, 高=${bubbleSize.height}');

    // 显示菜单
    _showReportMenu(context, bubblePosition, bubbleSize, effectiveBubbleWidth, _currentRoleId);
  }

  // 显示举报菜单
  void _showReportMenu(BuildContext context, Offset bubblePosition, Size bubbleSize, double effectiveBubbleWidth, int aiRoleId) {
    try {
      // 获取屏幕尺寸
      final Size screenSize = MediaQuery.of(context).size;
      final double statusBarHeight = MediaQuery.of(context).padding.top;
      
      // 计算菜单的估计高度（每个菜单项约44高，目前只有一个菜单项）
      const double estimatedMenuHeight = 44.0 + 16.0; // 菜单项高度 + 内边距
      
      // 计算水平位置（居中）
      final double menuX = bubblePosition.dx + (bubbleSize.width / 2);
      
      // 计算上方和下方的可用空间
      final double spaceAbove = bubblePosition.dy - statusBarHeight;
      final double spaceBelow = screenSize.height - (bubblePosition.dy + bubbleSize.height);
      
      // 默认优先显示在上方，只有当上方空间不足且下方空间足够时才显示在下方
      final bool showBelow = spaceAbove < estimatedMenuHeight && spaceBelow >= estimatedMenuHeight;
      
      // 计算垂直位置
      // 当显示在下方时，增加额外的垂直偏移量，以避开消息日期和其他信息
      final double menuY = showBelow 
          ? bubblePosition.dy + bubbleSize.height + 40  // 显示在气泡下方，增加偏移量避开日期
          : bubblePosition.dy - 3;  // 显示在气泡上方
      
      // 记录日志，帮助调试位置计算
      LogUtil.debug('菜单位置：x=$menuX, y=$menuY, 显示在气泡${showBelow ? "下方" : "上方"}, 上方空间=$spaceAbove, 下方空间=$spaceBelow');
      
      // 显示菜单
      ContextMenu.show(
        context: context,
        position: Offset(menuX, menuY),
        bubbleWidth: effectiveBubbleWidth, // 传递气泡宽度以调整菜单宽度
        menuItems: [
          ContextMenuItem(
            icon: Icons.report_outlined,
            text: 'Report',
            iconColor: Colors.white,
            onTap: () => _navigateToReportPage(aiRoleId),
          ),
        ],
        backgroundColor: const Color(0xFF262626),
        borderColor: Colors.transparent,
        borderRadius: 12.0,
        elevation: 8.0,
      );
    } catch (e) {
      LogUtil.error('显示菜单失败: $e');
    }
  }

  // 导航到举报页面
  void _navigateToReportPage(int aiRoleId) {
    try {
      // 记录实际使用的角色ID
      LogUtil.debug('导航到举报页面，使用角色ID: $aiRoleId');

      // 获取举报控制器
      ReportController? reportController;
      try {
        reportController = Get.find<ReportController>();
      } catch (e) {
        // 如果找不到控制器，注册一个新的
        LogUtil.warn('找不到ReportController，尝试注册新实例: $e');
        reportController = Get.put(ReportController());
      }

      // 确保reportController不为空
      if (reportController != null) {
        // 导航到举报页面
        reportController.navigateToReportPage(
          roleId: aiRoleId,
          roleName: widget.username.isNotEmpty ? widget.username : 'AI Character',
        );
      } else {
        LogUtil.error('无法创建ReportController实例');
        ErrorHandler.handleException(
          AppException(
            'Report function unavailable, please try again later',
            code: ErrorCodes.GENERAL_ERROR
          ),
        );
      }
    } catch (e) {
      LogUtil.error('打开举报页面失败: $e');
      ToastUtil.error('Report function unavailable, please try again later');
    }
  }

  @override
  bool get wantKeepAlive => true; // 保持状态，避免重建
}
