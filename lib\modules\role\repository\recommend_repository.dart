import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/interfaces/base_repository.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/constants/http_url_constants.dart';
import 'package:rolio/common/models/page_data.dart';
import 'package:rolio/env.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/cache_constants.dart';

/// 推荐仓库
///
/// 处理推荐数据的业务逻辑，包括缓存管理
class RecommendRepository extends BaseRepository {
  // 缓存管理器
  final CacheManager _cacheManager = Get.find<CacheManager>();
  
  // 缓存预加载状态标志
  bool isCachePreloaded = false;
  
  // 缓存键常量 - 使用统一配置
  static const String _roleListCacheKey = CacheConstants.recommendRoleListCacheKey;
  static const String _roleCacheKeyPrefix = CacheConstants.roleListCachePrefix;
  static const String _recommendCacheKeyPrefix = CacheConstants.recommendCachePrefix;
  
  // 缓存过期时间配置（毫秒）- 使用统一配置
  static const int _listCacheExpiry = CacheConstants.roleListExpiryMs;
  
  // 最大重试次数
  static const int _maxRetryCount = 2;
  
  // API路径 - 使用常量
  final String _apiPath = HttpUrl.recommendPath;
  
  // 构造函数
  RecommendRepository();
  
  // 获取API基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;
  
  // 确保URL格式正确
  String _ensureUrlFormat(String baseUrl, String path) {
    // 确保baseUrl不以斜杠结尾
    String normalizedBaseUrl = baseUrl;
    if (normalizedBaseUrl.endsWith('/')) {
      normalizedBaseUrl = normalizedBaseUrl.substring(0, normalizedBaseUrl.length - 1);
    }
    
    // 确保path以斜杠开头
    String normalizedPath = path;
    if (!normalizedPath.startsWith('/')) {
      normalizedPath = '/$normalizedPath';
    }
    
    final finalUrl = '$normalizedBaseUrl$normalizedPath';
    LogUtil.debug('URL构建: baseUrl=$baseUrl, path=$path, finalUrl=$finalUrl');
    return finalUrl;
  }
  
  // 生成缓存键
  String _generateCacheKey(String prefix, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) {
      return prefix;
    }
    
    // 按键排序，确保相同参数生成相同的缓存键
    final sortedKeys = params.keys.toList()..sort();
    final parts = sortedKeys.map((key) => '$key=${params[key]}').join('_');
    
    return '${prefix}_$parts';
  }
  
  // 转换请求参数，统一参数命名
  Map<String, dynamic> _convertRequestParams(Map<String, dynamic>? params) {
    if (params == null) return {};
    
    final Map<String, dynamic> apiParams = Map.from(params);
    
    // 将page_size转换为size（API要求）
    if (apiParams.containsKey('page_size')) {
      apiParams['size'] = apiParams['page_size'];
      apiParams.remove('page_size');
    }
    
    // 如果有pageSize参数，也转换为size
    if (apiParams.containsKey('pageSize')) {
      apiParams['size'] = apiParams['pageSize'];
      apiParams.remove('pageSize');
    }
    
    LogUtil.debug('转换后的API参数: $apiParams');
    return apiParams;
  }
  
  // 通用方法：处理API响应
  Future<List<AiRole>> _parseApiResponse(dynamic responseData) async {
    try {
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，不是Map类型: ${responseData.runtimeType}');
        return [];
      }
      
      if (!responseData.containsKey('data')) {
        LogUtil.warn('响应数据结构异常，data字段不存在');
        return [];
      }
      
      final dynamic dataField = responseData['data'];
      if (dataField is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，data不是Map类型: ${dataField.runtimeType}');
        return [];
      }
      
      if (!dataField.containsKey('items')) {
        LogUtil.warn('响应数据结构异常，data.items字段不存在');
        return [];
      }
      
      final dynamic items = dataField['items'];
      if (items is! List) {
        LogUtil.warn('items不是列表类型: ${items.runtimeType}');
        return [];
      }
      
      final List<dynamic> rolesData = items;
      LogUtil.debug('成功获取到${rolesData.length}个AI角色数据');
      
      final List<AiRole> roles = [];
      
      // 添加数据验证和缓存
      for (var data in rolesData) {
        try {
          final role = AiRole.fromJson(data);
          
          // 验证必要字段
          if (role.id <= 0 || role.name.isEmpty) {
            LogUtil.warn('角色数据验证失败，跳过: $data');
            continue;
          }
          
          roles.add(role);
        } catch (e) {
          LogUtil.error('解析角色数据失败: $e, 数据: $data');
          // 继续处理下一个角色，不中断整个过程
        }
      }
      
      LogUtil.debug('成功解析到${roles.length}个有效AI角色');
      return roles;
    } catch (e) {
      LogUtil.error('解析角色数据异常: $e');
      return [];
    }
  }
  
  // 通用API请求方法，带重试机制
  Future<List<AiRole>> _fetchRoles(
    String endpoint, {
    Map<String, dynamic>? params,
    String? cacheKeyPrefix,
    int retryCount = 0,
    bool forceRefresh = false
  }) async {
    final cacheKey = cacheKeyPrefix != null 
        ? _generateCacheKey(cacheKeyPrefix, params)
        : null;
    
    // 如果提供了缓存键且不是强制刷新，尝试从缓存获取
    if (cacheKey != null && !forceRefresh) {
      final cachedRoles = await _cacheManager.get<List<AiRole>>(
        cacheKey,
        strategy: CacheStrategy.memoryThenPersistent,
        fromJson: (json) => (json['roles'] as List).map((r) => AiRole.fromJson(r)).toList(),
      );
      
      if (cachedRoles != null) {
        LogUtil.debug('从缓存获取角色列表: $cacheKey, 数量: ${cachedRoles.length}');
        return cachedRoles;
      }
    }
    
    try {
      final url = _ensureUrlFormat(_baseUrl, endpoint);
      LogUtil.debug('获取AI角色，URL: $url');
      
      final apiParams = _convertRequestParams(params);
      LogUtil.debug('请求参数: $apiParams');
      
      final response = await HttpManager.get(
        url: url,
        params: apiParams,
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取AI角色失败，响应为空');
        
        // 如果还有重试次数，进行重试
        if (retryCount < _maxRetryCount) {
          LogUtil.debug('重试获取AI角色，重试次数: ${retryCount + 1}');
          // 使用指数退避策略增加延迟
          await Future.delayed(Duration(milliseconds: 300 * (retryCount + 1)));
          return _fetchRoles(endpoint, params: params, cacheKeyPrefix: cacheKeyPrefix, retryCount: retryCount + 1, forceRefresh: forceRefresh);
        }
        
        return [];
      }
      
      final roles = await _parseApiResponse(response.rawData);
      
      // 如果提供了缓存键且获取到了角色，写入缓存
      if (cacheKey != null && roles.isNotEmpty) {
        await _cacheManager.set(
          cacheKey,
          {'roles': roles.map((r) => r.toJson()).toList()},
          strategy: CacheStrategy.both,
          expiry: _listCacheExpiry,
        );
      }
      
      return roles;
    } catch (e) {
      LogUtil.error('获取AI角色列表失败: $e');
      
      // 如果还有重试次数，进行重试
      if (retryCount < _maxRetryCount) {
        LogUtil.debug('重试获取AI角色，重试次数: ${retryCount + 1}');
        // 使用指数退避策略增加延迟
        await Future.delayed(Duration(milliseconds: 300 * (retryCount + 1)));
        return _fetchRoles(endpoint, params: params, cacheKeyPrefix: cacheKeyPrefix, retryCount: retryCount + 1, forceRefresh: forceRefresh);
      }
      
      // 处理错误
      ErrorHandler.handleException(
        AppException(
          '加载AI角色列表失败',
          code: ErrorCodes.ROLE_LIST_LOAD_FAILED
        )
      );
      return [];
    }
  }
  
  // 获取角色详情（基本信息，不包含收藏状态）
  Future<AiRole?> getById(int id) async {
    final cacheKey = '$_roleCacheKeyPrefix$id';
    
    try {
      // 尝试从缓存获取
      final cachedRole = await _cacheManager.get<AiRole>(
        cacheKey,
        strategy: CacheStrategy.memoryThenPersistent,
        fromJson: (json) => AiRole.fromJson(json),
      );
      
      if (cachedRole != null) {
        LogUtil.debug('从缓存获取AI角色基本信息，ID: $id');
        return cachedRole;
      }
      
      return null;
      
    } catch (e) {
      LogUtil.error('获取AI角色基本信息失败: $e');
      ErrorHandler.handleException(
        AppException(
          '加载AI角色基本信息失败',
          code: ErrorCodes.ROLE_INFO_LOAD_FAILED,
          originalError: e
        )
      );
      return null;
    }
  }
  
  // 获取角色列表
  Future<List<AiRole>> getList({Map<String, dynamic>? params, bool forceRefresh = false}) async {
    return _fetchRoles(
      _apiPath,
      params: params,
      cacheKeyPrefix: _roleListCacheKey,
      forceRefresh: forceRefresh
    );
  }
  
  // 获取推荐AI角色列表
  Future<List<AiRole>> getRecommendRoles({
    int page = 1,
    int pageSize = StringsConsts.recommendPageSize
  }) async {
    return _fetchRoles(
      '$_apiPath${HttpUrl.recommendPath}',
      params: {
        'page': page,
        'pageSize': pageSize,
        'tag': StringsConsts.recommendDefaultTag
      },
      cacheKeyPrefix: '$_recommendCacheKeyPrefix${StringsConsts.recommendDefaultTag}',
    );
  }
  
  // 清除所有缓存
  Future<void> clearCache() async {
    try {
      // 清除角色列表缓存
      await _cacheManager.remove(_roleListCacheKey, strategy: CacheStrategy.both);
      
      // 清除推荐角色缓存（基础缓存）
      await _cacheManager.remove('$_recommendCacheKeyPrefix${StringsConsts.recommendDefaultTag}', 
          strategy: CacheStrategy.both);
      
      // 清除分页缓存 - 清除前5页的缓存，确保分页数据被正确清除
      for (int page = 1; page <= 5; page++) {
        final Map<String, dynamic> params = {
          'page': page,
          'size': StringsConsts.recommendPageSize,
          'tag': StringsConsts.recommendDefaultTag
        };
        final cacheKey = _generateCacheKey(_recommendCacheKeyPrefix, params);
        await _cacheManager.remove(cacheKey, strategy: CacheStrategy.both);
      }

      
      // 搜索和分类缓存由于键值多样，较难全部清除，但会自动过期
      
      LogUtil.debug('已清除主要推荐角色缓存，包括分页缓存');
    } catch (e) {
      LogUtil.error('清除推荐角色缓存失败: $e');
    }
  }

  /// 获取角色列表（分页）
  /// 
  /// [params] 请求参数，包含page、size等
  /// [forceRefresh] 是否强制刷新缓存
  /// 返回分页结果
  Future<PageData<AiRole>> getPagedList({
    Map<String, dynamic>? params,
    bool forceRefresh = false
  }) async {
    try {
      final apiParams = _convertRequestParams(params);
      final cacheKey = _generateCacheKey(_recommendCacheKeyPrefix, apiParams);
      
      // 如果不是强制刷新，尝试从缓存获取
      if (!forceRefresh) {
        final cachedData = await _cacheManager.get<Map<String, dynamic>>(
          cacheKey,
          strategy: CacheStrategy.memoryThenPersistent,
        );
        
        if (cachedData != null) {
          try {
            // 手动反序列化
            final page = cachedData['page'] as int? ?? 1;
            final size = cachedData['size'] as int? ?? 10;
            final total = cachedData['total'] as int? ?? 0;
            
            // 解析角色列表
            final itemsJson = cachedData['items'] as List<dynamic>? ?? [];
            final items = itemsJson
                .map((item) => item is Map<String, dynamic> ? AiRole.fromJson(item) : null)
                .where((item) => item != null)
                .cast<AiRole>()
                .toList();
                
            final result = PageData<AiRole>(
              page: page,
              size: size,
              total: total,
              items: items,
            );
            
            LogUtil.debug('从缓存获取分页角色列表: $cacheKey, 数量: ${result.items.length}, 总数: ${result.total}');
            return result;
          } catch (e) {
            LogUtil.error('解析缓存的角色列表失败: $e');
            // 缓存解析失败，继续从服务器获取
          }
        }
      }
      
      // 构建API URL
      final url = _ensureUrlFormat(_baseUrl, _apiPath);
      LogUtil.debug('获取分页AI角色，URL: $url, 参数: $apiParams');
      
      // 发送请求
      final response = await HttpManager.get(
        url: url,
        params: apiParams,
      );
      
      if (response == null || response.rawData == null) {
        LogUtil.warn('获取分页AI角色失败，响应为空');
        return PageData<AiRole>(items: [], total: 0, page: 1, size: StringsConsts.recommendPageSize);
      }
      
      // 解析响应数据
      final responseData = response.rawData;
      if (responseData is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，不是Map类型');
        return PageData<AiRole>(items: [], total: 0, page: 1, size: StringsConsts.recommendPageSize);
      }
      
      if (!responseData.containsKey('data')) {
        LogUtil.warn('响应数据结构异常，data字段不存在');
        return PageData<AiRole>(items: [], total: 0, page: 1, size: StringsConsts.recommendPageSize);
      }
      
      final dynamic dataField = responseData['data'];
      if (dataField is! Map<String, dynamic>) {
        LogUtil.warn('响应数据结构异常，data不是Map类型');
        return PageData<AiRole>(items: [], total: 0, page: 1, size: StringsConsts.recommendPageSize);
      }
      
      // 获取总数
      final total = dataField['total'] is int ? dataField['total'] : 0;
      
      // 获取角色列表
      final List<AiRole> roles = [];
      if (dataField.containsKey('items') && dataField['items'] is List) {
        final List<dynamic> rolesData = dataField['items'];
        
        for (var data in rolesData) {
          try {
            final role = AiRole.fromJson(data);
            
            // 验证必要字段
            if (role.id <= 0 || role.name.isEmpty) {
              LogUtil.warn('角色数据验证失败，跳过: $data');
              continue;
            }
            
            roles.add(role);
          } catch (e) {
            LogUtil.error('解析角色数据失败: $e, 数据: $data');
          }
        }
      }
      
      // 创建分页结果
      final result = PageData<AiRole>(
        items: roles,
        total: total,
        page: params?['page'] ?? 1,
        size: params?['size'] ?? StringsConsts.recommendPageSize,
      );
      
      // 缓存分页结果 - 手动序列化
      final Map<String, dynamic> serializedData = {
        'page': result.page,
        'size': result.size,
        'total': result.total,
        'items': result.items.map((role) => role.toJson()).toList(),
      };
      
      await _cacheManager.set(
        cacheKey,
        serializedData,
        strategy: CacheStrategy.both,
        expiry: _listCacheExpiry,
      );
      
      LogUtil.debug('成功获取分页角色列表: ${roles.length}个角色，总数: $total');
      return result;
    } catch (e) {
      LogUtil.error('获取分页角色列表失败: $e');
      throw ErrorHandler.createAppException(e, 'get role list failed');
    }
  }
} 