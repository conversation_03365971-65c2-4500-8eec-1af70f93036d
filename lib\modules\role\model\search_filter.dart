/// 搜索过滤器
/// 用于搜索AI角色的过滤条件
class SearchFilter {
  /// 搜索关键词
  final String keyword;
  
  /// 标签过滤
  final List<String>? tags;
  
  /// 分类过滤
  final String? category;
  
  /// 页码
  final int page;
  
  /// 每页大小
  final int pageSize;
  
  /// 构造函数
  SearchFilter({
    required this.keyword,
    this.tags,
    this.category,
    this.page = 1,
    this.pageSize = 20,
  });
  
  /// 转换为查询参数
  Map<String, String> toQueryParams() {
    final params = <String, String>{
      'keyword': keyword,
      'page': page.toString(),
      'pageSize': pageSize.toString(),
    };
    
    if (tags != null && tags!.isNotEmpty) {
      params['tags'] = tags!.join(',');
    }
    
    if (category != null && category!.isNotEmpty) {
      params['category'] = category!;
    }
    
    return params;
  }
  
  /// 创建副本
  SearchFilter copyWith({
    String? keyword,
    List<String>? tags,
    String? category,
    int? page,
    int? pageSize,
  }) {
    return SearchFilter(
      keyword: keyword ?? this.keyword,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
    );
  }
} 