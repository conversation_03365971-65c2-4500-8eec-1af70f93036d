import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';

/// 骨架网格视图组件
/// 
/// 用于网格列表的加载状态显示
class SkeletonGridView extends StatelessWidget {
  /// 网格项数量
  final int itemCount;
  
  /// 交叉轴数量（列数）
  final int crossAxisCount;
  
  /// 子项宽高比
  final double childAspectRatio;
  
  /// 交叉轴间距
  final double crossAxisSpacing;
  
  /// 主轴间距
  final double mainAxisSpacing;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 是否可滚动
  final bool scrollable;
  
  /// 自定义骨架项构建器
  final Widget Function(BuildContext, int)? itemBuilder;

  /// 构造函数
  const SkeletonGridView({
    Key? key,
    this.itemCount = 8,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 16,
    this.mainAxisSpacing = 16,
    this.padding = const EdgeInsets.all(16),
    this.scrollable = true,
    this.itemBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding,
      physics: scrollable 
          ? const AlwaysScrollableScrollPhysics() 
          : const NeverScrollableScrollPhysics(),
      shrinkWrap: !scrollable,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder ?? _defaultItemBuilder,
    );
  }
  
  /// 默认骨架项构建器
  Widget _defaultItemBuilder(BuildContext context, int index) {
    return ShimmerWidget(
      child: SkeletonContainer(
        borderRadius: 8,
      ),
    );
  }
} 