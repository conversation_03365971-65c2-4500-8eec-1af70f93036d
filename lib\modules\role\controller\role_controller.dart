import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'dart:async';  // 导入用于StreamSubscription

/// 角色控制器
/// 负责管理角色详情页的UI状态和业务逻辑
class RoleController extends GetxController {
  // 服务
  late final RoleService roleService;

  // Workers列表 - 用于错误重试
  final List<Worker> _workers = [];

  // Computed属性 - 通过RoleService的StateManager获取状态
  bool get isLoading => roleService.stateManager.isLoading;
  bool get isFavoriteLoading => roleService.stateManager.isFavoriteLoading;
  bool get hasError => roleService.stateManager.hasError;
  bool get isCoverPreloaded => roleService.stateManager.isCoverPreloaded;
  AiRole? get currentRole => roleService.stateManager.currentRole.value;
  String get errorMessage => roleService.stateManager.errorMessage.value;
  
  @override
  void onInit() {
    super.onInit();

    // 获取服务实例
    roleService = Get.find<RoleService>();

    // 设置GetX Workers - 只保留错误重试逻辑
    _setupWorkers();

    // 获取路由参数
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final roleId = arguments['roleId'];
      if (roleId != null && roleId is int) {
        // 获取角色详情（包含收藏信息）
        getRoleDetail(roleId);
      } else {
        LogUtil.error('角色ID无效: $roleId');
        roleService.stateManager.setError('无效的角色ID');
      }
    } else {
      LogUtil.error('未提供角色ID参数');
      roleService.stateManager.setError('未提供角色ID');
    }
  }

  /// 设置GetX Workers管理状态依赖
  void _setupWorkers() {
    // 防抖处理重试逻辑 - 监听页面状态变化
    final retryWorker = debounce(
      roleService.stateManager.pageState,
      (state) async {
        if (state == RolePageState.error) {
          // 使用异步检查，包含网络状态检测
          final canRetry = await roleService.stateManager.canRetryAsync();
          if (canRetry) {
            _handleAutoRetry();
          } else {
            LogUtil.debug('RoleController: 无法自动重试，已达到最大重试次数或网络不可用');
          }
        }
      },
      time: const Duration(seconds: 2),
    );
    _workers.add(retryWorker);
  }
  
  /// 打开搜索页面
  void goToSearch() {
    LogUtil.debug('打开搜索页面');
    Get.toNamed('/role/search');
  }

  /// 获取角色详情
  ///
  /// 通过RoleService获取角色详细信息
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<void> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    LogUtil.info('获取角色详情: ID=$roleId, forceRefresh=$forceRefresh');
    
    // 直接调用RoleService的方法，状态管理已经在Service中处理
    final role = await roleService.getRoleDetail(roleId, forceRefresh: forceRefresh);
    
    if (role != null) {
      LogUtil.debug('成功获取角色详情: ${role.name}, 收藏状态: ${role.isFavorited}');
    } else {
      LogUtil.error('未找到角色: ID=$roleId');
    }
  }

  /// 处理自动重试 - 由Worker自动调用
  void _handleAutoRetry() {
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final roleId = arguments['roleId'];
      if (roleId != null && roleId is int) {
        LogUtil.info('自动重试获取角色详情: ID=$roleId');
        roleService.retryGetRoleDetail(roleId);
      }
    }
  }
  
  /// 重试获取角色详情
  Future<void> retryGetRoleDetail() async {
    final role = currentRole;
    if (role != null) {
      LogUtil.info('手动重试获取角色详情: ID=${role.id}');
      await roleService.retryGetRoleDetail(role.id);
    } else {
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final roleId = arguments['roleId'];
        if (roleId != null && roleId is int) {
          LogUtil.info('从路由参数手动重试获取角色详情: ID=$roleId');
          await roleService.retryGetRoleDetail(roleId);
        } else {
          LogUtil.error('无法重试，当前没有角色ID');
          ToastUtil.error('Cannot retry, no role ID available');
        }
      } else {
        LogUtil.error('无法重试，当前没有角色ID');
        ToastUtil.error('Cannot retry, no role ID available');
      }
    }
  }

  /// 切换收藏状态
  ///
  /// 如果角色已收藏则取消收藏，否则收藏角色
  Future<void> toggleFavorite() async {
    try {
      final role = currentRole;
      if (role == null) {
        LogUtil.warn('当前没有角色，无法切换收藏状态');
        return;
      }

      LogUtil.debug('切换收藏状态，当前状态: ${role.isFavorited}, 角色ID: ${role.id}');

      // 调用服务层统一方法切换收藏状态
      final newState = await roleService.toggleFavorite(role.id);

      if (newState == null) {
        // 操作失败，使用统一错误处理
        ErrorHandler.handleException(
          AppException(
            'Failed to change favorite status',
            code: ErrorCodes.FAVORITE_FAILED
          ),
        );
      }
    } catch (e) {
      LogUtil.error('切换收藏状态异常: $e');
      // 使用统一错误处理，不重复显示错误
      ErrorHandler.handleException(
        AppException(
          'An error occurred while updating favorite status',
          code: ErrorCodes.FAVORITE_FAILED,
          originalError: e
        ),
      );
    }
  }

  @override
  void onClose() {
    // 清理所有Workers
    for (final worker in _workers) {
      worker.dispose();
    }
    _workers.clear();

    LogUtil.debug('RoleController: 已释放资源');
    super.onClose();
  }
}
