import "dart:io";
import 'package:rolio/manager/global_state.dart';
import "package:rolio/common/utils/logger.dart";
import "package:firebase_analytics/firebase_analytics.dart";
import "package:get/get.dart";

class FirebaseAnalyticsService {
  static final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  static final String _baseParameterInfo = 'info';
  static final String _baseParameterTime = 'local_time';
  static final String _extParameter = 'ext';

  static void logEvent(String eventName, dynamic parameters) {
    if (Platform.isAndroid || Platform.isIOS) {
      try {
        analytics.logEvent(name: eventName, parameters: {
          _baseParameterInfo: Get.find<GlobalState>().currentUser.value?.uid ?? '未登录',
          _baseParameterTime: DateTime.now().millisecondsSinceEpoch,
          _extParameter: parameters.toString(),
        });
      } catch (e) {
        LogUtil.error(e.toString());
      }
    }
  }
}

class FirebaseAnalyticsEvent {
  static const String verification_code_acq = "verification_code_acq";
  static const String login_success = "login_success";
  static const String profile_page = "profile_page";
  static const String login_failed = "login_failed";
  static const String report_msg = "report_msg";
}
