import 'package:flutter/material.dart';
import 'package:rolio/common/constants/colors_constants.dart';

/// 系统消息卡片组件
/// 
/// 用于显示系统消息，如连接状态、角色简介等
class SystemMessageCard extends StatefulWidget {
  /// 消息内容
  final String message;
  
  /// 是否为角色简介
  final bool isIntro;

  const SystemMessageCard({
    Key? key,
    required this.message,
    this.isIntro = false,
  }) : super(key: key);

  @override
  State<SystemMessageCard> createState() => _SystemMessageCardState();
}

class _SystemMessageCardState extends State<SystemMessageCard> {
  // 透明度控制
  double _opacity = 0.0;

  @override
  void initState() {
    super.initState();
    
    // 延迟设置透明度，实现淡入效果
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {
          _opacity = 1.0;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _opacity,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        decoration: BoxDecoration(
          color: widget.isIntro 
              ? Colors.black.withOpacity(0.6) // 简介使用较深的背景
              : Colors.black.withOpacity(0.4), // 普通系统消息使用较浅的背景
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color: widget.isIntro 
                ? Colors.grey.withOpacity(0.3) // 简介使用较明显的边框
                : Colors.grey.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: Text(
          widget.message,
          style: TextStyle(
            color: widget.isIntro 
                ? Colors.white.withOpacity(0.95) // 简介文字较亮
                : Colors.white.withOpacity(0.8), // 系统消息文字稍暗
            fontSize: widget.isIntro ? 14.0 : 12.0,
            fontWeight: widget.isIntro ? FontWeight.w400 : FontWeight.w300,
          ),
          textAlign: TextAlign.left , // 简介左对齐，系统消息居中对齐
        ),
      ),
    );
  }
} 