import 'package:rolio/generated/json/base/json_convert_content.dart';
import "package:rolio/modules/login/model/account_server_parse_token_resp_entity.dart";

AccountServerParseTokenRespEntity $AccountServerParseTokenRespEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerParseTokenRespEntity accountServerParseTokenRespEntity =
      AccountServerParseTokenRespEntity();
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    accountServerParseTokenRespEntity.userId = userId;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    accountServerParseTokenRespEntity.email = email;
  }
  return accountServerParseTokenRespEntity;
}

Map<String, dynamic> $AccountServerParseTokenRespEntityToJson(
    AccountServerParseTokenRespEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['email'] = entity.email;
  return data;
}

extension AccountServerParseTokenRespEntityExtension
    on AccountServerParseTokenRespEntity {
  AccountServerParseTokenRespEntity copyWith({
    int? userId,
    String? email,
  }) {
    return AccountServerParseTokenRespEntity()
      ..userId = userId ?? this.userId
      ..email = email ?? this.email;
  }
}
