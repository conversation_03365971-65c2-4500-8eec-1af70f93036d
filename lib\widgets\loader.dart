import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/manager/global_state.dart';

/// 加载覆盖层
///
/// 在需要阻止用户交互时显示简单的加载指示器
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final RxBool isLoading;
  
  const LoadingOverlay({
    Key? key,
    required this.child,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 主要内容
        child,
        
        // 加载指示器覆盖层
        Obx(() {
          if (!isLoading.value) {
            return const SizedBox.shrink();
          }
          
          return Container(
            color: Colors.black.withOpacity(0.5),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          );
        }),
      ],
    );
  }
}

/// 认证加载覆盖层
///
/// 专门用于认证流程的加载覆盖层
class AuthLoadingOverlay extends StatelessWidget {
  final Widget child;
  
  const AuthLoadingOverlay({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final globalState = Get.find<GlobalState>();
    
    return LoadingOverlay(
      child: child,
      isLoading: globalState.isAuthProcessing,
    );
  }
} 