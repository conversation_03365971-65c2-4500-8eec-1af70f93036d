import 'package:flutter/material.dart';

class AppColors {
  // App main colors
  static const primary = Color(0xFF9C9C9C);
  static const onPrimary = Colors.white;
  static const secondary = Color(0xFFFB7F6B);
  static const onSecondary = Color(0xFFFFFFFF);
  static const onError = Color(0xFFFF0000);

  // Scaffold colors
  static const scaffoldBG = Colors.white;
  static const scaffoldBGChat = Color(0xFF121212); // 修改为深色背景

  // AppBar
  static const appBar = Color(0xFF00FFDD);
  static const appBarTitle = Colors.white;
  static const appBarActionIcon = Colors.white;
  static const chatAppBar = Colors.black; // 修改为黑色

  // TabBar
  static const tabIndicator = Colors.white;
  static const sTabLabel = Colors.white;
  static const uTabLabel = Colors.white70;

  /// General
  static const white = Colors.white;
  static const grey = Colors.grey;
  static const black = Colors.black;
  static const lightBlack = Color(0xFF1E1E1E); // 略微亮一点的黑色

  // Chat Screen
  static const chatTFFill = Color(0xFF2C2C2C); // 修改为深色输入框背景
  static const chatScreenGrey = Color(0xFFACACAC);
  static const chatOffWhite = Colors.white70;

  // AI主题颜色
  static const aiPrimary = Color(0xFF3D7EFF); // AI主色调
  static const aiSecondary = Color(0xFF05C2DE); // AI辅助色
  static const aiBackground = Color(0xFF0F0F0F); // AI背景色

  // 标签相关颜色
  static const tagBackground = Color(0xFF2C2C2C); // 标签背景颜色
  static const tagText = Color(0xFFACACAC); // 标签文字颜色

  static const green = Colors.green;
  static const red = Colors.red;
}
