import "dart:convert";

import "package:rolio/generated/json/account_server_http_header_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

export 'package:rolio/generated/json/account_server_http_header_entity.g.dart';

@JsonSerializable()
class AccountServerHttpHeaderEntity {
  late String gaid;
  late String uid;
  late int cvc;
  late double svc;
  late String device;
  late String network;
  late String simcode;
  late String lang;
  late String ls;
  late String pf;
  late String ip;
  late String country;
  late int appid;

  AccountServerHttpHeaderEntity()
      : gaid = '',
        uid = '',
        cvc = 0,
        svc = 0.0,
        device = '',
        network = '',
        simcode = '',
        lang = '',
        ls = '',
        pf = '',
        ip = '',
        country = '',
        appid = 0;

  factory AccountServerHttpHeaderEntity.fromJson(Map<String, dynamic> json) =>
      $AccountServerHttpHeaderEntityFromJson(json);

  Map<String, dynamic> toJson() => $AccountServerHttpHeaderEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
