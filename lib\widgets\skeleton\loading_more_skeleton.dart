import 'package:flutter/material.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/widgets/skeleton/role_card_skeleton.dart';

/// 加载更多骨架屏组件
///
/// 用于显示"加载更多"时的一行骨架卡片，支持网格和列表两种布局
class LoadingMoreSkeleton extends StatelessWidget {
  /// 卡片类型：网格或列表
  final RoleCardSkeletonType type;
  
  /// 显示的列数/项目数
  final int? itemCount;
  
  /// 子项宽高比（仅网格布局有效）
  final double childAspectRatio;
  
  /// 交叉轴间距（仅网格布局有效）
  final double crossAxisSpacing;
  
  /// 主轴间距（仅网格布局有效）
  final double mainAxisSpacing;
  
  /// 列表项间距（仅列表布局有效）
  final double listItemSpacing;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 动画持续时间
  final Duration animationDuration;
  
  /// 构造函数
  const LoadingMoreSkeleton({
    Key? key,
    this.type = RoleCardSkeletonType.grid,
    this.itemCount,
    this.childAspectRatio = StringsConsts.recommendCardAspectRatio,
    this.crossAxisSpacing = StringsConsts.recommendGridSpacing,
    this.mainAxisSpacing = StringsConsts.recommendGridSpacing,
    this.listItemSpacing = 12.0,
    this.padding = const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
    this.animationDuration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Column(
        children: [
          // 使用AnimatedOpacity平滑显示骨架屏
          AnimatedOpacity(
            opacity: 1.0,
            duration: animationDuration,
            child: type == RoleCardSkeletonType.grid
                ? _buildGridSkeleton(context)
                : _buildListSkeleton(context),
          ),
        ],
      ),
    );
  }
  
  /// 构建网格布局的骨架屏
  Widget _buildGridSkeleton(BuildContext context) {
    // 获取当前网格的列数
    final width = MediaQuery.of(context).size.width;
    final crossAxisCount = itemCount ?? _calculateOptimalColumnCount(width);
    
    return SizedBox(
      // 根据卡片比例计算合适的高度
      height: width / crossAxisCount / childAspectRatio,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: crossAxisSpacing,
          mainAxisSpacing: mainAxisSpacing,
        ),
        itemCount: crossAxisCount, // 显示一行骨架屏
        itemBuilder: (context, index) => const RoleCardSkeleton(
          type: RoleCardSkeletonType.grid,
        ),
      ),
    );
  }
  
  /// 构建列表布局的骨架屏
  Widget _buildListSkeleton(BuildContext context) {
    return Column(
      children: List.generate(
        itemCount ?? 1,
        (index) => Padding(
          padding: EdgeInsets.only(bottom: index < (itemCount ?? 1) - 1 ? listItemSpacing : 0),
          child: const RoleCardSkeleton(
            type: RoleCardSkeletonType.list,
          ),
        ),
      ),
    );
  }
  
  /// 根据屏幕宽度计算最佳列数
  int _calculateOptimalColumnCount(double width) {
    if (width < 600) {
      return 2; // 手机屏幕
    } else if (width < 900) {
      return 3; // 小平板
    } else if (width < 1200) {
      return 4; // 大平板
    } else {
      return 5; // 桌面
    }
  }
} 