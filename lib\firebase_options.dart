// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBr18QiSULNBgjfEAkgp-HSaWqC8vS3hKg',
    appId: '1:16749514559:android:7be0e42f0827d90364cd8a',
    messagingSenderId: '16749514559',
    projectId: 'rolio-1bdad',
    storageBucket: 'rolio-1bdad.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCz85PjLqA3HEHqQVoDsu4eipenQ_ekcGA',
    appId: '1:16749514559:ios:9c10e8bc5e2457f764cd8a',
    messagingSenderId: '16749514559',
    projectId: 'rolio-1bdad',
    storageBucket: 'rolio-1bdad.firebasestorage.app',
    androidClientId: '16749514559-5ij2761vavtqqqgh032r7ori1lb0bnek.apps.googleusercontent.com',
    iosClientId: '16749514559-afei06ae90795nb890riojeoqticvr4n.apps.googleusercontent.com',
    iosBundleId: 'com.i89trillion.ai.rolio',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCpcDkIdv0Ekw-yffOb6vegyKucHK-krzA',
    appId: '1:16749514559:web:147a8024f6bd49b064cd8a',
    messagingSenderId: '16749514559',
    projectId: 'rolio-1bdad',
    authDomain: 'rolio-1bdad.firebaseapp.com',
    storageBucket: 'rolio-1bdad.firebasestorage.app',
    measurementId: 'G-SCJCPB82M4',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCz85PjLqA3HEHqQVoDsu4eipenQ_ekcGA',
    appId: '1:16749514559:ios:9c10e8bc5e2457f764cd8a',
    messagingSenderId: '16749514559',
    projectId: 'rolio-1bdad',
    storageBucket: 'rolio-1bdad.firebasestorage.app',
    androidClientId: '16749514559-5ij2761vavtqqqgh032r7ori1lb0bnek.apps.googleusercontent.com',
    iosClientId: '16749514559-afei06ae90795nb890riojeoqticvr4n.apps.googleusercontent.com',
    iosBundleId: 'com.i89trillion.ai.rolio',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCpcDkIdv0Ekw-yffOb6vegyKucHK-krzA',
    appId: '1:16749514559:web:0ec9843c2cfd308264cd8a',
    messagingSenderId: '16749514559',
    projectId: 'rolio-1bdad',
    authDomain: 'rolio-1bdad.firebaseapp.com',
    storageBucket: 'rolio-1bdad.firebasestorage.app',
    measurementId: 'G-1FTF9VBBH8',
  );

}