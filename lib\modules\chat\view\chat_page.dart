import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/constants/role_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/toast_util.dart';
import '../controller/chat_controller.dart';
import '../controller/report_controller.dart';
import '../utils/websocket_error_handler.dart'; // 添加WebSocket错误处理器
import 'messages_list.dart';
import 'bottom_chat_text_field.dart';
import 'package:rolio/widgets/cover_image.dart';
import 'chat_status_widgets.dart';
import 'package:rolio/manager/ws_manager.dart'; // 导入WsManager
import '../service/chat_service.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  // WebSocket错误事件监听器
  Worker? _wsErrorSubscription;
  // 获取ChatController实例
  late final ChatController controller;
  // 获取WsManager实例用于重连
  late final WsManager _wsManager;
  // 获取ChatService实例
  late final ChatService _chatService;
  
  // 消息加载超时状态 - 使用GetX响应式变量
  final RxBool _isLoadingTimeoutActive = false.obs;
  // 消息加载超时时间（秒）
  static const int _messageLoadingTimeout = 20;
  
  // 预先创建背景图Widget以避免重复构建
  Widget? _backgroundImageWidget;
  String? _lastCoverUrl;

  @override
  void initState() {
    super.initState();
    controller = Get.find<ChatController>();
    _wsManager = Get.find<WsManager>();
    _chatService = Get.find<ChatService>();
    
    // 设置WebSocket错误监听
    _setupWebSocketErrorListener();
    
    // 设置消息加载超时检测
    _setupMessageLoadingTimeout();
  }
  
  // 设置消息加载超时检测
  void _setupMessageLoadingTimeout() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 监听消息加载状态
      ever(controller.isLoadingMessages, (bool isLoading) {
        // 取消之前的超时状态
        _isLoadingTimeoutActive.value = false;

        // 如果正在加载消息，启动超时检测
        if (isLoading) {
          _isLoadingTimeoutActive.value = true;
          Future.delayed(Duration(seconds: _messageLoadingTimeout), () {
            // 如果超时状态仍然活跃且仍在加载消息，显示超时错误
            if (_isLoadingTimeoutActive.value && controller.isLoadingMessages.value) {
              controller.setMessageLoadingError(true, 'Messages loading timeout');
            }
          });
        }
      });
    });
  }
  
  // 设置WebSocket错误监听
  void _setupWebSocketErrorListener() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _wsErrorSubscription = WebSocketErrorHandler.setupWebSocketErrorListener(
        context,
        onError: (ctx, error) {
          // 处理WebSocket错误
          WebSocketErrorHandler.handleWebSocketError(
            ctx,
            onRetry: () {
              // 重试连接WebSocket
              _reconnectWebSocket();
            },
            error: error,
            errorMessage: 'Connection lost. Please check your network',
          );
        },
      );
    });
  }
  
  // 重连WebSocket
  void _reconnectWebSocket() async {
    try {
      LogUtil.info('正在尝试重新连接WebSocket...');
      final success = await _wsManager.reconnect();
      if (success) {
        LogUtil.info('WebSocket重连成功');
        // 可以在这里执行重新加载数据等操作
        Get.back(); // 关闭错误对话框
      } else {
        LogUtil.error('WebSocket重连失败');
      }
    } catch (e) {
      LogUtil.error('WebSocket重连过程中发生异常: $e');
    }
  }
  
  // 重新加载消息
  void _retryLoadMessages() {
    try {
      LogUtil.info('正在尝试重新加载消息...');
      // 重置错误状态
      controller.setMessageLoadingError(false, '');
      // 重新加载消息
      controller.loadMessages();
    } catch (e) {
      LogUtil.error('重新加载消息失败: $e');
      controller.setMessageLoadingError(true, 'Failed to reload messages');
    }
  }

  @override
  void dispose() {
    // 移除WebSocket错误监听
    WebSocketErrorHandler.removeWebSocketErrorListener(_wsErrorSubscription);
    // 取消消息加载超时状态
    _isLoadingTimeoutActive.value = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 添加空值检查，如果参数为空则使用默认的空Map
    final Map<String, dynamic> userData = Get.arguments as Map<String, dynamic>? ?? {};
    
    return Scaffold(
      extendBodyBehindAppBar: true, // 将内容扩展到AppBar下方
      appBar: _buildAppBar(context, userData),
      body: _buildBody(context, userData),
      backgroundColor: AppColors.black,
    );
  }

  // 构建背景图片，只在URL变化时重新构建
  Widget _buildBackgroundImage(String? coverUrl) {
    // 如果URL为空，返回空容器
    if (coverUrl == null || coverUrl.isEmpty) {
      return Container(color: Colors.black);
    }
    
    // 如果URL与上次相同且已有缓存的背景，直接返回缓存的Widget
    if (coverUrl == _lastCoverUrl && _backgroundImageWidget != null) {
      return _backgroundImageWidget!;
    }
    
    // 获取屏幕尺寸
    final size = MediaQuery.of(context).size;
    
    // 否则创建新的背景图片Widget并缓存
    _lastCoverUrl = coverUrl;
    _backgroundImageWidget = Container(
      width: size.width,
      height: size.height,
      alignment: Alignment.center,
      child: IgnorePointer(
        child: ChatCoverImage(
          imageUrl: coverUrl,
          width: size.width,
          height: size.height,
        ),
      ),
    );
    
    return _backgroundImageWidget!;
  }

  Widget _buildBody(BuildContext context, Map<String, dynamic> userData) {
    // 使用GetBuilder监听controller中的封面图变化
    return GetBuilder<ChatController>(
      builder: (controller) {
        // 获取封面URL，优先使用controller中的值
        final String? coverUrl = controller.aiCoverUrl.isNotEmpty 
            ? controller.aiCoverUrl.value 
            : _getInitialCoverUrl(userData);
        
        // 获取接收者ID，确保不为空
        final String receiverUserId = _getSafeUserId(userData);
        
        // 确保isGroupChat不为空
        final bool isGroupChat = _isGroupChat(userData);
        
        return GestureDetector(
          // 处理左右滑动手势
          onHorizontalDragEnd: (details) {
            // 检查是否正在初始化，避免重复切换
            if (controller.isInitializing.value) return;

            // 根据滑动方向切换角色
            if (details.primaryVelocity != null) {
              if (details.primaryVelocity! > 0) {
                // 向右滑动，切换到上一个角色
                controller.switchToPreviousRole();
              } else if (details.primaryVelocity! < 0) {
                // 向左滑动，切换到下一个角色
                controller.switchToNextRole();
              }
            }
          },
          child: Stack(
            children: [
              // 背景封面图层，使用优化后的构建方法
              _buildBackgroundImage(coverUrl),
              
              // 顶部额外阴影层，确保在明亮背景下AppBar内容可见
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: 120, // 阴影高度，覆盖状态栏和AppBar
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.5), // 降低顶部阴影透明度
                        Colors.black.withOpacity(0.3), // 降低中间渐变透明度
                        Colors.transparent, // 完全透明
                      ],
                      stops: const [0.0, 0.6, 1.0],
                    ),
                  ),
                ),
              ),

              // 聊天内容
              SafeArea(
                bottom: false, // 不为底部添加安全区域，让内容可以更贴近底部
                child: Column(
                  children: [
                    Expanded(
                      child: Obx(() {
                        // 显示消息加载错误
                        if (controller.hasMessageLoadingError.value) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.error_outline, color: Colors.red, size: 48),
                                const SizedBox(height: 16),
                                Text(
                                  controller.messageLoadingErrorMessage.value.isNotEmpty ? 
                                    controller.messageLoadingErrorMessage.value : 
                                    'Failed to load messages',
                                  style: const TextStyle(color: Colors.white, fontSize: 16),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton(
                                  onPressed: _retryLoadMessages,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primary,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                  ),
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          );
                        }
                        
                        // 正常显示消息列表
                        return MessagesList(
                          chatController: controller,
                        );
                      }),
                    ),
                    // 底部输入框
                    BottomChatTextField(
                      receiverUserId: receiverUserId,
                      isGroupChat: isGroupChat,
                    ),
                  ],
                ),
              ),

              // WebSocket重连提示组件 - 显示在顶部
              Positioned(
                top: MediaQuery.of(context).padding.top + kToolbarHeight,
                left: 0,
                right: 0,
                child: WsReconnectWidget(),
              ),
            ],
          ),
        );
      }
    );
  }

  // 获取安全的用户ID，确保不为空
  String _getSafeUserId(Map<String, dynamic> userData) {
    if (userData.containsKey(StringsConsts.userId)) {
      final userId = userData[StringsConsts.userId];
      if (userId is String && userId.isNotEmpty) {
        return userId;
      }
    }
    
    // 使用AI前缀+默认角色ID作为接收者ID
    return '${RoleConstants.AI_SENDER_PREFIX}${RoleConstants.DEFAULT_AI_ROLE_ID}';
  }
  
  // 确定是否为群聊
  bool _isGroupChat(Map<String, dynamic> userData) {
    if (userData.containsKey(StringsConsts.isGroupChat)) {
      final isGroupChat = userData[StringsConsts.isGroupChat];
      if (isGroupChat is bool) {
        return isGroupChat;
      }
    }
    
    // 默认不是群聊
    return false;
  }

  // 获取初始封面图URL
  String? _getInitialCoverUrl(Map<String, dynamic> userData) {
    // 尝试从userData中直接获取coverUrl
    if (userData.containsKey('coverUrl')) {
      final coverUrl = userData['coverUrl'] as String?;
      if (coverUrl != null && coverUrl.isNotEmpty) {
        return coverUrl;
      }
    }
    
    return null;
  }

  AppBar _buildAppBar(BuildContext context, Map<String, dynamic> userData) {
    return AppBar(
      backgroundColor: Colors.transparent, // 完全透明背景
      elevation: 0, // 移除阴影
      flexibleSpace: Container(
        decoration: BoxDecoration(
          // 添加从上到下的渐变阴影效果
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.4), // 降低顶部阴影透明度
              Colors.black.withOpacity(0.2), // 降低中间阴影透明度
              Colors.transparent, // 底部完全透明
            ],
            stops: const [0.0, 0.6, 1.0],
          ),
        ),
      ),
      leading: IconButton(
        icon: ShaderMask(
          shaderCallback: (Rect bounds) {
            return LinearGradient(
              colors: [Colors.white, Colors.white],
            ).createShader(bounds);
          },
          child: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            shadows: [
              Shadow(
                color: Colors.black,
                offset: Offset(0, 1),
                blurRadius: 3.0,
              ),
            ],
          ),
        ),
        onPressed: () => Get.back(),
        style: IconButton.styleFrom(
          shadowColor: Colors.black,
          foregroundColor: Colors.white,
        ),
      ),
      title: GetBuilder<ChatController>(
        builder: (controller) {
          // 获取角色名称，优先使用controller中的值
          final String username = controller.aiRoleName.isNotEmpty
              ? controller.aiRoleName.value
              : _getSafeUsername(userData);
          
          // 获取角色头像URL
          final String avatarUrl = controller.aiAvatarUrl.isNotEmpty
              ? controller.aiAvatarUrl.value
              : _getSafeAvatarUrl(userData);
          
          // 获取角色ID
          final int roleId = controller.currentAiRoleId;
          
          // 使用Row布局，包含头像和名称
          return Row(
            children: [
              // 添加可点击的头像
              GestureDetector(
                onTap: () {
                  // 如果角色ID有效，跳转到角色详情页
                  if (roleId > 0) {
                    LogUtil.debug('点击头像，跳转到角色详情页: roleId=$roleId');
                    // 使用路由管理器跳转到角色详情页
                    
                    RouterManager.navigateTo(
                      Routes.roleDetailScreen,
                      arguments: {'roleId': roleId}
                    );
                  }
                },
                child: Container(
                  width: 36,
                  height: 36,
                  margin: const EdgeInsets.only(right: 10),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: avatarUrl.isNotEmpty
                        ? Image.network(
                            avatarUrl,
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return const Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.person,
                                color: Colors.white,
                                size: 20,
                              );
                            },
                          )
                        : const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 20,
                          ),
                  ),
                ),
              ),
              // 角色名称
              Expanded(
                child: Text(
                  username,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    shadows: [
                      Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 4.0,
                        color: Colors.black87,
                      ),
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2.0,
                        color: Colors.black,
                      ),
                      Shadow(
                        offset: Offset(0, 2),
                        blurRadius: 6.0,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        }
      ),
      actions: [
        // 添加举报按钮
        IconButton(
          icon: ShaderMask(
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                colors: [Colors.white, Colors.white],
              ).createShader(bounds);
            },
            child: const Icon(
              Icons.report_outlined,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black,
                  offset: Offset(0, 1),
                  blurRadius: 3.0,
                ),
              ],
            ),
          ),
          onPressed: () {
            // 处理举报逻辑
            _showReportDialog(context);
          },
        ),
      ],
    );
  }
  
  // 获取安全的用户名，确保不为空
  String _getSafeUsername(Map<String, dynamic> userData) {
    if (userData.containsKey(StringsConsts.username)) {
      final username = userData[StringsConsts.username];
      if (username is String && username.isNotEmpty) {
        return username;
      }
    }
    
    // 使用默认值
    return 'AI Assistant';
  }

  // 获取安全的头像URL
  String _getSafeAvatarUrl(Map<String, dynamic> userData) {
    // 尝试获取头像URL
    final profilePic = userData[StringsConsts.profilePic];
    if (profilePic != null && profilePic is String && profilePic.isNotEmpty) {
      return profilePic;
    }
    
    // 尝试获取avatarUrl
    final avatarUrl = userData['avatarUrl'];
    if (avatarUrl != null && avatarUrl is String && avatarUrl.isNotEmpty) {
      return avatarUrl;
    }
    
    return ''; // 返回空字符串作为默认值
  }

  // 显示举报对话框
  void _showReportDialog(BuildContext context) {
    try {
      // 获取角色名称
      final roleName = controller.aiRoleName.value.isNotEmpty
          ? controller.aiRoleName.value
          : 'AI Character';

      // 确保aiRoleId有效
      final int aiRoleId = controller.aiRoleId > 0
          ? controller.aiRoleId
          : controller.currentAiRoleId > 0
          ? controller.currentAiRoleId
          : RoleConstants.DEFAULT_AI_ROLE_ID;

      // 尝试获取举报控制器
      ReportController? reportController;
      try {
        reportController = Get.find<ReportController>();
      } catch (e) {
        // 如果找不到控制器，注册一个新的
        LogUtil.warn('找不到ReportController，尝试注册新实例: $e');
        reportController = Get.put(ReportController());
      }

      // 确保reportController不为空
      if (reportController != null) {
        // 导航到举报页面，使用aiRoleId作为roleId
        reportController.navigateToReportPage(
          roleId: aiRoleId,
          roleName: roleName,
        );
      } else {
        LogUtil.error('无法创建ReportController实例');
        ToastUtil.error('Report function unavailable, please try again later');
      }
    } catch (e) {
      LogUtil.error('打开举报页面失败: $e');
      ToastUtil.error('Report function unavailable, please try again later');
    }
  }
}
