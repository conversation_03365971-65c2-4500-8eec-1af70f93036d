import 'package:get/get.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';
import 'package:rolio/modules/role/repository/role_repository.dart';
import 'package:rolio/modules/role/repository/recommend_repository.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/di/service_bindings.dart';

/// 推荐模块绑定类
/// 
/// 负责推荐模块相关的依赖注入和控制器绑定
class RecommendBinding implements Bindings {
  @override
  void dependencies() {
    try {
      LogUtil.info('开始注册RecommendBinding依赖...');
      
      // 注册推荐模块的仓库
      _registerRepositories();
      
      // 注册推荐模块的服务
      _registerServices();
      
      // 注册推荐模块的控制器
      _registerControllers();
      
      LogUtil.info('RecommendBinding依赖注册完成');
    } catch (e) {
      LogUtil.error('RecommendBinding依赖注册失败: $e');
      rethrow;
    }
  }
  
  void _registerRepositories() {
    // 注册推荐仓库
    if (!Get.isRegistered<RecommendRepository>()) {
      Get.put<RecommendRepository>(RecommendRepository(), permanent: true);
      LogUtil.debug('注册仓库: RecommendRepository');
    }
    
    // 注册角色仓库
    if (!Get.isRegistered<RoleRepository>()) {
      Get.put<RoleRepository>(RoleRepository(), permanent: true);
      LogUtil.debug('注册仓库: RoleRepository');
    }
  }
  
  void _registerServices() {
    // 创建 RecommendService 实例
    final recommendService = RecommendService();
    
    // 使用ServiceBindings安全地替换服务
    
    // 注册RecommendService
    ServiceBindings.replaceService<RecommendService>(recommendService);
    
    // 替换IRoleProvider实现
    ServiceBindings.replaceService<IRoleProvider>(recommendService);
    
    // 注册RoleService (如果尚未注册)
    if (!Get.isRegistered<RoleService>()) {
      Get.put<RoleService>(RoleService(), permanent: true);
      LogUtil.debug('注册服务: RoleService');
    }
  }
  
  void _registerControllers() {
    // 如果已经存在RecommendController，先移除它
    if (Get.isRegistered<RecommendController>()) {
      LogUtil.debug('移除已存在的RecommendController');
      Get.delete<RecommendController>(force: true);
    }
    
    Get.lazyPut<RecommendController>(() => RecommendController(), fenix: true);
    LogUtil.debug('注册控制器: RecommendController');
  }
} 