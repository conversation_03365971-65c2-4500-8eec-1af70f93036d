import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/data_converter_util.dart';
import 'package:rolio/common/utils/http_manager_util.dart';

/// 抽象仓库基类
///
/// 所有仓库类的基类，提供基础功能和错误处理
abstract class BaseRepository {
  final DataConverter _converter = DataConverter();
  
  /// 获取数据转换器
  DataConverter get converter => _converter;
  
  /// 构造函数
  BaseRepository() {
    LogUtil.debug('创建仓库: ${runtimeType.toString()}');
  }
  
  /// 获取仓库名称
  String get name => runtimeType.toString();
  
  /// 初始化仓库
  Future<void> initialize() async {
    LogUtil.debug('初始化仓库: $name');
  }
  
  /// 安全执行异步操作
  /// 
  /// [operation] 要执行的异步操作
  /// [errorMessage] 出错时的错误消息
  Future<T?> safeCall<T>(Future<T> Function() operation, {String? errorMessage}) async {
    try {
      return await operation();
    } catch (e) {
      final exception = ErrorHandler.createAppException(e, errorMessage);
      LogUtil.error('[$name] 操作失败: ${exception.message}');
      throw exception;
    }
  }
  
  /// 执行GET请求
  /// 
  /// [url] 请求URL
  /// [fromJsonT] 从JSON转换为对象的函数
  /// [headers] 请求头
  /// [params] 请求参数
  /// [isEncrypt] 是否加密
  /// [cancelToken] 取消令牌
  Future<T?> executeGet<T>({
    required String url,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    Map<String, dynamic>? params,
    bool? isEncrypt,
    String? cancelToken,
  }) async {
    try {
      LogUtil.info('[$name] 准备发送GET请求到: $url');
      LogUtil.debug('[$name] 请求参数: $params');
      
      final response = await HttpManager.get(
        url: url,
        fromJsonT: fromJsonT,
        headers: headers,
        params: params,
        isEncrypt: isEncrypt,
        cancelToken: cancelToken,
      );
      
      LogUtil.info('[$name] GET请求成功，响应数据类型: ${response.data.runtimeType}');
      return response.data;
    } catch (e) {
      LogUtil.error('[$name] GET请求失败: $e');
      LogUtil.error('[$name] 请求URL: $url');
      LogUtil.error('[$name] 错误详情: ${e.toString()}');
      return null;
    }
  }
  
  /// 执行POST请求
  /// 
  /// [url] 请求URL
  /// [fromJsonT] 从JSON转换为对象的函数
  /// [headers] 请求头
  /// [body] 请求体
  /// [isEncrypt] 是否加密
  /// [cancelToken] 取消令牌
  Future<T?> executePost<T>({
    required String url,
    T Function(dynamic)? fromJsonT,
    Map<String, String>? headers,
    dynamic body,
    bool? isEncrypt,
    String? cancelToken,
  }) async {
    try {
      final response = await HttpManager.post(
        url: url,
        fromJsonT: fromJsonT,
        headers: headers,
        body: body,
        isEncrypt: isEncrypt,
        cancelToken: cancelToken,
      );
      return response.data;
    } catch (e) {
      LogUtil.error('[$name] POST请求失败: $e');
      return null;
    }
  }
}

/// CRUD仓库接口
///
/// 定义基本的CRUD操作
abstract class CrudRepository<T, ID> extends BaseRepository {
  /// 创建实体
  Future<T> create(T entity);
  
  /// 通过ID获取实体
  Future<T?> findById(ID id);
  
  /// 获取所有实体
  Future<List<T>> findAll();
  
  /// 更新实体
  Future<T> update(T entity);
  
  /// 通过ID删除实体
  Future<bool> deleteById(ID id);
}

/// 远程仓库接口
///
/// 定义从远程获取数据的操作
abstract class RemoteRepository extends BaseRepository {
  /// 检查网络连接
  Future<bool> checkConnection();
  
  /// 设置请求超时时间（秒）
  set timeout(int seconds);
}

/// 本地仓库接口
///
/// 定义本地数据存取操作
abstract class LocalRepository extends BaseRepository {
  /// 清除所有数据
  Future<void> clearAll();
  
  /// 检查存储可用性
  Future<bool> isStorageAvailable();
}

/// 缓存仓库接口
///
/// 定义缓存操作
abstract class CacheRepository<K, V> extends BaseRepository {
  /// 添加缓存
  void put(K key, V value);
  
  /// 获取缓存
  V? get(K key);
  
  /// 移除缓存
  void remove(K key);
  
  /// 清空缓存
  void clear();
  
  /// 缓存是否包含键
  bool containsKey(K key);
  
  /// 获取缓存大小
  int get size;
  
  /// 获取所有缓存键
  List<K> get keys;
}