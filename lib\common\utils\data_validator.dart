import 'package:rolio/common/models/validation_result.dart';
import 'package:rolio/common/utils/logger.dart';

/// 数据验证工具类
/// 
/// 提供通用的数据验证方法，用于增强数据验证
class DataValidator {
  /// 验证必填字段（默认为警告级别）
  static ValidationResult validateRequired(String field, dynamic value) {
    if (value == null) {
      return ValidationResult.errorWithLevel(
        field, 
        '$field不能为空', 
        ValidationErrorLevel.WARNING
      );
    }
    
    if (value is String && value.trim().isEmpty) {
      return ValidationResult.errorWithLevel(
        field, 
        '$field不能为空', 
        ValidationErrorLevel.WARNING
      );
    }
    
    return ValidationResult.success();
  }
  
  /// 验证必填字段（致命错误级别）
  static ValidationResult validateRequiredFatal(String field, dynamic value) {
    if (value == null) {
      return ValidationResult.errorWithLevel(
        field, 
        '$field不能为空', 
        ValidationErrorLevel.FATAL
      );
    }
    
    if (value is String && value.trim().isEmpty) {
      return ValidationResult.errorWithLevel(
        field, 
        '$field不能为空', 
        ValidationErrorLevel.FATAL
      );
    }
    
    return ValidationResult.success();
  }
  
  /// 验证必填字段（警告级别）
  static ValidationResult validateRequiredWarning(String field, dynamic value) {
    if (value == null) {
      return ValidationResult.errorWithLevel(
        field, 
        '$field不能为空', 
        ValidationErrorLevel.WARNING
      );
    }
    
    if (value is String && value.trim().isEmpty) {
      return ValidationResult.errorWithLevel(
        field, 
        '$field不能为空', 
        ValidationErrorLevel.WARNING
      );
    }
    
    return ValidationResult.success();
  }
  
  /// 验证字符串字段（默认为警告级别）
  static ValidationResult validateString(String field, dynamic value, {int? minLength, int? maxLength, ValidationErrorLevel level = ValidationErrorLevel.WARNING}) {
    // 如果值为null，返回成功（必填性由validateRequired处理）
    if (value == null) {
      return ValidationResult.success();
    }
    
    // 如果不是字符串，返回错误
    if (value is! String) {
      return ValidationResult.errorWithLevel(field, '$field必须是字符串', level);
    }
    
    // 验证最小长度
    if (minLength != null && value.length < minLength) {
      return ValidationResult.errorWithLevel(field, '$field长度不能小于$minLength', level);
    }
    
    // 验证最大长度
    if (maxLength != null && value.length > maxLength) {
      return ValidationResult.errorWithLevel(field, '$field长度不能大于$maxLength', level);
    }
    
    return ValidationResult.success();
  }
  
  /// 验证整数字段（默认为警告级别）
  static ValidationResult validateInt(String field, dynamic value, {int? min, int? max, ValidationErrorLevel level = ValidationErrorLevel.WARNING}) {
    // 如果值为null，返回成功（必填性由validateRequired处理）
    if (value == null) {
      return ValidationResult.success();
    }
    
    // 尝试转换为整数
    int? intValue;
    if (value is int) {
      intValue = value;
    } else if (value is String) {
      intValue = int.tryParse(value);
    }
    
    // 如果转换失败，返回错误
    if (intValue == null) {
      return ValidationResult.errorWithLevel(field, '$field必须是整数', level);
    }
    
    // 验证最小值
    if (min != null && intValue < min) {
      return ValidationResult.errorWithLevel(field, '$field不能小于$min', level);
    }
    
    // 验证最大值
    if (max != null && intValue > max) {
      return ValidationResult.errorWithLevel(field, '$field不能大于$max', level);
    }
    
    return ValidationResult.success();
  }
  
  /// 验证URL字段（默认为警告级别）
  static ValidationResult validateUrl(String field, dynamic value, {List<String>? allowedDomains, ValidationErrorLevel level = ValidationErrorLevel.WARNING}) {
    // 如果值为null，返回成功（必填性由validateRequired处理）
    if (value == null || (value is String && value.isEmpty)) {
      return ValidationResult.success();
    }
    
    // 如果不是字符串，返回错误
    if (value is! String) {
      return ValidationResult.errorWithLevel(field, '$field必须是字符串', level);
    }
    
    // URL格式验证
    final urlRegExp = RegExp(
      r'^(http|https):\/\/([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$',
      caseSensitive: false,
    );
    
    if (!urlRegExp.hasMatch(value)) {
      return ValidationResult.errorWithLevel(field, '$field必须是有效的URL', level);
    }
    
    // 如果指定了允许的域名，验证域名
    if (allowedDomains != null && allowedDomains.isNotEmpty) {
      bool domainValid = false;
      
      for (final domain in allowedDomains) {
        if (value.toLowerCase().contains(domain.toLowerCase())) {
          domainValid = true;
          break;
        }
      }
      
      if (!domainValid) {
        return ValidationResult.errorWithLevel(field, '$field域名不在允许列表中', level);
      }
    }
    
    return ValidationResult.success();
  }
  
  /// 验证列表字段（默认为警告级别）
  static ValidationResult validateList(String field, dynamic value, {int? minLength, int? maxLength, ValidationErrorLevel level = ValidationErrorLevel.WARNING}) {
    // 如果值为null，返回成功（必填性由validateRequired处理）
    if (value == null) {
      return ValidationResult.success();
    }
    
    // 如果不是列表，返回错误
    if (value is! List) {
      return ValidationResult.errorWithLevel(field, '$field必须是列表', level);
    }
    
    // 验证最小长度
    if (minLength != null && value.length < minLength) {
      return ValidationResult.errorWithLevel(field, '$field长度不能小于$minLength', level);
    }
    
    // 验证最大长度
    if (maxLength != null && value.length > maxLength) {
      return ValidationResult.errorWithLevel(field, '$field长度不能大于$maxLength', level);
    }
    
    return ValidationResult.success();
  }
  
  /// 验证布尔字段（默认为警告级别）
  static ValidationResult validateBool(String field, dynamic value, {ValidationErrorLevel level = ValidationErrorLevel.WARNING}) {
    // 如果值为null，返回成功（必填性由validateRequired处理）
    if (value == null) {
      return ValidationResult.success();
    }
    
    // 如果是布尔值，返回成功
    if (value is bool) {
      return ValidationResult.success();
    }
    
    // 如果是字符串，尝试转换
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == 'false') {
        return ValidationResult.success();
      }
    }
    
    // 其他情况返回错误
    return ValidationResult.errorWithLevel(field, '$field必须是布尔值', level);
  }
  
  /// 安全获取字符串值
  static String safeGetString(Map<String, dynamic> json, String key, {String defaultValue = ''}) {
    try {
      if (json[key] != null) {
        return json[key].toString().trim();
      }
    } catch (e) {
      LogUtil.warn('获取字符串字段失败: $key, 错误: $e');
    }
    return defaultValue;
  }
  
  /// 安全获取整数值
  static int safeGetInt(Map<String, dynamic> json, String key, {int defaultValue = 0}) {
    try {
      if (json[key] != null) {
        if (json[key] is int) {
          return json[key];
        } else {
          return int.tryParse(json[key].toString()) ?? defaultValue;
        }
      }
    } catch (e) {
      LogUtil.warn('获取整数字段失败: $key, 错误: $e');
    }
    return defaultValue;
  }
  
  /// 安全获取布尔值
  static bool safeGetBool(Map<String, dynamic> json, String key, {bool defaultValue = false}) {
    try {
      if (json[key] != null) {
        if (json[key] is bool) {
          return json[key];
        } else {
          return json[key].toString().toLowerCase() == 'true';
        }
      }
    } catch (e) {
      LogUtil.warn('获取布尔字段失败: $key, 错误: $e');
    }
    return defaultValue;
  }
  
  /// 安全获取字符串列表
  static List<String> safeGetStringList(Map<String, dynamic> json, String key) {
    try {
      if (json[key] != null && json[key] is List) {
        return List<String>.from(json[key].map((item) => item.toString()));
      }
    } catch (e) {
      LogUtil.warn('获取字符串列表字段失败: $key, 错误: $e');
    }
    return [];
  }
} 