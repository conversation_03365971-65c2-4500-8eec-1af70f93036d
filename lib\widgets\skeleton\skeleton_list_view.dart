import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';

/// 骨架列表视图组件
///
/// 用于列表加载状态的占位UI
class SkeletonListView extends StatelessWidget {
  /// 列表项数量
  final int itemCount;
  
  /// 列表项高度
  final double itemHeight;
  
  /// 列表项间距
  final double spacing;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 是否可滚动
  final bool scrollable;
  
  /// 自定义骨架项构建器
  final Widget Function(BuildContext, int)? itemBuilder;

  /// 构造函数
  const SkeletonListView({
    Key? key,
    this.itemCount = 5,
    this.itemHeight = 80,
    this.spacing = 16,
    this.padding = const EdgeInsets.all(16),
    this.scrollable = true,
    this.itemBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: padding,
      physics: scrollable 
          ? const AlwaysScrollableScrollPhysics() 
          : const NeverScrollableScrollPhysics(),
      shrinkWrap: !scrollable,
      itemCount: itemCount,
      separatorBuilder: (context, index) => SizedBox(height: spacing),
      itemBuilder: itemBuilder ?? _defaultItemBuilder,
    );
  }
  
  /// 默认骨架项构建器
  Widget _defaultItemBuilder(BuildContext context, int index) {
    return ShimmerWidget(
      child: Container(
        height: itemHeight,
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧圆形头像
              SkeletonContainer.circular(size: 48),
              const SizedBox(width: 16),
              // 右侧内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 标题
                    SkeletonContainer(
                      width: double.infinity,
                      height: 16,
                      borderRadius: 4,
                    ),
                    const SizedBox(height: 8),
                    // 副标题
                    SkeletonContainer(
                      width: double.infinity * 0.7,
                      height: 12,
                      borderRadius: 4,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 