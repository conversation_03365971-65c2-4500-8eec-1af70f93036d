import 'package:get/get.dart';
import 'package:rolio/common/enums/report_reason.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/chat/model/report.dart';
import 'package:rolio/modules/chat/repository/report_repository.dart';

/// 举报服务
/// 
/// 负责处理举报相关的业务逻辑
class ReportService extends GetxService {
  final ReportRepository _repository;
  
  // 用于防止重复提交的标记
  final RxBool _isSubmitting = false.obs;
  
  ReportService({ReportRepository? repository})
      : _repository = repository ?? Get.find<ReportRepository>();
  
  /// 提交举报
  /// 
  /// 使用独立的HTTP请求提交举报，不依赖于WebSocket连接状态。
  /// 即使聊天的WebSocket连接断开，此功能也能正常工作。
  /// 添加本地防重复提交机制，确保用户不会在短时间内重复提交相同举报。
  Future<bool> submitReport({
    required int roleId,
    required ReportReason reason,
    required String description,
  }) async {
    try {
      // 检查是否已经有提交正在进行中，避免重复点击
      if (_isSubmitting.value) {
        LogUtil.info('已有举报提交正在进行中，忽略重复请求');
        return false;
      }
      
      // 设置提交状态为true
      _isSubmitting.value = true;
      LogUtil.info('通过独立HTTP请求提交举报，与WebSocket连接状态无关');
      
      // 调用仓库提交举报，只传递必要参数
      final result = await _repository.submitReport(
        roleId: roleId,
        reason: reason.toString().split('.').last,
        description: description,
      );
      
      return result;
    } catch (e) {
      LogUtil.error('提交举报异常: $e');
      return false;
    } finally {
      // 无论成功与否，延迟一秒后重置提交状态，避免连续提交
      Future.delayed(const Duration(seconds: 1), () {
        _isSubmitting.value = false;
      });
    }
  }
  
  /// 获取用户举报历史
  Future<List<Report>> getReportHistory() async {
    try {
      return await _repository.getReportHistory();
    } catch (e) {
      LogUtil.error('获取举报历史异常: $e');
      return [];
    }
  }
} 