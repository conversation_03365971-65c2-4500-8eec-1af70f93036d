import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/datetime_utils.dart';
import 'package:rolio/common/utils/data_validator.dart';
import 'package:rolio/common/models/validation_result.dart';
import 'package:rolio/common/models/ai_role.dart';

/// 会话模型类
class Session {
  final int id;
  final String title;
  final int aiRoleId;
  final String userId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String avatarUrl;
  final String coverUrl;
  final String? lastMessage;
  final DateTime? lastMessageCreatedAt; // 最后一条消息的创建时间
  final String? description; // 角色描述
  final bool isPinned; // 是否置顶
  final bool isHidden; // 是否隐藏
  final AiRole? role; // 角色信息
  
  /// 构造函数
  Session({
    required this.id,
    required this.title,
    required this.aiRoleId,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
    this.avatarUrl = '',
    this.coverUrl = '',
    this.lastMessage,
    this.lastMessageCreatedAt,
    this.description,
    this.isPinned = false,
    this.isHidden = false,
    this.role,
  });
  
  /// 从Map创建会话对象
  factory Session.fromMap(Map<String, dynamic> map) {
    try {
      // 获取ID - 尝试多个可能的字段名
      final id = DataValidator.safeGetInt(map, 'id');
      final conversationId = DataValidator.safeGetInt(map, 'conversation_id');
      
      // 获取标题 - 尝试多个可能的字段名
      final title = DataValidator.safeGetString(map, 'title');
      final name = DataValidator.safeGetString(map, 'name');
      
      // 获取AI角色ID - 尝试多个可能的字段名
      final aiRoleId = DataValidator.safeGetInt(map, 'ai_role_id');
      final characterDesignId = DataValidator.safeGetInt(map, 'character_design_id');
      final roleId = DataValidator.safeGetInt(map, 'role_id');
      final userId = DataValidator.safeGetString(map, 'user_id');
      
      // 角色数据 - 处理role字段
      AiRole? roleObject;
      if (map.containsKey('role') && map['role'] != null && map['role'] is Map) {
        try {
          roleObject = AiRole.fromJson(map['role'] as Map<String, dynamic>);
          LogUtil.debug('成功从会话数据中解析角色信息: ${roleObject.name}, ID=${roleObject.id}');
        } catch (e) {
          LogUtil.error('解析会话中的角色数据失败: $e');
        }
      }
      
      // 获取头像和封面URL，优先使用角色信息
      String avatarUrl = '';
      String coverUrl = '';
      String? description;
      
      // 如果有角色对象，优先使用角色的信息
      if (roleObject != null) {
        avatarUrl = roleObject.avatarUrl;
        coverUrl = roleObject.coverUrl;
        description = roleObject.description;
      } else {
        // 否则从外层字段获取
        avatarUrl = DataValidator.safeGetString(map, 'avatar_url');
        coverUrl = DataValidator.safeGetString(map, 'cover_url');
        description = DataValidator.safeGetString(map, 'description');
      }
      
      final lastMessage = DataValidator.safeGetString(map, 'last_message');
      
      // 获取置顶状态，0表示未置顶，1表示已置顶
      final isPinned = DataValidator.safeGetInt(map, 'is_pinned') == 1;
      // 获取隐藏状态，0表示未隐藏，1表示已隐藏
      final isHidden = DataValidator.safeGetInt(map, 'is_hidden') == 1;
      
      // 确定最终ID值（优先使用id，其次是conversation_id）
      final finalId = id > 0 ? id : conversationId;
      
      // 确定最终标题（优先使用title，其次是name，再次是角色名称）
      String finalTitle = title.isNotEmpty ? title : (name.isNotEmpty ? name : '未命名对话');
      if (finalTitle == '未命名对话' && roleObject != null && roleObject.name.isNotEmpty) {
        finalTitle = roleObject.name;
      }
      
      // 确定最终AI角色ID（按优先级尝试不同字段）
      int finalAiRoleId = 0;
      if (aiRoleId > 0) {
        finalAiRoleId = aiRoleId;
      } else if (characterDesignId > 0) {
        finalAiRoleId = characterDesignId;
      } else if (roleId > 0) {
        finalAiRoleId = roleId;
      } else if (roleObject != null) {
        finalAiRoleId = roleObject.id;
      }
      
      // 致命错误验证 - 只有ID是致命的
      if (finalId <= 0) {
        LogUtil.error('会话ID无效(致命错误): $finalId, 源数据: $map');
        return Session(
          id: 0,
          title: '无效ID',
          aiRoleId: 0,
          userId: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          description: '会话ID验证失败(致命错误)',
        );
      }
      
      // 警告级别验证 - 记录警告但不返回默认对象
      List<String> warnings = [];
      
      // 标题验证
      if (finalTitle.isEmpty) {
        LogUtil.warn('会话标题为空(警告)，使用默认标题');
        finalTitle = '未命名对话';
        warnings.add('标题为空');
      }
      
      // 角色ID验证
      if (finalAiRoleId <= 0 && roleObject == null) {
        LogUtil.warn('会话没有关联角色ID(警告)');
        warnings.add('无角色ID');
      }
      
      // 检查数据来源
      bool isFromCache = DataValidator.safeGetBool(map, 'is_from_cache', defaultValue: false);
      
      // 解析创建时间、更新时间和最后一条消息创建时间
      DateTime createdAt = DateTime.now();
      DateTime updatedAt = DateTime.now();
      DateTime? lastMessageCreatedAt;
      
      try {
        // 获取时间字符串
        final createdAtStr = DataValidator.safeGetString(map, 'created_at');
        final updatedAtStr = DataValidator.safeGetString(map, 'updated_at');
        final lastMessageCreatedAtStr = DataValidator.safeGetString(map, 'last_message_created_at');
        
        // 统一解析时间，区分缓存数据和服务器数据
        if (isFromCache) {
          // 来自缓存的数据直接解析，不再进行时区转换
          if (createdAtStr.isNotEmpty) {
            createdAt = DateTime.parse(createdAtStr);
          }
          
          if (updatedAtStr.isNotEmpty) {
            updatedAt = DateTime.parse(updatedAtStr);
          } else {
            updatedAt = createdAt; // 如果更新时间为空，使用创建时间
          }
          
          if (lastMessageCreatedAtStr.isNotEmpty) {
            lastMessageCreatedAt = DateTime.parse(lastMessageCreatedAtStr);
          }
        } else {
          // 来自服务器的数据需要进行时区转换
          if (createdAtStr.isNotEmpty) {
            createdAt = DateTimeUtils.handleServerTime(createdAtStr);
          }
          
          if (updatedAtStr.isNotEmpty) {
            updatedAt = DateTimeUtils.handleServerTime(updatedAtStr);
          } else {
            updatedAt = createdAt; // 如果更新时间为空，使用创建时间
          }
          
          if (lastMessageCreatedAtStr.isNotEmpty) {
            lastMessageCreatedAt = DateTimeUtils.handleServerTime(lastMessageCreatedAtStr);
          }
        }
      } catch (e) {
        LogUtil.error('解析时间数据失败: $e');
        // 记录警告但继续使用默认时间
        warnings.add('时间解析失败');
      }
      
      // 如果有警告，记录汇总信息
      if (warnings.isNotEmpty) {
        LogUtil.warn('会话数据存在警告级别问题: ${warnings.join(", ")}');
      }
      
      // 创建会话对象
      return Session(
        id: finalId,
        title: finalTitle,
        aiRoleId: finalAiRoleId,
        userId: userId,
        createdAt: createdAt,
        updatedAt: updatedAt,
        avatarUrl: avatarUrl,
        coverUrl: coverUrl,
        lastMessage: lastMessage,
        lastMessageCreatedAt: lastMessageCreatedAt,
        description: description,
        isPinned: isPinned,
        isHidden: isHidden,
        role: roleObject, // 设置角色对象
      );
    } catch (e) {
      LogUtil.error('创建会话对象失败(致命错误): $e');
      // 返回一个默认的会话对象，避免应用崩溃
      return Session(
        id: 0,
        title: '解析错误',
        aiRoleId: 0,
        userId: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        description: '数据解析失败(致命错误)',
      );
    }
  }
  
  /// 转换为Map
  Map<String, dynamic> toMap() {
    final map = {
      'id': id,
      'title': title,
      'ai_role_id': aiRoleId,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'avatar_url': avatarUrl,
      'cover_url': coverUrl,
      'last_message': lastMessage,
      'last_message_created_at': lastMessageCreatedAt?.toIso8601String(),
      'description': description,
      'is_pinned': isPinned ? 1 : 0,
      'is_hidden': isHidden ? 1 : 0,
      'is_from_cache': true, // 标记数据来源为缓存
    };
    
    // 如果有角色信息，添加到map中
    if (role != null) {
      map['role'] = role!.toMap();
    }
    
    return map;
  }
  
  /// 克隆会话对象，可选择性地更改某些属性
  Session copyWith({
    int? id,
    String? title,
    int? aiRoleId,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? avatarUrl,
    String? coverUrl,
    String? lastMessage,
    DateTime? lastMessageCreatedAt,
    String? description,
    bool? isPinned,
    bool? isHidden,
    AiRole? role,
  }) {
    return Session(
      id: id ?? this.id,
      title: title ?? this.title,
      aiRoleId: aiRoleId ?? this.aiRoleId,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      coverUrl: coverUrl ?? this.coverUrl,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageCreatedAt: lastMessageCreatedAt ?? this.lastMessageCreatedAt,
      description: description ?? this.description,
      isPinned: isPinned ?? this.isPinned,
      isHidden: isHidden ?? this.isHidden,
      role: role ?? this.role,
    );
  }
  
  /// 验证会话数据是否有效 - 简化版本，只验证必要字段
  ValidationResult validate() {
    // 仅验证关键字段
    if (id <= 0) {
      return ValidationResult.error('id', 'ID无效');
    }
    
    if (title.isEmpty) {
      return ValidationResult.error('title', '标题不能为空');
    }
    
    return ValidationResult.success();
  }
} 