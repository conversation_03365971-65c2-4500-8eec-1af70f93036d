import 'package:flutter/material.dart';
import 'package:rolio/modules/sessions/model/session.dart';
import 'package:rolio/widgets/ai_avatar.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/datetime_utils.dart';
import 'package:rolio/widgets/context_menu.dart';

class ChatListItem extends StatelessWidget {
  final Session session;
  final VoidCallback onTap;
  final Function(int) onDelete;
  final Function(int) onTogglePin;
  final Function(int) onHide;
  
  const ChatListItem({
    Key? key,
    required this.session,
    required this.onTap,
    required this.onDelete,
    required this.onTogglePin,
    required this.onHide,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: () => _showContextMenu(context),
      child: Container(
        decoration: BoxDecoration(
          // 使用高亮色而非灰色背景表示置顶
          color: session.isPinned ? Color(0xFF1A2B3C) : Colors.black,
          // 移除会话之间的分割线
        ),
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12.0),
            child: Row(
              children: [
                _buildAvatar(),
                const SizedBox(width: 16),
                // 消息内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              session.title,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                // 置顶会话标题文字使用白色，与普通会话一致
                                color: Colors.white,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          _buildTimeLabel(),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        session.lastMessage ?? 'Start chatting...',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade500,
                          fontStyle: session.lastMessage == null ? FontStyle.italic : FontStyle.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // 显示上下文菜单
  void _showContextMenu(BuildContext context) {
    // 获取当前位置信息
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Offset position = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;
    
    // 定义菜单项
    List<ContextMenuItem> menuItems = [
      // 置顶/取消置顶选项
      ContextMenuItem(
        icon: session.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
        text: session.isPinned ? 'Unpin' : 'Pin',
        iconColor: Colors.blue,
        onTap: () => onTogglePin(session.id),
      ),
      // 隐藏选项
      ContextMenuItem(
        icon: Icons.visibility_off,
        text: 'Hide',
        iconColor: Colors.orange,
        onTap: () => _confirmHide(context),
      ),
      // 删除选项
      ContextMenuItem(
        icon: Icons.delete_outline,
        text: 'Delete',
        iconColor: Colors.red,
        onTap: () => _confirmDelete(context),
      ),
    ];
    
    // 显示菜单
    ContextMenu.show(
      context: context,
      menuItems: menuItems,
      position: Offset(position.dx + (size.width / 2), position.dy + (size.height / 2)),
      backgroundColor: Colors.grey.shade900,
      borderColor: Colors.grey.shade800,
      borderRadius: 8.0,
      elevation: 4.0,
    );
  }
  
  // 删除确认对话框
  void _confirmDelete(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey.shade900,
          title: const Text('Confirm Delete', style: TextStyle(color: Colors.white)),
          content: const Text('Are you sure you want to delete this conversation?', style: TextStyle(color: Colors.white70)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDelete(session.id);
              },
              child: const Text(
                'Delete',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
  
  // 隐藏确认对话框
  void _confirmHide(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey.shade900,
          title: const Text('Confirm Hide', style: TextStyle(color: Colors.white)),
          content: const Text('Are you sure you want to hide this conversation? You can find it later in hidden conversations.', style: TextStyle(color: Colors.white70)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onHide(session.id);
              },
              child: Text(
                'Hide',
                style: TextStyle(color: Colors.orange.shade400),
              ),
            ),
          ],
        );
      },
    );
  }
  
  // 构建头像前的置顶图标
  Widget _buildLeadingIcon() {
    if (session.isPinned) {
      return Padding(
        padding: const EdgeInsets.only(left: 8.0, right: 4.0),
        child: Icon(
          Icons.push_pin,
          size: 16,
          color: Colors.blue.shade300,
        ),
      );
    }
    return const SizedBox(width: 0);
  }
  
  // 构建头像组件
  Widget _buildAvatar() {
    return Hero(
      tag: 'avatar_${session.id}',
      child: AiAvatar(
        characterId: session.aiRoleId,
        avatarUrl: session.avatarUrl,
        size: 56.0,
        borderColor: Colors.grey.shade700,
      ),
    );
  }
  
  // 构建时间标签
  Widget _buildTimeLabel() {
    return Text(
      _formatTime(session.lastMessageCreatedAt ?? session.updatedAt),
      style: TextStyle(
        fontSize: 12,
        color: Colors.grey.shade500,
      ),
    );
  }
  
  String _formatTime(DateTime time) {
    try {
      // 直接使用DateTimeUtils统一格式化时间
      return DateTimeUtils.formatSessionTime(time);
    } catch (e) {
      LogUtil.error('格式化时间失败: $e');
      return '';
    }
  }
} 