import 'package:flutter/material.dart';

/// 骨架容器组件
/// 
/// 用于创建骨架屏的基础容器，支持自定义形状、大小和颜色
class SkeletonContainer extends StatelessWidget {
  /// 宽度
  final double? width;
  
  /// 高度
  final double? height;
  
  /// 圆角半径
  final double borderRadius;
  
  /// 背景颜色
  final Color? color;
  
  /// 构造函数
  const SkeletonContainer({
    Key? key,
    this.width,
    this.height,
    this.borderRadius = 0.0,
    this.color,
  }) : super(key: key);
  
  /// 创建圆形骨架容器
  factory SkeletonContainer.circular({
    Key? key,
    required double size,
    Color? color,
  }) {
    return SkeletonContainer(
      key: key,
      width: size,
      height: size,
      borderRadius: size / 2,
      color: color,
    );
  }
  
  /// 创建矩形骨架容器
  factory SkeletonContainer.rectangular({
    Key? key,
    double? width,
    double? height,
    double borderRadius = 4.0,
    Color? color,
  }) {
    return SkeletonContainer(
      key: key,
      width: width,
      height: height,
      borderRadius: borderRadius,
      color: color,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 根据当前主题适配颜色
    final defaultColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[800]
        : Colors.grey[300];
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color ?? defaultColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
} 