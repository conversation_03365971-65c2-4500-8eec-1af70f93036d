{"flutter": {"platforms": {"android": {"default": {"projectId": "rolio-1bdad", "appId": "1:16749514559:android:7be0e42f0827d90364cd8a", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "rolio-1bdad", "appId": "1:16749514559:ios:9c10e8bc5e2457f764cd8a", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "rolio-1bdad", "appId": "1:16749514559:ios:9c10e8bc5e2457f764cd8a", "uploadDebugSymbols": true, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "rolio-1bdad", "configurations": {"android": "1:16749514559:android:7be0e42f0827d90364cd8a", "ios": "1:16749514559:ios:9c10e8bc5e2457f764cd8a", "macos": "1:16749514559:ios:9c10e8bc5e2457f764cd8a", "web": "1:16749514559:web:147a8024f6bd49b064cd8a", "windows": "1:16749514559:web:0ec9843c2cfd308264cd8a"}}}}}}