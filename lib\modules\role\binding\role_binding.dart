import 'package:get/get.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/controller/role_controller.dart';
import 'package:rolio/modules/role/controller/favorite_controller.dart';
import 'package:rolio/modules/role/repository/role_repository.dart';
import 'package:rolio/modules/role/repository/recommend_repository.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/modules/role/service/role_service.dart';

/// 角色详情模块绑定类
/// 
/// 负责角色详情页面相关的依赖注入和控制器绑定
/// 同时负责收藏功能的依赖注入
class RoleBinding implements Bindings {
  @override
  void dependencies() {
    try {
      LogUtil.info('开始注册RoleBinding依赖...');
      
      // 确保仓库和服务已注册
      _ensureRepositoriesAndServices();
      
      // 注册角色详情控制器
      _registerControllers();
      
      LogUtil.info('RoleBinding依赖注册完成');
    } catch (e) {
      LogUtil.error('RoleBinding依赖注册失败: $e');
      rethrow;
    }
  }
  
  void _ensureRepositoriesAndServices() {
    // 确保角色仓库已注册
    if (!Get.isRegistered<RoleRepository>()) {
      Get.put<RoleRepository>(RoleRepository(), permanent: true);
      LogUtil.debug('注册仓库: RoleRepository');
    }
    
    // 确保推荐仓库已注册
    if (!Get.isRegistered<RecommendRepository>()) {
      Get.put<RecommendRepository>(RecommendRepository(), permanent: true);
      LogUtil.debug('注册仓库: RecommendRepository');
    }
    
    // 确保RecommendService已注册
    if (!Get.isRegistered<RecommendService>()) {
      final recommendService = RecommendService();
      Get.put<RecommendService>(recommendService, permanent: true);
      LogUtil.debug('注册服务: RecommendService');
      
      // 如果IRoleProvider未注册，也注册它
      if (!Get.isRegistered<IRoleProvider>()) {
        Get.put<IRoleProvider>(recommendService, permanent: true);
        LogUtil.debug('注册服务: IRoleProvider');
      }
    }
    
    // 注册RoleService
    if (!Get.isRegistered<RoleService>()) {
      final roleService = RoleService();
      Get.put<RoleService>(roleService, permanent: true);
      LogUtil.debug('注册服务: RoleService');
    }
  }
  
  void _registerControllers() {
    // 如果已经存在RoleController，先移除它
    if (Get.isRegistered<RoleController>()) {
      LogUtil.debug('RoleBinding: 移除已存在的RoleController');
      Get.delete<RoleController>(force: true);
    }

    // 确保RoleService已经注册
    if (!Get.isRegistered<RoleService>()) {
      final roleService = RoleService();
      Get.put<RoleService>(roleService, permanent: true);
      LogUtil.debug('RoleBinding: 注册RoleService');
    }
    
    // 注册控制器
    Get.lazyPut<RoleController>(() => RoleController(), fenix: true);
    LogUtil.debug('RoleBinding: 注册RoleController');
    
    // 注册收藏控制器 FavoriteController
    if (Get.isRegistered<FavoriteController>()) {
      LogUtil.debug('移除已存在的FavoriteController');
      Get.delete<FavoriteController>(force: true);
    }
    
    Get.lazyPut<FavoriteController>(() => FavoriteController());
    LogUtil.debug('注册控制器: FavoriteController');
  }
}