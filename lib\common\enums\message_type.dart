enum MessageType {
  text('text'),
  image('image'),
  gif('gif'),
  audio('audio'),
  video('video'),
  file('file'),
  system('system');

  final String type;
  const MessageType(this.type);
}

extension ConvertMessageType on String {
  MessageType toEnum() {
    switch (this) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'gif':
        return MessageType.gif;
      case 'audio':
        return MessageType.audio;
      case 'video':
        return MessageType.video;
      case 'file':
        return MessageType.file;
      case 'system':
        return MessageType.system;
      default:
        return MessageType.text;
    }
  }
}
