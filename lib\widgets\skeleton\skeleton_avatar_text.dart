import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';

/// 带头像和文本的骨架屏组件
///
/// 适用于聊天列表项、用户信息卡片等场景
class SkeletonAvatarText extends StatelessWidget {
  /// 头像尺寸
  final double avatarSize;
  
  /// 文本行数
  final int textLines;
  
  /// 文本行高
  final double textLineHeight;
  
  /// 文本行间距
  final double textLineSpacing;
  
  /// 头像与文本间距
  final double spacing;
  
  /// 构造函数
  const SkeletonAvatarText({
    Key? key,
    this.avatarSize = 48,
    this.textLines = 2,
    this.textLineHeight = 14,
    this.textLineSpacing = 6,
    this.spacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 头像
          SkeletonContainer.circular(
            size: avatarSize,
          ),
          SizedBox(width: spacing),
          // 文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: List.generate(
                textLines,
                (index) => Padding(
                  padding: EdgeInsets.only(
                    bottom: index < textLines - 1 ? textLineSpacing : 0,
                  ),
                  child: SkeletonContainer(
                    width: index == 0 ? double.infinity : double.infinity * (0.7 - (index * 0.1)),
                    height: textLineHeight,
                    borderRadius: 4,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 