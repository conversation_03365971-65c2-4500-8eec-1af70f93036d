import "dart:convert";

import "package:rolio/generated/json/account_server_register_resp_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

export 'package:rolio/generated/json/account_server_register_resp_entity.g.dart';

@JsonSerializable()
class AccountServerRegisterRespEntity {
  late String token;
  late int userId;
  late int userType;

  AccountServerRegisterRespEntity();

  factory AccountServerRegisterRespEntity.fromJson(Map<String, dynamic> json) =>
      $AccountServerRegisterRespEntityFromJson(json);

  Map<String, dynamic> toJson() => $AccountServerRegisterRespEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
