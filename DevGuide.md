# Rolio 项目开发指南

## 1. 项目架构

Rolio 采用基于 GetX 的 MVCS (Model-View-Controller-Service) 架构模式，清晰分离各层职责：

### 1.1 目录结构与职责划分

- `lib/`：Flutter 主代码目录
    - `common/`：通用组件和工具
        - `constants/`：常量定义（颜色、字符串、主题、WebSocket事件等）
        - `enums/`：枚举类型（消息类型等）
        - `helper_methods/`：辅助方法
        - `utils/`：工具类（日志、HTTP等）
    - `manager/`：全局管理器
        - `app_initializer.dart`：应用初始化（Firebase、用户认证、WebSocket等）
        - `global_state.dart`：全局状态管理（当前用户、初始化状态等）
        - `ws_manager.dart`：WebSocket连接与事件管理
    - `modules/`：功能模块，每个模块包含：
        - `binding/`：依赖注入绑定
        - `controller/`：业务逻辑与状态管理
        - `model/`：数据模型
        - `service/`：服务层，处理数据获取与业务逻辑
        - `repository/`：数据仓库，处理数据存储与缓存
        - `view/`：UI组件
    - `routes/`：路由管理
    - `widgets/`：公共UI组件

### 1.2 分层架构

- **视图层（View）**：负责UI展示，位于各模块的`view/`目录
- **控制器层（Controller）**：负责业务逻辑与状态管理，位于各模块的`controller/`目录
- **服务层（Service）**：负责数据处理与业务逻辑封装，位于各模块的`service/`目录
- **模型层（Model）**：数据结构定义，位于各模块的`model/`目录
- **管理器层（Manager）**：全局服务与状态管理，位于`manager/`目录

## 2. 核心组件

### 2.1 状态管理

项目使用 GetX 进行状态管理：

- **全局状态**：通过 `GlobalState` 管理全局状态（如当前用户）
- **模块状态**：各模块通过 `GetxController` 管理自身状态
- **响应式编程**：使用 `Rx` 变量和 `Obx` 组件实现响应式UI更新

### 2.2 WebSocket通信

- **WsManager**：WebSocket连接管理，负责：
    - 连接建立与维护（自动重连、心跳检测）
    - 消息发送与接收
    - 事件订阅机制（通过 `on`/`off` 方法）

### 2.3 应用初始化

`AppInitializer` 负责应用启动时的初始化流程：

1. 日志系统初始化
2. Firebase初始化
3. 用户认证检查
4. 服务注册（如MessageService）
5. WebSocket连接（如果用户已登录）

## 3. 开发规范

### 3.1 代码组织

- **模块化**：功能按模块组织，每个模块使用MVCS结构
- **单一职责**：每个类/文件只负责一个功能点
- **依赖注入**：使用GetX的依赖注入管理服务和控制器实例

### 3.2 命名规范

- **文件命名**：使用小写下划线命名法（snake_case），如 `home_controller.dart`
- **类命名**：使用大驼峰命名法（PascalCase），如 `HomeController`
- **变量/方法**：使用小驼峰命名法（camelCase），如 `sendMessage()`
- **私有成员**：使用下划线前缀，如 `_wsManager`

### 3.3 代码风格

- 遵循 Dart 官方格式化规范
- 使用 `final` 声明不可变变量
- 方法体保持简洁，单一职责
- 添加必要的注释，特别是公共API和复杂逻辑

## 4. 开发流程

### 4.1 功能开发流程

1. **需求分析**：明确功能需求和交互流程
2. **模型设计**：定义数据模型（Model）
3. **仓库设计**：定义数据仓库（Repository）
4. **服务实现**：开发服务层逻辑（Service）
5. **控制器开发**：实现业务逻辑和状态管理（Controller）
6. **UI实现**：开发用户界面（View）
7. **绑定注册**：配置依赖注入（Binding）
8. **测试与优化**：功能测试和性能优化

### 4.2 Git工作流

1. 从主分支创建功能分支：`feat-xxx`或`fix-xxx`
2. 完成开发后提交代码并创建合并请求
3. 代码审核通过后合并到主分支

## 5. 如何添加新功能模块

### 5.1 创建新模块目录结构

在 lib/modules/ 下创建新模块目录，例如 profile ：

### 5.2 定义数据模型

在 model/profile.dart 中定义数据模型：

### 5.3 实现数据层
创建 service/profile_repository.dart 文件，实现数据层逻辑：

### 5.4 实现服务层

在 service/profile_service.dart 中实现服务层：

### 5.5 实现控制器

在 controller/profile_controller.dart 中实现控制器：

### 5.6 实现视图

在 view/profile_page.dart 中实现视图：

### 5.7 实现绑定

在 binding/profile_binding.dart 中实现绑定：

### 5.8 注册路由

在 lib/routes/routes.dart 中添加新路由：
在 lib/routes/router_manager.dart 中注册路由：

## 6. 环境配置

项目支持多环境配置，通过 --dart-define=APP_ENV=xxx 参数指定：

- debug : 开发环境配置
- test : 测试环境配置
- release : 生产环境配置
  每个环境的具体配置在 lib/env.dart 文件中定义，包括服务器地址、API端点等。

### 6.1 自定义环境变量

如需添加新的环境变量，在 lib/env.dart 中修改：

## 7. 调试与测试

### 7.1 日志系统

使用 `LogUtil` 进行日志记录：

```dart
LogUtil.info("信息日志");
LogUtil.error('错误日志');
```

### 7.2 WebSocket调试

可通过观察 `WsManager` 中的日志了解WebSocket通信状态：

- 连接状态
- 消息发送/接收
- 心跳状态
- 重连尝试

### 7.3 运行测试环境

```bash
flutter run --dart-define=APP_ENV=test
```

## . 常见问题与解决方案

### 8.1 WebSocket连接问题

- 检查服务器地址配置（ env.dart ）
- 确认网络连接状态
- 查看 WsManager 日志了解具体错误
- 检查是否正确初始化 WsManager

### 8.2 状态管理问题

- 确保使用 Rx 变量存储响应式状态
- 使用 Obx 或 GetX 组件监听状态变化
- 检查控制器是否正确注册和绑定
- 确保在 onInit 中初始化必要的状态

### 8.3 依赖注入问题

- 确保服务在使用前已通过 Get.put() 注册
- 使用 Get.find() 获取已注册的服务实例
- 检查 Binding 类是否正确配置
- 确保路由注册时包含了正确的 Binding

### 8.4 路由导航问题

- 确保路由名称在 Routes 类中定义
- 确保路由在 RouteManager 中注册
- 检查路由参数是否正确传递
- 使用 Get.arguments 获取路由参数

### 8.5 UI更新问题

- 确保使用 Obx 或 GetX 监听响应式变量
- 确保在修改响应式变量时使用 .value 属性
- 检查是否在正确的生命周期方法中更新状态
- 使用 update() 方法强制更新非响应式UI

## 9. WebSocket连接管理

### 9.1 WebSocket架构

Rolio应用的WebSocket通信架构分为两层：

- **WsManager**：底层连接管理，负责WebSocket连接的建立、维护、消息收发和事件分发
- **WebSocketConnectionService**：业务层封装，负责会话管理、重连策略和应用状态监听

### 9.2 连接生命周期

WebSocket连接的生命周期状态由`WsConnectionState`枚举定义：

- **disconnected**：连接已断开
- **connecting**：正在建立连接
- **connected**：连接已建立
- **reconnecting**：正在尝试重新连接
- **failed**：连接失败，无法自动恢复

### 9.3 自动重连机制

应用实现了多层次的自动重连机制：

1. **网络状态监听**：通过`connectivity_plus`监听网络状态变化，在网络恢复时自动重连
2. **应用生命周期监听**：通过`WidgetsBindingObserver`监听应用前后台切换，在应用恢复前台时尝试重连
3. **指数退避算法**：重连间隔随尝试次数增加而延长，避免频繁重连
4. **心跳检测**：定期发送心跳包，及时发现连接断开并触发重连

```dart
// 指数退避重连示例
final backoffMs = _initialReconnectInterval.inMilliseconds * 
    pow(_backoffFactor, _reconnectAttempts - 1).toInt();
final reconnectMs = min(backoffMs, _maxReconnectInterval.inMilliseconds);
```

### 9.4 心跳机制

为维持WebSocket连接活跃并及时发现连接断开：

1. 每30秒发送一次心跳包（ping事件）
2. 设置90秒的心跳超时计时器
3. 收到pong响应后重置超时计时器
4. 超时未收到响应则判定连接断开，触发重连

### 9.5 应用状态监听

应用实现了对前后台切换的监听：

```dart
@override
void didChangeAppLifecycleState(AppLifecycleState state) {
  switch (state) {
    case AppLifecycleState.resumed:
      // 应用恢复到前台，尝试重新连接WebSocket
      if (!isConnected && _lastConversationId > 0) {
        _tryReconnect();
      }
      break;
    case AppLifecycleState.paused:
      // 应用进入后台，记录当前状态
      break;
  }
}
```

## 10. 应用生命周期管理

### 10.1 生命周期状态

Flutter应用的生命周期状态由`AppLifecycleState`定义：

- **resumed**：应用在前台可见且可交互
- **inactive**：应用在前台但不可交互（如接到电话）
- **paused**：应用在后台不可见
- **detached**：应用仍在运行但与宿主视图分离
- **hidden**：应用被隐藏但仍在运行

### 10.2 监听生命周期

通过实现`WidgetsBindingObserver`接口监听应用生命周期：

```dart
class MyService extends GetxService with WidgetsBindingObserver {
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
  }
  
  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 处理生命周期状态变化
  }
}
```

### 10.3 资源管理策略

- **resumed**：恢复网络连接、刷新数据、重新订阅事件
- **paused**：保存状态、减少网络请求、暂停非必要操作
- **inactive**：暂停敏感操作、保护用户数据
- **detached**：保存关键状态、释放非必要资源

## 11. 性能优化

### 11.1 UI性能优化

- **虚拟化列表**：使用`VirtualizedMessageList`实现消息列表虚拟化，仅渲染可见区域
- **图片优化**：使用`CachedNetworkImage`缓存图片，减少网络请求
- **延迟加载**：使用`FutureBuilder`和`StreamBuilder`实现数据异步加载
- **防抖与节流**：对频繁操作（如滚动加载、输入搜索）应用防抖/节流策略

### 11.2 网络优化

- **连接复用**：WebSocket连接复用，避免频繁建立连接
- **数据缓存**：使用`CacheManager`缓存网络数据，减少重复请求
- **增量更新**：仅传输变更数据，减少传输量
- **压缩传输**：对大型数据使用压缩传输

### 11.3 内存管理

- **资源释放**：确保不再使用的资源（如Timer、Stream订阅）被及时释放
- **图片缓存控制**：限制图片缓存大小，避免内存溢出
- **延迟加载**：大型资源采用延迟加载策略
- **弱引用缓存**：对非关键数据使用弱引用缓存

## 12. 错误处理与恢复

### 12.1 全局错误处理

应用实现了多层次的错误处理机制：

- **UI层**：使用`ErrorWidget.builder`自定义错误展示
- **业务层**：使用`ErrorHandler`统一处理业务错误
- **网络层**：使用`Dio`拦截器处理HTTP错误
- **WebSocket层**：实现自动重连和错误恢复

### 12.2 错误上报

- **Firebase Crashlytics**：记录崩溃和异常
- **自定义日志**：使用`LogUtil`记录关键错误
- **用户反馈**：提供错误反馈界面

### 12.3 恢复策略

- **自动重试**：网络请求失败自动重试
- **状态恢复**：应用重启后恢复上次状态
- **数据持久化**：关键数据本地持久化，确保不丢失