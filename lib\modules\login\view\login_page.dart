import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/login/controller/login_controller.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

/// 登录页面
class LoginPage extends StatelessWidget {
  /// 登录页面构造函数
  const LoginPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final loginController = Get.find<LoginController>();
    
    return Scaffold(
      backgroundColor: Colors.black, // 纯黑色背景
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() => SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              // 标题
              Text(
                loginController.isRegisterMode.value ? 'Register' : 'Login',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              // 提示文字
              Text(
                loginController.isRegisterMode.value 
                    ? 'Create a new account' 
                    : 'Sign in with email and password',
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
              
              // 移除游客状态显示部分
              
              const SizedBox(height: 40),
              
              // 邮箱输入框
              TextField(
                controller: loginController.emailController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Email Address',
                  hintStyle: const TextStyle(color: Colors.grey),
                  filled: true,
                  fillColor: Colors.grey.withOpacity(0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 16),
              
              // 密码输入框
              Obx(() => TextField(
                controller: loginController.passwordController,
                obscureText: !loginController.showPassword.value,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Password',
                  hintStyle: const TextStyle(color: Colors.grey),
                  filled: true,
                  fillColor: Colors.grey.withOpacity(0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  suffixIcon: IconButton(
                    icon: Icon(
                      loginController.showPassword.value ? Icons.visibility : Icons.visibility_off,
                      color: Colors.grey,
                    ),
                    onPressed: loginController.togglePasswordVisibility,
                  ),
                ),
              )),
              
              // 注册模式下显示确认密码和用户名
              if (loginController.isRegisterMode.value) ...[
                const SizedBox(height: 16),
                // 确认密码输入框
                Obx(() => TextField(
                  controller: loginController.confirmPasswordController,
                  obscureText: !loginController.showConfirmPassword.value,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Confirm Password',
                    hintStyle: const TextStyle(color: Colors.grey),
                    filled: true,
                    fillColor: Colors.grey.withOpacity(0.1),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    suffixIcon: IconButton(
                      icon: Icon(
                        loginController.showConfirmPassword.value ? Icons.visibility : Icons.visibility_off,
                        color: Colors.grey,
                      ),
                      onPressed: loginController.toggleConfirmPasswordVisibility,
                    ),
                  ),
                )),

              ],
              
              const SizedBox(height: 24),
              
              // 登录/注册按钮
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: loginController.isLoading.value
                      ? null
                      : () {
                          if (loginController.isRegisterMode.value) {
                            loginController.registerAccount();
                          } else {
                            loginController.loginWithEmailPassword();
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: loginController.isLoading.value
                      ? const SpinKitThreeBounce(
                          color: Colors.grey,
                          size: 24.0,
                        )
                      : Text(
                          loginController.isRegisterMode.value ? 'Register' : 'Login',
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                ),
              ),
              
              // 切换登录/注册模式
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      loginController.isRegisterMode.value
                          ? 'Already have an account?'
                          : 'Don\'t have an account?',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    TextButton(
                      onPressed: loginController.toggleMode,
                      child: Text(
                        loginController.isRegisterMode.value ? 'Login' : 'Register',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 社交登录选项已删除
            ],
          ),
        ),
      )),
    );
  }

  // 社交登录选项方法已删除
}
