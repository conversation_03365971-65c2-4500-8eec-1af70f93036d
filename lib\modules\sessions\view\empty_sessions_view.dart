import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/home/<USER>/home_controller.dart';

class EmptySessionsView extends StatelessWidget {
  final VoidCallback? onActionPressed;
  
  const EmptySessionsView({
    Key? key,
    this.onActionPressed,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey.shade600,
          ),
          const SizedBox(height: 24),
          Text(
            'No Chat History',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start chatting with AI Role',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: onActionPressed ?? () {
              // 获取HomeController并切换到推荐标签页（索引0）
              try {
                final homeController = Get.find<HomeController>();
                homeController.changeTabIndex(0);
              } catch (e) {
                // 如果无法获取HomeController，则不做任何操作
                print('无法获取HomeController: $e');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: const Text(
              'Explore Roles',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
} 