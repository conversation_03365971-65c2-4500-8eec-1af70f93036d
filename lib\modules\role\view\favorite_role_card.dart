import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/common/models/ai_role.dart';

class FavoriteRoleCard extends StatefulWidget {
  final AiRole role;
  final VoidCallback onTap;
  final VoidCallback onFavoriteTap;
  
  const FavoriteRoleCard({
    Key? key,
    required this.role,
    required this.onTap,
    required this.onFavoriteTap,
  }) : super(key: key);
  
  @override
  State<FavoriteRoleCard> createState() => _FavoriteRoleCardState();
}

class _FavoriteRoleCardState extends State<FavoriteRoleCard> with SingleTickerProviderStateMixin {
  // 卡片缩放动画控制器
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  
  // 是否正在按下
  bool _isPressed = false;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    // 创建缩放动画
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeInOut,
      ),
    );
  }
  
  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // 使用AnimatedBuilder实现缩放效果
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        child: GestureDetector(
          onTapDown: (_) {
            if (!_isPressed) {
              _isPressed = true;
              _scaleController.forward();
            }
          },
          onTapUp: (_) {
            if (_isPressed) {
              _isPressed = false;
              _scaleController.reverse().then((_) {
                widget.onTap();
              });
            }
          },
          onTapCancel: () {
            if (_isPressed) {
              _isPressed = false;
              _scaleController.reverse();
            }
          },
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(16.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    // 头像
                    Hero(
                      tag: 'role_avatar_${widget.role.id}',
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16.0),
                        child: SizedBox(
                          width: 60,
                          height: 60,
                          child: CachedNetworkImage(
                            imageUrl: widget.role.avatarUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[800],
                              child: const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white54,
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[800],
                              child: const Icon(Icons.smart_toy_outlined, color: Colors.white70),
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // 角色信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 名字
                          Text(
                            widget.role.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // 描述
                          Text(
                            widget.role.description,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.white70,
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // 收藏按钮
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(20),
                        onTap: widget.onFavoriteTap,
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Icon(
                            widget.role.isFavorited ? Icons.favorite : Icons.favorite_border,
                            color: widget.role.isFavorited ? Colors.red : Colors.white70,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 4),
                    
                    // 箭头图标
                    const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white54,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 构建封面图片
  Widget _buildCoverImage() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
      ),
      child: CachedNetworkImage(
        imageUrl: widget.role.coverUrl,
        fit: BoxFit.cover, // 确保图片填充整个区域而不变形
        placeholder: (context, url) => Container(
          color: Colors.grey[900],
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.white,
            ),
          ),
        ),
        errorWidget: _buildErrorWidget,
        fadeInDuration: const Duration(milliseconds: 200),
      ),
    );
  }
  
  /// 构建错误显示组件
  Widget _buildErrorWidget(BuildContext context, String url, dynamic error) {
    return Container(
      color: Colors.grey[800],
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.smart_toy_outlined, 
            size: 48, 
            color: Colors.white
          ),
          SizedBox(height: 8),
          Text(
            'Failed to load image',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
} 