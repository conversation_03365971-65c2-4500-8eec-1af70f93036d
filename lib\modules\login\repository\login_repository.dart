import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/user/repository/user_repository.dart';
import 'package:get/get.dart';

/// 登录仓库
class LoginRepository {
  /// Firebase Auth实例
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  /// Google登录实例
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
    // 可以在Firebase控制台获取Web客户端ID
    // serverClientId: '你的Web客户端ID',
  );
  
  /// Facebook登录实例
  final FacebookAuth _facebookAuth = FacebookAuth.instance;
  
  /// 邮箱密码登录
  Future<UserCredential> loginWithEmailPassword(String email, String password) async {
    try {
      // 直接使用邮箱密码登录，不考虑匿名用户状态
      // 如果当前有匿名用户登录，会被新登录的用户替换
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      LogUtil.info('邮箱密码登录成功: ${userCredential.user?.uid}');
      return userCredential;
    } catch (e) {
      LogUtil.error('邮箱密码登录失败: $e');
      rethrow;
    }
  }
  
  /// 邮箱密码注册
  Future<UserCredential> registerWithEmailPassword(String email, String password) async {
    try {
      // 判断当前是否已经有匿名登录
      final currentUser = _auth.currentUser;
      final isAnonymous = currentUser?.isAnonymous ?? false;
      
      UserCredential userCredential;
      
      if (isAnonymous && currentUser != null) {
        // 如果是匿名用户，则将匿名账号升级为正式账号
        // 创建邮箱密码凭证
        final credential = EmailAuthProvider.credential(
          email: email,
          password: password,
        );
        
        try {
          // 链接邮箱账号到匿名账号
          userCredential = await currentUser.linkWithCredential(credential);
          LogUtil.info('匿名用户已升级为正式账号: ${userCredential.user?.uid}, 邮箱: $email');
          
          // 为新注册的用户设置随机头像（如果没有头像）
          await _setRandomAvatarForNewUser(userCredential.user);
          
        } on FirebaseAuthException catch (e) {
          // 如果邮箱已被使用，则无法链接
          if (e.code == 'email-already-in-use') {
            LogUtil.error('注册失败: 该邮箱已被使用');
            throw Exception('该邮箱已被注册，请使用其他邮箱或直接登录');
          } else {
            rethrow;
          }
        }
      } else {
        // 创建全新账号
        userCredential = await _auth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );
        LogUtil.info('新用户注册成功: ${userCredential.user?.uid}, 邮箱: $email');
        
        // 为新注册的用户设置随机头像
        await _setRandomAvatarForNewUser(userCredential.user);
      }
      
      return userCredential;
    } catch (e) {
      LogUtil.error('邮箱密码注册失败: $e');
      rethrow;
    }
  }
  
  /// 为新用户设置随机头像
  Future<void> _setRandomAvatarForNewUser(User? user) async {
    try {
      if (user == null) return;
      
      // 如果用户没有头像，则设置一个随机头像
      if (user.photoURL == null || user.photoURL!.isEmpty) {
        // 使用UserAvatarRepository设置随机头像
        final avatarRepo = Get.put(UserAvatarRepository());
        await avatarRepo.setRandomAvatarIfNeeded();
        LogUtil.info('已为新注册用户设置随机头像');
      }
    } catch (e) {
      LogUtil.error('设置随机头像失败: $e');
      // 不抛出异常，因为这是非关键功能
    }
  }

  
  /// Google登录
  Future<UserCredential> loginWithGoogle() async {
    try {
      // 判断当前是否已经有匿名登录
      final currentUser = _auth.currentUser;
      final isAnonymous = currentUser?.isAnonymous ?? false;
      
      // 触发Google登录流程
      // 确保退出之前的Google登录状态
      await _googleSignIn.signOut();
      
      // 尝试登录，如果失败会抛出具体的异常
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google login was cancelled or failed');
      }
      
      // 获取Google授权凭证
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens, please check app configuration');
      }
      
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      UserCredential userCredential;
      
      if (isAnonymous && currentUser != null) {
        try {
          // 如果是匿名用户，则链接Google账号
          userCredential = await currentUser.linkWithCredential(credential);
          LogUtil.info('Anonymous user linked with Google account: ${userCredential.user?.uid}');
        } on FirebaseAuthException catch (e) {
          // 如果账户已存在，则直接使用该账户登录
          if (e.code == 'credential-already-in-use') {
            LogUtil.info('Google account already exists, will sign in directly');
            userCredential = await _auth.signInWithCredential(credential);
          } else {
            LogUtil.error('Failed to link Google account: ${e.code} - ${e.message}');
            throw Exception('Failed to link Google account: ${e.message ?? "Unknown error"}');
          }
        }
      } else {
        // 否则直接登录
        try {
          userCredential = await _auth.signInWithCredential(credential);
          LogUtil.info('Google login successful: ${userCredential.user?.uid}');
        } catch (e) {
          LogUtil.error('Failed to sign in with Google credential: $e');
          throw Exception('Google account login failed, please try again later');
        }
      }
      
      // 确保获取到用户资料
      if (userCredential.user != null && (userCredential.user!.displayName == null || userCredential.user!.displayName!.isEmpty)) {
        // 更新用户资料
        await userCredential.user!.updateDisplayName(googleUser.displayName);
        if (googleUser.photoUrl != null && googleUser.photoUrl!.isNotEmpty) {
          await userCredential.user!.updatePhotoURL(googleUser.photoUrl);
        } else {
          // 如果Google账号没有提供头像，设置一个随机头像
          await _setRandomAvatarForNewUser(userCredential.user);
        }
        LogUtil.info('Updated Google user profile');
      }
      
      return userCredential;
    } catch (e) {
      LogUtil.error('Google login failed: $e');
      
      // 提供更详细的错误信息
      if (e is Exception) {
        rethrow; // 已经是格式化过的异常，直接抛出
      } else {
        throw Exception('Google login service error, please try again later');
      }
    }
  }
  
  /// 检查GoogleSignIn是否可用
  Future<bool> _checkGoogleSignInAvailable() async {
    try {
      // 不再调用canAccessScopes方法，因为它在某些平台上未实现
      // 直接返回true，我们将在实际的登录流程中检测是否可用
      LogUtil.info('跳过GoogleSignIn可用性检查，直接尝试登录流程');
      return true;
    } catch (e) {
      LogUtil.error('检查GoogleSignIn可用性失败: $e');
      return false;
    }
  }
  
  /// Facebook登录
  Future<UserCredential> loginWithFacebook() async {
    try {
      // 判断当前是否已经有匿名登录
      final currentUser = _auth.currentUser;
      final isAnonymous = currentUser?.isAnonymous ?? false;
      
      // 触发Facebook登录流程
      final LoginResult loginResult = await _facebookAuth.login();
      if (loginResult.status != LoginStatus.success) {
        throw Exception('Facebook登录失败: ${loginResult.message}');
      }
      
      // 获取Facebook访问令牌
      final AccessToken? accessToken = loginResult.accessToken;
      if (accessToken == null) {
        throw Exception('无法获取Facebook访问令牌');
      }
      
      // 创建Facebook凭证
      final credential = FacebookAuthProvider.credential(accessToken.token);
      
      UserCredential userCredential;
      
      if (isAnonymous && currentUser != null) {
        try {
          // 如果是匿名用户，则链接Facebook账号
          userCredential = await currentUser.linkWithCredential(credential);
          LogUtil.info('匿名用户已链接Facebook账号: ${userCredential.user?.uid}');
        } on FirebaseAuthException catch (e) {
          // 如果账户已存在，则直接使用该账户登录
          if (e.code == 'credential-already-in-use') {
            LogUtil.info('Facebook账号已存在，将直接登录');
            userCredential = await _auth.signInWithCredential(credential);
          } else {
            rethrow;
          }
        }
      } else {
        // 否则直接登录
        userCredential = await _auth.signInWithCredential(credential);
        LogUtil.info('Facebook登录成功: ${userCredential.user?.uid}');
      }
      
      // 获取Facebook用户资料，如果头像为空则设置随机头像
      if (userCredential.user != null) {
        final userData = await _facebookAuth.getUserData();
        final String? pictureUrl = userData['picture']?['data']?['url'] as String?;
        
        if ((userCredential.user!.photoURL == null || userCredential.user!.photoURL!.isEmpty) &&
            (pictureUrl == null || pictureUrl.isEmpty)) {
          // 如果用户头像为空，设置随机头像
          await _setRandomAvatarForNewUser(userCredential.user);
        } else if (pictureUrl != null && pictureUrl.isNotEmpty) {
          // 使用Facebook提供的头像
          await userCredential.user!.updatePhotoURL(pictureUrl);
        }
      }
      
      return userCredential;
    } catch (e) {
      LogUtil.error('Facebook登录失败: $e');
      rethrow;
    }
  }
  
  /// 获取当前用户
  User? getCurrentUser() {
    return _auth.currentUser;
  }
  
  /// 匿名登录
  Future<UserCredential> signInAnonymously() async {
    try {
      // 检查是否已经有匿名用户登录
      final currentUser = _auth.currentUser;
      if (currentUser != null && currentUser.isAnonymous) {
        // 已有匿名用户登录，不再重新调用API
        LogUtil.info('已存在匿名登录用户: ${currentUser.uid}，避免重复调用Firebase API');
        
        // Firebase不允许直接创建UserCredential，但我们可以设计一个解决方案
        // 1. 返回一个模拟的Future，避免实际API调用
        return Future.value(_getMockedCredentialFromUser(currentUser));
      }
      
      // 没有匿名用户，创建新匿名账号
      final credential = await _auth.signInAnonymously();
      LogUtil.info('匿名登录成功: ${credential.user?.uid}');
      
      // 为匿名用户设置随机头像
      await _setRandomAvatarForNewUser(credential.user);
      
      return credential;
    } catch (e) {
      LogUtil.error('匿名登录失败: $e');
      rethrow;
    }
  }
  
  /// 从现有用户获取UserCredential的解决方案
  /// 
  /// 由于Firebase不允许直接创建UserCredential，我们通过Firebase API获取
  /// 但这里使用一个单独方法，便于未来替换为更优的解决方案
  Future<UserCredential> _getMockedCredentialFromUser(User user) async {
    // 当前只能通过重新验证用户来获取UserCredential
    // 实际上对于匿名用户，这不会产生新的匿名用户，只会返回当前用户的凭证
    return await _auth.signInAnonymously();
  }
  
  /// 确保匿名登录
  /// 
  /// 如果当前没有用户登录，则创建匿名用户
  /// 如果已经有用户登录（包括匿名用户），则直接返回当前用户
  Future<User?> ensureAnonymousLogin() async {
    try {
      // 检查当前是否有用户登录
      User? currentUser = _auth.currentUser;
      
      // 如果没有用户登录，执行匿名登录
      if (currentUser == null) {
        final userCredential = await signInAnonymously();
        currentUser = userCredential.user;
      } else if (currentUser.isAnonymous && (currentUser.photoURL == null || currentUser.photoURL!.isEmpty)) {
        // 如果是匿名用户且没有头像，设置随机头像
        await _setRandomAvatarForNewUser(currentUser);
      }
      
      return currentUser;
    } catch (e) {
      LogUtil.error('确保匿名登录失败: $e');
      rethrow;
    }
  }
  
  /// 获取用户Token
  Future<String?> getIdToken({bool forceRefresh = false}) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return null;
      }
      
      return await currentUser.getIdToken(forceRefresh);
    } catch (e) {
      LogUtil.error('获取用户Token失败: $e');
      rethrow;
    }
  }

  /// 将邮箱凭证链接到当前用户
  Future<UserCredential> linkWithEmail(String email, String password) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('没有当前登录用户');
      }
      
      final credential = EmailAuthProvider.credential(
        email: email,
        password: password,
      );
      
      final userCredential = await currentUser.linkWithCredential(credential);
      LogUtil.info('用户已链接邮箱: ${userCredential.user?.email}');
      
      // 如果用户没有头像，设置随机头像
      await _setRandomAvatarForNewUser(userCredential.user);
      
      return userCredential;
    } catch (e) {
      LogUtil.error('链接邮箱失败: $e');
      rethrow;
    }
  }
}