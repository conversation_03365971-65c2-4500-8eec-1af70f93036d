/// 账户服务接口
/// 
/// 定义账户相关的核心功能
abstract class IAccountService {
  /// 刷新用户token
  /// 
  /// 返回刷新后的token信息
  Future<Map<String, dynamic>?> refreshToken();
  
  /// 游客注册（预留接口）
  /// 
  /// [deviceId] 设备ID
  /// 返回注册结果
  Future<Map<String, dynamic>?> guestRegister(String deviceId);
  
  /// 获取用户信息
  /// 
  /// 返回当前用户信息
  Future<Map<String, dynamic>?> getUserInfo();
  
  /// 登出
  /// 
  /// 清理用户相关数据
  Future<bool> logout();
}