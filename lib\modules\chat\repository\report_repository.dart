import 'package:rolio/common/constants/http_url_constants.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/env.dart';
import 'package:rolio/modules/chat/model/report.dart';
import 'package:uuid/uuid.dart';

/// 举报仓库
///
/// 负责处理举报相关的网络请求
class ReportRepository {
  // 最大重试次数
  static const int _maxRetries = 3;

  // 获取AI服务基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;

  /// 提交举报
  /// 
  /// 使用独立的HTTP请求提交举报，与WebSocket连接状态无关。
  /// 包含重试机制，确保在网络不稳定时也能尽可能提交成功。
  /// 添加幂等性设计，使用客户端生成的唯一ID确保不会重复提交相同的举报。
  Future<bool> submitReport({
    required int roleId,
    required String reason,
    required String description,
  }) async {
    int retryCount = 0;
    
    // 构建完整URL
    final String fullUrl = "$_baseUrl${HttpUrl.reportApiPath}";
    
    // 生成唯一的请求ID，用于幂等性处理
    final String requestId = const Uuid().v4();
    LogUtil.info('生成举报请求ID: $requestId');
    
    while (retryCount < _maxRetries) {
      try {
        // 创建请求数据，包含唯一ID确保幂等性
        final Map<String, dynamic> data = {
          'role_id': roleId,
          'reason': reason,
          'description': description,
          'request_id': requestId, // 添加请求ID用于幂等性处理
        };
        
        LogUtil.info('提交举报请求(尝试 ${retryCount + 1}/$_maxRetries): 请求ID=$requestId');
        LogUtil.debug('完整请求URL: $fullUrl');
        
        // 发送请求，允许重复请求，设置较长超时
        final response = await HttpManager.post(
          url: fullUrl,  // 使用完整URL
          body: data,
          allowDuplicate: true,
        );
        
        // 检查响应
        if (response.isSuccess) {
          LogUtil.info('举报提交成功: 请求ID=$requestId, 响应=${response.rawData}');
          return true;
        } else {
          // 特殊处理：如果服务器返回409 Conflict或其他表明重复提交的状态码
          // 根据实际API设计可能需要调整这个条件
          if (response.code == 409 || (response.rawData != null && 
              response.rawData.toString().contains('duplicate'))) {
            LogUtil.info('举报已提交(重复请求): 请求ID=$requestId');
            return true; // 视为成功，因为举报已经被处理过
          }
          
          LogUtil.error('举报提交失败: ${response.code}, ${response.msg}');
          // 当服务器返回明确的错误时，不再重试
          if (response.code != 0) {
            return false;
          }
          // 其他情况可能是网络问题，进行重试
          retryCount++;
        }
      } catch (e) {
        LogUtil.error('提交举报异常(尝试 ${retryCount + 1}/$_maxRetries): $e');
        retryCount++;
        
        if (retryCount < _maxRetries) {
          // 等待一段时间后重试，使用指数退避策略
          final backoffSeconds = retryCount * 2;
          LogUtil.info('等待${backoffSeconds}秒后重试...');
          await Future.delayed(Duration(seconds: backoffSeconds));
        }
      }
    }
    
    return false;  // 所有重试均失败
  }
  
  /// 获取用户举报历史
  Future<List<Report>> getReportHistory() async {
    try {
      // 构建完整URL
      final String fullUrl = "$_baseUrl${HttpUrl.reportApiPath}";
      
      final response = await HttpManager.get(
        url: fullUrl,  // 使用完整URL
      );
      
      if (response.isSuccess && response.data != null) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data.map((json) => Report.fromJson(json)).toList();
      }
      
      return [];
    } catch (e) {
      LogUtil.error('获取举报历史异常: $e');
      return [];
    }
  }
} 