/// 分页请求模型
/// 
/// 用于标准化分页请求参数，提高类型安全性
class PageRequest {
  /// 当前页码，从1开始
  final int page;
  
  /// 每页数量
  final int size;
  
  /// 排序字段
  final String? sortBy;
  
  /// 排序方向（升序或降序）
  final SortDirection sortDirection;
  
  /// 过滤条件
  final Map<String, dynamic> filters;
  
  /// 构造函数
  const PageRequest({
    this.page = 1,
    this.size = 10,
    this.sortBy,
    this.sortDirection = SortDirection.desc,
    this.filters = const {},
  });
  
  /// 转换为请求参数
  Map<String, dynamic> toParams() {
    final params = <String, dynamic>{
      'page': page,
      'size': size,
    };

    // 添加排序参数
    if (sortBy != null && sortBy!.isNotEmpty) {
      params['sort_by'] = sortBy;
      params['sort_direction'] = sortDirection.value;
    }

    // 添加过滤参数 - 按键排序确保一致性
    if (filters.isNotEmpty) {
      final sortedFilters = Map.fromEntries(
        filters.entries.toList()..sort((a, b) => a.key.compareTo(b.key))
      );
      params.addAll(sortedFilters);
    }

    return params;
  }
  
  /// 复制并修改
  PageRequest copyWith({
    int? page,
    int? size,
    String? sortBy,
    SortDirection? sortDirection,
    Map<String, dynamic>? filters,
  }) {
    return PageRequest(
      page: page ?? this.page,
      size: size ?? this.size,
      sortBy: sortBy ?? this.sortBy,
      sortDirection: sortDirection ?? this.sortDirection,
      filters: filters ?? this.filters,
    );
  }
  
  /// 创建下一页请求
  PageRequest nextPage() {
    return copyWith(page: page + 1);
  }
  
  /// 创建上一页请求
  PageRequest previousPage() {
    return copyWith(page: page > 1 ? page - 1 : 1);
  }
  
  /// 重置为第一页
  PageRequest resetPage() {
    return copyWith(page: 1);
  }
  
  /// 添加过滤条件
  PageRequest addFilter(String key, dynamic value) {
    final newFilters = Map<String, dynamic>.from(filters);
    newFilters[key] = value;
    return copyWith(filters: newFilters);
  }
  
  /// 移除过滤条件
  PageRequest removeFilter(String key) {
    final newFilters = Map<String, dynamic>.from(filters);
    newFilters.remove(key);
    return copyWith(filters: newFilters);
  }
}

/// 排序方向枚举
enum SortDirection {
  /// 升序
  asc('asc'),
  
  /// 降序
  desc('desc');
  
  /// 排序方向值
  final String value;
  
  /// 构造函数
  const SortDirection(this.value);
  
  /// 从字符串创建排序方向
  static SortDirection fromString(String value) {
    return SortDirection.values.firstWhere(
      (e) => e.value == value.toLowerCase(),
      orElse: () => SortDirection.desc,
    );
  }
} 