import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/theme_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/splash/controller/splash_controller.dart';
import 'package:rolio/widgets/loading_indicator.dart';

/// 应用启动加载页面
class SplashPage extends StatelessWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 使用GetX自动注入控制器
    final controller = Get.put(SplashController());
    
    return Scaffold(
      backgroundColor: ThemeColors.primaryBackgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用Logo或品牌图片
            Image.asset(
              'assets/images/app_logo.png',
              width: 120,
              height: 120,
              errorBuilder: (context, error, stackTrace) {
                LogUtil.error('Failed to load logo image: $error');
                // 如果logo加载失败，显示替代内容
                return Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: ThemeColors.primaryColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      'AI Chat',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 40),
            
            // 加载状态文本
            Obx(() => Text(
              controller.loadingStatus.value,
              style: TextStyle(
                color: ThemeColors.secondaryTextColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            )),
            
            const SizedBox(height: 24),
            
            // 进度条
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50),
              child: Obx(() => LinearProgressIndicator(
                value: controller.loadingProgress.value,
                backgroundColor: ThemeColors.borderColor,
                valueColor: AlwaysStoppedAnimation<Color>(ThemeColors.primaryColor),
                minHeight: 4,
                borderRadius: BorderRadius.circular(2),
              )),
            ),
            
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
} 