import "dart:convert";

import "package:rolio/generated/json/account_server_send_email_req_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

export 'package:rolio/generated/json/account_server_send_email_req_entity.g.dart';

@JsonSerializable()
class AccountServerSendEmailReqEntity {
  late String email;

  AccountServerSendEmailReqEntity();

  factory AccountServerSendEmailReqEntity.fromJson(Map<String, dynamic> json) =>
      $AccountServerSendEmailReqEntityFromJson(json);

  Map<String, dynamic> toJson() => $AccountServerSendEmailReqEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
