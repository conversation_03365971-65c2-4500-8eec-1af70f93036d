import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'skeleton_container.dart';

/// 卡片骨架组件
/// 
/// 用于卡片加载状态的占位UI
class SkeletonCard extends StatelessWidget {
  /// 卡片宽度
  final double? width;
  
  /// 卡片高度
  final double? height;
  
  /// 卡片圆角
  final double borderRadius;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 是否显示头像
  final bool showAvatar;
  
  /// 头像尺寸
  final double avatarSize;
  
  /// 内容行数
  final int contentLines;
  
  /// 内容行高
  final double contentLineHeight;
  
  /// 内容行间距
  final double contentLineSpacing;
  
  /// 是否显示底部操作区
  final bool showActions;
  
  /// 底部操作区高度
  final double actionsHeight;
  
  /// 构造函数
  const SkeletonCard({
    Key? key,
    this.width,
    this.height,
    this.borderRadius = 12,
    this.padding = const EdgeInsets.all(16),
    this.showAvatar = true,
    this.avatarSize = 40,
    this.contentLines = 3,
    this.contentLineHeight = 14,
    this.contentLineSpacing = 8,
    this.showActions = true,
    this.actionsHeight = 36,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Container(
        width: width,
        height: height,
        padding: padding,
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部（头像+标题）
            if (showAvatar) ...[
              Row(
                children: [
                  // 头像
                  SkeletonContainer.circular(size: avatarSize),
                  const SizedBox(width: 12),
                  // 标题和副标题
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SkeletonContainer(
                          width: double.infinity * 0.7,
                          height: 16,
                          borderRadius: 4,
                        ),
                        const SizedBox(height: 6),
                        SkeletonContainer(
                          width: double.infinity * 0.5,
                          height: 12,
                          borderRadius: 4,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // 内容
            ...List.generate(
              contentLines,
              (index) => Padding(
                padding: EdgeInsets.only(
                  bottom: index < contentLines - 1 ? contentLineSpacing : 0,
                ),
                child: SkeletonContainer(
                  width: double.infinity * (1.0 - (index * 0.1)),
                  height: contentLineHeight,
                  borderRadius: 4,
                ),
              ),
            ),
            
            // 底部操作区
            if (showActions) ...[
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SkeletonContainer(
                    width: 80,
                    height: actionsHeight,
                    borderRadius: actionsHeight / 2,
                  ),
                  SkeletonContainer(
                    width: 80,
                    height: actionsHeight,
                    borderRadius: actionsHeight / 2,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
} 