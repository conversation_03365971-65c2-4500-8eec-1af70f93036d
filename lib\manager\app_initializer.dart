import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/common/models/user.dart' as app;
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:rolio/common/services/cache_cleanup_service.dart';

/// 应用初始化器
///
/// 负责应用启动时的初始化流程
class AppInitializer {
  bool _isInitialized = false;

  /// 初始化应用（优化版本）
  Future<void> initialize() async {
    try {
      final globalState = Get.find<GlobalState>();
      globalState.setInitState(InitState.initializing);
      _isInitialized = true;
      
      // 1. 快速初始化关键服务
      await _initializeCriticalServices();
      
      // 2. 快速检查用户认证（简化版）
      await _quickAuthCheck();
      
      // 3. 标记初始化完成，允许 UI 显示
      globalState.setInitState(InitState.completed);
      
      // 4. 后台初始化非关键服务
      _initializeNonCriticalServices();
      
    } catch (e) {
      // 更新初始化状态为错误
      if (_isInitialized) {
        final globalState = Get.find<GlobalState>();
        globalState.setInitState(InitState.error);
      }
      LogUtil.error('初始化失败: $e');
      
      // 处理不同类型的异常
      String errorMessage = 'Application initialization failed';
      String errorCode = ErrorCodes.GENERAL_ERROR;
      
      if (e is AuthException) {
        errorMessage = 'Login failed, please try again';
        errorCode = ErrorCodes.AUTH_ERROR;
      } else if (e is WebSocketException) {
        errorMessage = 'Connection to server failed, please check your network';
        errorCode = ErrorCodes.WS_CONNECTION_FAILED;
      } else if (e is NetworkException) {
        errorMessage = 'Network error, please check your connection';
        errorCode = ErrorCodes.NETWORK_ERROR;
      }
      
      ErrorHandler.handleException(
        AppException(errorMessage, originalError: e, code: errorCode),
        showSnackbar: true,
      );
    }
  }
  
  /// 关键服务初始化
  Future<void> _initializeCriticalServices() async {
    // 轻量级初始化缓存清理服务（只初始化服务，不执行完整清理）
    await CacheCleanupService.init();

    // 第一阶段缓存清理：仅清理角色绑定和会话绑定
    // 确保在推荐数据获取前完成绑定清理，避免竞态条件
    await _clearRoleBindingsFirst();

    // 设置认证流程事件监听
    _setupAuthProcessListeners();
    LogUtil.info('关键服务初始化完成');
  }

  /// 快速用户认证检查
  Future<void> _quickAuthCheck() async {
    try {
      // 获取登录服务
      final loginService = Get.find<LoginService>();
      final globalState = Get.find<GlobalState>();
      
      // 检查当前是否有登录用户（快速检查）
      app.User? currentUser = await loginService.getCurrentUser();
      
      // 如果没有登录用户，则进行快速匿名登录
      if (currentUser == null) {
        LogUtil.info('没有检测到登录用户，执行匿名登录');
        currentUser = await loginService.ensureAnonymousLogin();
        
        if (currentUser == null) {
          LogUtil.error('匿名登录失败，无法获取用户信息');
          throw AuthException('匿名登录失败', code: ErrorCodes.AUTH_ERROR);
        }
      } else {
        LogUtil.info('检测到已登录用户 - 用户ID: ${currentUser.uid}, 是否匿名: ${currentUser.isAnonymous}');
        
        // 异步获取token，不阻塞启动流程
        loginService.getIdToken(forceRefresh: false).then((token) {
          if (token != null) {
            LogUtil.info('Token 已异步获取完成');
          }
        });
      }
      
      LogUtil.debug('GlobalState中的当前用户: ${globalState.currentUser.value?.uid}');
    } catch (e) {
      LogUtil.error('用户快速认证检查失败: $e');
      rethrow;
    }
  }
  
  /// 非关键服务初始化（后台执行）
  void _initializeNonCriticalServices() {
    Future.delayed(const Duration(milliseconds: 500), () async {
      try {
        // 完整缓存清理（后台执行）
        _cleanupCachesBackground();
        
        // 强制刷新token（确保最新token）
        await _refreshTokenBackground();
        
        // WebSocket连接移到后台
        await _initializeWebSocket();
        
        LogUtil.info('后台服务初始化完成');
      } catch (e) {
        LogUtil.error('后台服务初始化失败: $e');
      }
    });
  }
  
  /// 第一阶段缓存清理：仅清理角色绑定
  Future<void> _clearRoleBindingsFirst() async {
    try {
      if (Get.isRegistered<CacheCleanupService>()) {
        final cacheCleanupService = Get.find<CacheCleanupService>();
        final success = await cacheCleanupService.clearRoleBindingsOnly();
        if (success) {
          LogUtil.debug('第一阶段缓存清理完成：角色绑定已清除');
        } else {
          LogUtil.warn('第一阶段缓存清理部分失败');
        }
      }
    } catch (e) {
      LogUtil.error('第一阶段缓存清理失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 后台清理缓存（第二阶段）
  void _cleanupCachesBackground() {
    if (Get.isRegistered<CacheCleanupService>()) {
      final cacheCleanupService = Get.find<CacheCleanupService>();
      // 使用跳过绑定清理的方法，因为绑定已在第一阶段清理
      cacheCleanupService.cleanupCachesOnRestartSkipBindings().then((_) {
        LogUtil.debug('后台缓存清理完成');
      }).catchError((e) {
        LogUtil.error('后台缓存清理失败: $e');
      });
    }
  }
  
  /// 后台刷新token
  Future<void> _refreshTokenBackground() async {
    try {
      if (Get.isRegistered<LoginService>()) {
        final loginService = Get.find<LoginService>();
        await loginService.getIdToken(forceRefresh: true);
        LogUtil.debug('后台Token刷新完成');
      }
    } catch (e) {
      LogUtil.error('后台Token刷新失败: $e');
    }
  }
  
  /// 初始化全局WebSocket连接
  Future<void> _initializeWebSocket() async {
    try {
      final globalState = Get.find<GlobalState>();
      var userId = globalState.currentUser.value?.uid;
      
      // 如果userId为空，使用默认的test_user
      if (userId == null || userId.isEmpty) {
        LogUtil.warn('用户ID为空，使用默认用户ID: test_user');
        userId = 'test_user';
      }
      
      // 减少等待时间
      await Future.delayed(const Duration(milliseconds: 200));
      
      // 获取WsManager实例
      final wsManager = Get.find<WsManager>();
      
      // 异步连接，不等待结果
      wsManager.connectGlobal(userId: userId).then((connected) {
        if (connected) {
          LogUtil.info('WebSocket连接成功');
        } else {
          LogUtil.warn('WebSocket连接失败，将自动重试');
          // 5秒后重试
          Future.delayed(const Duration(seconds: 5), () {
            wsManager.reconnect();
          });
        }
      });
      
    } catch (e) {
      LogUtil.error('WebSocket初始化失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 销毁资源
  void dispose() {
    LogUtil.debug('销毁AppInitializer资源');
  }

  /// 设置认证流程事件监听
  void _setupAuthProcessListeners() {
    try {
      // 认证流程状态现在直接在GlobalState中管理，不需要额外的监听器
      LogUtil.info('认证流程状态已迁移到GlobalState统一管理');
    } catch (e) {
      LogUtil.error('设置认证流程事件监听失败: $e');
    }
  }
}
