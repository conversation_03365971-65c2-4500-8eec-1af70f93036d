import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:get/get.dart';

/// 安全存储工具类
/// 
/// 使用flutter_secure_storage进行敏感数据的安全存储
/// 提供单例模式和GetX服务注册
class SecureStorage {
  static final SecureStorage _instance = SecureStorage._internal();
  factory SecureStorage() => _instance;
  
  // flutter_secure_storage实例
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock,
    ),
  );
  
  // 存储键名常量
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  
  SecureStorage._internal();
  
  /// 初始化并注册为GetX服务
  static void init() {
    if (!Get.isRegistered<SecureStorage>()) {
      Get.put<SecureStorage>(SecureStorage._instance, permanent: true);
      LogUtil.debug('SecureStorage已初始化并注册为GetX服务');
    }
  }
  
  /// 从GetX获取单例
  static SecureStorage get to => Get.find<SecureStorage>();
  
  /// 安全存储token
  Future<void> saveToken(String token) async {
    try {
      await _secureStorage.write(key: _tokenKey, value: token);
      LogUtil.debug('Token已安全存储，长度: ${token.length}');
    } catch (e) {
      LogUtil.error('安全存储Token失败: $e');
      rethrow;
    }
  }
  
  /// 获取存储的token
  Future<String?> getToken() async {
    try {
      final token = await _secureStorage.read(key: _tokenKey);
      if (token != null) {
        LogUtil.debug('已从安全存储获取token，长度: ${token.length}');
      }
      return token;
    } catch (e) {
      LogUtil.error('获取安全存储的Token失败: $e');
      return null;
    }
  }
  
  /// 删除存储的token
  Future<void> deleteToken() async {
    try {
      await _secureStorage.delete(key: _tokenKey);
      LogUtil.debug('安全存储的Token已删除');
    } catch (e) {
      LogUtil.error('删除安全存储的Token失败: $e');
    }
  }
  
  /// 安全存储刷新token
  Future<void> saveRefreshToken(String refreshToken) async {
    try {
      await _secureStorage.write(key: _refreshTokenKey, value: refreshToken);
      LogUtil.debug('刷新Token已安全存储');
    } catch (e) {
      LogUtil.error('安全存储刷新Token失败: $e');
      rethrow;
    }
  }
  
  /// 获取存储的刷新token
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      LogUtil.error('获取安全存储的刷新Token失败: $e');
      return null;
    }
  }
  
  /// 删除存储的刷新token
  Future<void> deleteRefreshToken() async {
    try {
      await _secureStorage.delete(key: _refreshTokenKey);
      LogUtil.debug('安全存储的刷新Token已删除');
    } catch (e) {
      LogUtil.error('删除安全存储的刷新Token失败: $e');
    }
  }
  
  /// 安全存储用户ID
  Future<void> saveUserId(String userId) async {
    try {
      await _secureStorage.write(key: _userIdKey, value: userId);
      LogUtil.debug('用户ID已安全存储: $userId');
    } catch (e) {
      LogUtil.error('安全存储用户ID失败: $e');
      rethrow;
    }
  }
  
  /// 获取存储的用户ID
  Future<String?> getUserId() async {
    try {
      return await _secureStorage.read(key: _userIdKey);
    } catch (e) {
      LogUtil.error('获取安全存储的用户ID失败: $e');
      return null;
    }
  }
  
  /// 删除存储的用户ID
  Future<void> deleteUserId() async {
    try {
      await _secureStorage.delete(key: _userIdKey);
      LogUtil.debug('安全存储的用户ID已删除');
    } catch (e) {
      LogUtil.error('删除安全存储的用户ID失败: $e');
    }
  }
  
  /// 安全存储通用方法
  Future<void> saveData(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
      LogUtil.debug('数据已安全存储: $key');
    } catch (e) {
      LogUtil.error('安全存储数据失败: $key, 错误: $e');
      rethrow;
    }
  }
  
  /// 获取存储的通用数据
  Future<String?> getData(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      LogUtil.error('获取安全存储的数据失败: $key, 错误: $e');
      return null;
    }
  }
  
  /// 删除存储的通用数据
  Future<void> deleteData(String key) async {
    try {
      await _secureStorage.delete(key: key);
      LogUtil.debug('安全存储的数据已删除: $key');
    } catch (e) {
      LogUtil.error('删除安全存储的数据失败: $key, 错误: $e');
    }
  }
  
  /// 检查是否存在指定键的数据
  Future<bool> containsKey(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      return value != null;
    } catch (e) {
      LogUtil.error('检查安全存储键失败: $key, 错误: $e');
      return false;
    }
  }
  
  /// 获取所有存储的键
  Future<List<String>> getAllKeys() async {
    try {
      final allData = await _secureStorage.readAll();
      return allData.keys.toList();
    } catch (e) {
      LogUtil.error('获取所有安全存储键失败: $e');
      return [];
    }
  }
  
  /// 清除所有安全存储的数据
  Future<void> clearAll() async {
    try {
      await _secureStorage.deleteAll();
      LogUtil.debug('所有安全存储的数据已清除');
    } catch (e) {
      LogUtil.error('清除所有安全存储的数据失败: $e');
    }
  }
} 