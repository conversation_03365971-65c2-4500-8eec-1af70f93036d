import 'package:shared_preferences/shared_preferences.dart';
import 'package:rolio/common/utils/logger.dart';

/// 配置管理器
/// 
/// 统一管理应用配置，支持运行时配置和持久化配置
class ConfigManager {
  // 单例实例
  static final ConfigManager instance = ConfigManager._internal();
  
  // 内存中的配置缓存
  final Map<String, dynamic> _configCache = {};
  
  // 角色ID到会话ID的映射缓存
  final Map<String, String> _roleSessionMap = {};
  
  // SharedPreferences实例
  late SharedPreferences _prefs;
  
  // 是否已初始化
  bool _initialized = false;
  
  // 私有构造函数
  ConfigManager._internal();
  
  /// 初始化配置管理器
  Future<void> init() async {
    if (_initialized) {
      return;
    }
    
    try {
      _prefs = await SharedPreferences.getInstance();
      _initialized = true;
      LogUtil.info('ConfigManager: 初始化完成');
      
      // 加载默认配置
      _loadDefaultConfigs();
      
      // 加载角色会话映射
      _loadRoleSessionMap();
    } catch (e) {
      LogUtil.error('ConfigManager: 初始化失败: $e');
    }
  }
  
  /// 加载默认配置
  void _loadDefaultConfigs() {
    // 设置默认时区
    if (!_prefs.containsKey('timezone_offset')) {
      _prefs.setInt('timezone_offset', 8); // 默认东八区
    }
    
    // 从持久化存储加载到内存缓存
    _configCache['timezone_offset'] = _prefs.getInt('timezone_offset');
    
    LogUtil.debug('ConfigManager: 已加载默认配置');
  }
  
  /// 加载角色会话映射
  void _loadRoleSessionMap() {
    // 获取所有键
    final keys = _prefs.getKeys();
    
    // 获取所有角色会话映射的键
    final roleSessionKeys = keys.where((key) => key.startsWith('role_session_'));
    
    // 加载所有角色会话映射
    for (final key in roleSessionKeys) {
      final roleId = key.substring('role_session_'.length);
      final conversationid = _prefs.getString(key);
      if (conversationid != null) {
        _roleSessionMap[roleId] = conversationid;
      }
    }
    
    LogUtil.debug('ConfigManager: 已加载${_roleSessionMap.length}个角色会话映射');
  }
  
  /// 根据角色ID获取会话ID
  /// 
  /// [roleId] 角色ID
  /// [defaultValue] 默认值，当找不到对应的会话ID时返回
  /// 
  /// 返回对应角色的会话ID，如果不存在则返回默认值
  String getconversationidForRole(String roleId, {String defaultValue = ''}) {
    _ensureInitialized();
    
    return _roleSessionMap[roleId] ?? defaultValue;
  }
  
  /// 设置角色ID到会话ID的映射
  /// 
  /// [roleId] 角色ID
  /// [conversationid] 会话ID
  /// 
  /// 返回操作是否成功
  Future<bool> setconversationidForRole(String roleId, String conversationid) async {
    _ensureInitialized();
    
    // 更新内存映射
    _roleSessionMap[roleId] = conversationid;
    
    // 更新持久化存储
    return await _prefs.setString('role_session_$roleId', conversationid);
  }
  
  /// 移除角色ID到会话ID的映射
  /// 
  /// [roleId] 角色ID
  /// 
  /// 返回操作是否成功
  Future<bool> removeconversationidForRole(String roleId) async {
    _ensureInitialized();
    
    // 从内存映射中移除
    _roleSessionMap.remove(roleId);
    
    // 从持久化存储中移除
    return await _prefs.remove('role_session_$roleId');
  }
  
  /// 获取所有角色ID到会话ID的映射
  /// 
  /// 返回所有角色ID到会话ID的映射的副本
  Map<String, String> getAllRoleSessionMappings() {
    _ensureInitialized();
    
    // 返回副本，避免外部修改
    return Map<String, String>.from(_roleSessionMap);
  }
  
  /// 获取字符串配置
  String getString(String key, {String defaultValue = ''}) {
    _ensureInitialized();
    
    // 优先从内存缓存获取
    if (_configCache.containsKey(key)) {
      final value = _configCache[key];
      if (value is String) {
        return value;
      }
    }
    
    // 从持久化存储获取
    final value = _prefs.getString(key);
    if (value != null) {
      _configCache[key] = value;
      return value;
    }
    
    return defaultValue;
  }
  
  /// 获取整数配置
  int getInt(String key, {int defaultValue = 0}) {
    _ensureInitialized();
    
    // 优先从内存缓存获取
    if (_configCache.containsKey(key)) {
      final value = _configCache[key];
      if (value is int) {
        return value;
      }
    }
    
    // 从持久化存储获取
    final value = _prefs.getInt(key);
    if (value != null) {
      _configCache[key] = value;
      return value;
    }
    
    return defaultValue;
  }
  
  /// 获取布尔配置
  bool getBool(String key, {bool defaultValue = false}) {
    _ensureInitialized();
    
    // 优先从内存缓存获取
    if (_configCache.containsKey(key)) {
      final value = _configCache[key];
      if (value is bool) {
        return value;
      }
    }
    
    // 从持久化存储获取
    final value = _prefs.getBool(key);
    if (value != null) {
      _configCache[key] = value;
      return value;
    }
    
    return defaultValue;
  }
  
  /// 设置字符串配置
  Future<bool> setString(String key, String value) async {
    _ensureInitialized();
    
    // 更新内存缓存
    _configCache[key] = value;
    
    // 更新持久化存储
    return await _prefs.setString(key, value);
  }
  
  /// 设置整数配置
  Future<bool> setInt(String key, int value) async {
    _ensureInitialized();
    
    // 更新内存缓存
    _configCache[key] = value;
    
    // 更新持久化存储
    return await _prefs.setInt(key, value);
  }
  
  /// 设置布尔配置
  Future<bool> setBool(String key, bool value) async {
    _ensureInitialized();
    
    // 更新内存缓存
    _configCache[key] = value;
    
    // 更新持久化存储
    return await _prefs.setBool(key, value);
  }
  
  /// 移除配置
  Future<bool> remove(String key) async {
    _ensureInitialized();
    
    // 从内存缓存移除
    _configCache.remove(key);
    
    // 从持久化存储移除
    return await _prefs.remove(key);
  }
  
  /// 清除所有配置
  Future<bool> clear() async {
    _ensureInitialized();
    
    // 清除内存缓存
    _configCache.clear();
    
    // 清除角色会话映射缓存
    _roleSessionMap.clear();
    
    // 清除持久化存储
    return await _prefs.clear();
  }
  
  /// 确保已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      LogUtil.warn('ConfigManager: 尚未初始化，将使用默认值');
    }
  }
} 