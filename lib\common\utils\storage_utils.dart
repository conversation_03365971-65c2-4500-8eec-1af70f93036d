import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rolio/common/utils/logger.dart';

/// 本地存储工具类
class StorageUtil {
  /// 保存字符串
  static Future<bool> setString(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(key, value);
    } catch (e) {
      LogUtil.error('存储字符串失败: $e');
      return false;
    }
  }

  /// 获取字符串
  static Future<String?> getString(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key);
    } catch (e) {
      LogUtil.error('获取字符串失败: $e');
      return null;
    }
  }

  /// 保存布尔值
  static Future<bool> setBool(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(key, value);
    } catch (e) {
      LogUtil.error('存储布尔值失败: $e');
      return false;
    }
  }

  /// 获取布尔值
  static Future<bool?> getBool(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(key);
    } catch (e) {
      LogUtil.error('获取布尔值失败: $e');
      return null;
    }
  }

  /// 保存整数
  static Future<bool> setInt(String key, int value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setInt(key, value);
    } catch (e) {
      LogUtil.error('存储整数失败: $e');
      return false;
    }
  }

  /// 获取整数
  static Future<int?> getInt(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(key);
    } catch (e) {
      LogUtil.error('获取整数失败: $e');
      return null;
    }
  }

  /// 保存双精度浮点数
  static Future<bool> setDouble(String key, double value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setDouble(key, value);
    } catch (e) {
      LogUtil.error('存储双精度浮点数失败: $e');
      return false;
    }
  }

  /// 获取双精度浮点数
  static Future<double?> getDouble(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(key);
    } catch (e) {
      LogUtil.error('获取双精度浮点数失败: $e');
      return null;
    }
  }

  /// 保存字符串列表
  static Future<bool> setStringList(String key, List<String> value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setStringList(key, value);
    } catch (e) {
      LogUtil.error('存储字符串列表失败: $e');
      return false;
    }
  }

  /// 获取字符串列表
  static Future<List<String>?> getStringList(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(key);
    } catch (e) {
      LogUtil.error('获取字符串列表失败: $e');
      return null;
    }
  }

  /// 保存对象
  static Future<bool> setObject(String key, Object value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(key, jsonEncode(value));
    } catch (e) {
      LogUtil.error('存储对象失败: $e');
      return false;
    }
  }

  /// 获取对象
  static Future<Map<String, dynamic>?> getObject(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(key);
      if (jsonString == null) {
        return null;
      }
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      LogUtil.error('获取对象失败: $e');
      return null;
    }
  }

  /// 移除指定键的数据
  static Future<bool> remove(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(key);
    } catch (e) {
      LogUtil.error('移除数据失败: $e');
      return false;
    }
  }

  /// 清空所有数据
  static Future<bool> clear() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.clear();
    } catch (e) {
      LogUtil.error('清空数据失败: $e');
      return false;
    }
  }

  /// 获取所有键
  static Future<Set<String>> getKeys() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getKeys();
    } catch (e) {
      LogUtil.error('获取所有键失败: $e');
      return <String>{};
    }
  }

  /// 检查键是否存在
  static Future<bool> containsKey(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(key);
    } catch (e) {
      LogUtil.error('检查键是否存在失败: $e');
      return false;
    }
  }
} 