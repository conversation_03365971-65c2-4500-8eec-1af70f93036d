import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/image_preloader.dart';

/// 聊天封面图片组件
/// 
/// 专门优化的聊天封面图片组件，提供更好的加载体验
class ChatCoverImage extends StatelessWidget {
  /// 图片URL
  final String imageUrl;
  
  /// 加载占位符颜色
  final Color placeholderColor;
  
  /// 错误占位符颜色
  final Color errorColor;
  
  /// 图片宽度
  final double? width;
  
  /// 图片高度
  final double? height;
  
  /// 图片适应方式
  final BoxFit fit;
  
  /// 构造函数
  const ChatCoverImage({
    Key? key,
    required this.imageUrl,
    this.placeholderColor = Colors.grey,
    this.errorColor = Colors.red,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 清理和验证URL
    final String cleanUrl = _sanitizeImageUrl(imageUrl);
    
    // 如果URL为空或无效，显示透明占位符
    if (cleanUrl.isEmpty) {
      return Container(
        width: width,
        height: height,
        color: Colors.black, // 使用黑色背景
      );
    }

    // 预加载图片，但只在第一次构建时输出日志
    _preloadImage(cleanUrl, logOutput: false);

    // 计算屏幕高度
    final screenHeight = MediaQuery.of(context).size.height;
    
    return Stack(
      fit: StackFit.expand,
      children: [
        // 使用AspectRatio限制图片比例，保持一个合理的宽高比
        AspectRatio(
          aspectRatio: 2/3, // 使用更合理的宽高比
          child: CachedNetworkImage(
            imageUrl: cleanUrl,
            fit: BoxFit.cover,
            fadeInDuration: const Duration(milliseconds: 150),
            fadeOutDuration: Duration.zero,
            alignment: Alignment.topCenter, // 从顶部开始显示图片
            memCacheWidth: width?.toInt(),
            memCacheHeight: height?.toInt(),
            placeholder: (context, url) => Container(
              color: Colors.black,
              child: const Center(
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
              ),
            ),
            errorWidget: (context, url, error) {
              LogUtil.error('ChatCoverImage - 加载封面图片失败: $url, 错误: $error');
              return Container(
                color: Colors.black,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.image_not_supported_outlined,
                          color: Colors.white70,
                          size: 32,
                        ),
                        SizedBox(height: 4),
                        Text(
                          '封面加载失败',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        
        // 顶部渐变阴影 - 为了让AppBar内容更清晰
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          height: 100,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.6),
                  Colors.black.withOpacity(0.3),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          ),
        ),
        
        // 底部渐变阴影 - 为了让底部输入框区域更清晰
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: screenHeight * 0.3, // 底部30%高度
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  Colors.black.withOpacity(0.9), // 底部几乎不透明
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.3),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 清理和验证图片URL
  String _sanitizeImageUrl(String? url) {
    if (url == null || url.isEmpty) {
      return '';
    }
    
    // 清理URL字符串
    String cleaned = url.trim();
    
    // 移除可能的反引号或其他特殊字符
    cleaned = cleaned.replaceAll(RegExp(r'^`+|`+$'), '');
    cleaned = cleaned.replaceAll(RegExp(r'^\"+|\"+$'), '');
    cleaned = cleaned.trim();
    
    // 检查是否为"null"字符串
    if (cleaned.toLowerCase() == 'null') {
      return '';
    }
    
    // 对于没有协议前缀的URL，添加https前缀
    if (!cleaned.startsWith('http://') && !cleaned.startsWith('https://') && 
        !cleaned.startsWith('/') && !cleaned.startsWith('./') && !cleaned.startsWith('../')) {
      cleaned = 'https://' + cleaned;
      LogUtil.debug('ChatCoverImage - 自动添加https前缀: $cleaned');
    }
    
    return cleaned;
  }

  /// 预加载图片
  void _preloadImage(String cleanUrl, {bool logOutput = true}) {
    if (cleanUrl.isEmpty) return;

    final imagePreloader = Get.find<ImagePreloader>();
    
    // 检查图片是否已经预加载过，避免重复预加载
    if (!imagePreloader.isImagePreloaded(cleanUrl)) {
      imagePreloader.preloadImage(
        cleanUrl,
        priority: ImagePreloadPriority.high,
        width: width?.toInt(),
        height: height?.toInt(),
        onComplete: (success) {
          if (success) {
            if (logOutput) {
              LogUtil.debug('ChatCoverImage - 预加载成功: $cleanUrl');
            }
          } else {
            if (logOutput) {
              LogUtil.error('ChatCoverImage - 预加载失败: $cleanUrl');
            }
          }
        }
      );
    } else {
      if (logOutput) {
        LogUtil.debug('ChatCoverImage - 图片已预加载: $cleanUrl');
      }
    }
  }
} 