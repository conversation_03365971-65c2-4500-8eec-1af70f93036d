import 'package:rolio/generated/json/base/json_convert_content.dart';
import "package:rolio/modules/login/model/account_server_http_header_entity.dart";

AccountServerHttpHeaderEntity $AccountServerHttpHeaderEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerHttpHeaderEntity accountServerHttpHeaderEntity =
      AccountServerHttpHeaderEntity();
  final String? gaid = jsonConvert.convert<String>(json['gaid']);
  if (gaid != null) {
    accountServerHttpHeaderEntity.gaid = gaid;
  }
  final String? uid = jsonConvert.convert<String>(json['uid']);
  if (uid != null) {
    accountServerHttpHeaderEntity.uid = uid;
  }
  final int? cvc = jsonConvert.convert<int>(json['cvc']);
  if (cvc != null) {
    accountServerHttpHeaderEntity.cvc = cvc;
  }
  final double? svc = jsonConvert.convert<double>(json['svc']);
  if (svc != null) {
    accountServerHttpHeaderEntity.svc = svc;
  }
  final String? device = jsonConvert.convert<String>(json['device']);
  if (device != null) {
    accountServerHttpHeaderEntity.device = device;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    accountServerHttpHeaderEntity.network = network;
  }
  final String? simcode = jsonConvert.convert<String>(json['simcode']);
  if (simcode != null) {
    accountServerHttpHeaderEntity.simcode = simcode;
  }
  final String? lang = jsonConvert.convert<String>(json['lang']);
  if (lang != null) {
    accountServerHttpHeaderEntity.lang = lang;
  }
  final String? ls = jsonConvert.convert<String>(json['ls']);
  if (ls != null) {
    accountServerHttpHeaderEntity.ls = ls;
  }
  final String? pf = jsonConvert.convert<String>(json['pf']);
  if (pf != null) {
    accountServerHttpHeaderEntity.pf = pf;
  }
  final String? ip = jsonConvert.convert<String>(json['ip']);
  if (ip != null) {
    accountServerHttpHeaderEntity.ip = ip;
  }
  final String? country = jsonConvert.convert<String>(json['country']);
  if (country != null) {
    accountServerHttpHeaderEntity.country = country;
  }
  final int? appid = jsonConvert.convert<int>(json['appid']);
  if (appid != null) {
    accountServerHttpHeaderEntity.appid = appid;
  }
  return accountServerHttpHeaderEntity;
}

Map<String, dynamic> $AccountServerHttpHeaderEntityToJson(
    AccountServerHttpHeaderEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['gaid'] = entity.gaid;
  data['uid'] = entity.uid;
  data['cvc'] = entity.cvc;
  data['svc'] = entity.svc;
  data['device'] = entity.device;
  data['network'] = entity.network;
  data['simcode'] = entity.simcode;
  data['lang'] = entity.lang;
  data['ls'] = entity.ls;
  data['pf'] = entity.pf;
  data['ip'] = entity.ip;
  data['country'] = entity.country;
  data['appid'] = entity.appid;
  return data;
}

extension AccountServerHttpHeaderEntityExtension
    on AccountServerHttpHeaderEntity {
  AccountServerHttpHeaderEntity copyWith({
    String? gaid,
    String? uid,
    int? cvc,
    double? svc,
    String? device,
    String? network,
    String? simcode,
    String? lang,
    String? ls,
    String? pf,
    String? ip,
    String? country,
    int? appid,
  }) {
    return AccountServerHttpHeaderEntity()
      ..gaid = gaid ?? this.gaid
      ..uid = uid ?? this.uid
      ..cvc = cvc ?? this.cvc
      ..svc = svc ?? this.svc
      ..device = device ?? this.device
      ..network = network ?? this.network
      ..simcode = simcode ?? this.simcode
      ..lang = lang ?? this.lang
      ..ls = ls ?? this.ls
      ..pf = pf ?? this.pf
      ..ip = ip ?? this.ip
      ..country = country ?? this.country
      ..appid = appid ?? this.appid;
  }
}
