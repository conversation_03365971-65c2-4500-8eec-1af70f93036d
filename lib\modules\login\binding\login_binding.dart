import 'package:get/get.dart';
import 'package:rolio/modules/login/controller/login_controller.dart';
import 'package:rolio/modules/login/repository/login_repository.dart';
import 'package:rolio/modules/login/service/login_service.dart';

/// 登录模块绑定
class LoginBinding extends Bindings {
  @override
  void dependencies() {
    // 懒加载方式注册依赖
    Get.lazyPut<LoginRepository>(() => LoginRepository());
    Get.lazyPut<LoginService>(() => LoginService(repository: Get.find<LoginRepository>()));
    Get.lazyPut<LoginController>(() => LoginController(loginService: Get.find<LoginService>()));
  }
}