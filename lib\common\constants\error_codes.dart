/// 错误代码常量类
///
/// 定义应用中使用的统一错误代码
class ErrorCodes {
  // 通用错误
  static const String GENERAL_ERROR = 'E0001';
  static const String NETWORK_ERROR = 'E0002';
  static const String SERVER_ERROR = 'E0003';
  static const String AUTH_ERROR = 'E0004';
  static const String BUSINESS_ERROR = 'E0005';
  static const String DATA_NOT_FOUND = 'E0006';
  static const String DATA_PARSE_ERROR = 'E0007';
  static const String DATA_INCONSISTENCY = 'E0008';
  static const String DUPLICATE_REQUEST = 'E0009';
  static const String RATE_LIMIT_ERROR = 'E0010';
  static const String AUTH_IN_PROGRESS = 'E0011'; // 认证流程进行中
  static const String REQUEST_TIMEOUT = 'E0012';

  // WebSocket相关错误
  static const String WS_CONNECTION_FAILED = 'W0001';
  static const String WS_CONNECTION_LOST = 'W0002';
  static const String WS_MESSAGE_SEND_FAILED = 'W0003';
  
  // 聊天模块错误
  static const String CHAT_SEND_FAILED = 'C0001';
  static const String CHAT_HISTORY_LOAD_FAILED = 'C0002';
  static const String CHAT_START_FAILED = 'C0003';
  static const String SESSION_SWITCH_FAILED = 'C0004';
  
  // 角色模块错误
  static const String ROLE_SWITCH_FAILED = 'R0001';
  static const String ROLE_INFO_LOAD_FAILED = 'R0002';
  static const String ROLE_LIST_LOAD_FAILED = 'R0003';
  static const String FAVORITE_FAILED = 'R0004'; // 收藏角色失败
  static const String UNFAVORITE_FAILED = 'R0005'; // 取消收藏角色失败
}