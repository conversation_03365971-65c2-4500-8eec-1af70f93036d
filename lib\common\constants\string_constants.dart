class StringsConsts {
  // app name.
  static const String appName = 'Rolio';

  // firestore
  static const String usersCollection = 'users';
  static const String groupsCollection = 'groups';
  static const String statusCollection = 'status';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';

  // chat
  static const String userId = 'userId';
  static const String username = 'username';
  static const String profilePic = 'profilePic';
  static const String isGroupChat = 'isGroupChat';

  // GIF
  static const String giphyApiKey = 'XNWLf5zhEEFFuOpXcj61QtnliI4pYH3p';
  static const String staticGiphyUrlStart = 'https://i.giphy.com/media/';
  static const String staticGiphyUrlEnd = '/200.gif';


  
  // Sessions
  static const int defaultPageSize = 20;
  static const int loadMoreThreshold = 200; // 距离底部多少像素开始加载
  
  // 推荐页常量
  static const int recommendPageSize = 10;
  static const int recommendGridCrossAxisCount = 2;
  static const double recommendCardAspectRatio = 0.65; // 卡片的宽高比
  static const double recommendGridSpacing = 4.0; // 减小间距
  static const double recommendCardBorderRadius = 8.0;
  static const int recommendRefreshInterval = 30000; // 30秒刷新间隔
  static const int recommendMinItemsForLoading = 5; // 最少5个项目才显示加载状态
  static const String recommendDefaultTag = '推荐';
  static const String recommendDefaultAvatarUrl = '';
  static const String recommendDefaultCoverUrl = '';
  
  // 图片缓存常量
  static const int maxMemCacheSize = 100; // 内存缓存最大图片数量
  static const int maxDiskCacheSize = 200; // 磁盘缓存最大图片数量 (MB)
  static const Duration imageCacheDuration = Duration(days: 7); // 图片缓存过期时间
  static const int memCacheWidth = 600; // 内存缓存图片宽度，从300增加到600
  static const int memCacheHeight = 900; // 内存缓存图片高度，从300增加到900
  
  // 错误处理常量
  static const String errorGeneral = '操作失败，请稍后重试';
  static const String errorNetwork = '网络连接失败，请检查网络设置';
  static const String errorServer = '服务器错误，请稍后重试';
  static const String errorDataParsing = '数据解析错误';
  static const String errorNoData = '暂无数据';
  
  // 举报相关常量
  static const String reportSubmitSuccess = '举报已提交，感谢您的反馈';
  static const String reportSubmitFailed = '举报提交失败，请稍后重试';
  static const String reportDescriptionHint = '请详细描述问题（最多300字）';
  static const int reportMaxDescriptionLength = 300;
  static const int reportMaxImageCount = 3;
  
  // 缓存清理时间常量（毫秒）
  static const int pendingMessageCleanupInterval = 15 * 1000; // 15秒清理间隔
  static const int pendingMessageExpiry = 20 * 1000; // 20秒过期时间
  
  // 用户头像URL列表
  static const List<String> avatarUrls = [
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497742815_MilesPubrick-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497742196_MeganWalker-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497741337_Maxwell-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497740612_MaxStitch-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497739848_LilyHenley-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497738760_Grace-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497738001_EvanFox-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497737320_EthanShepherd-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497736524_Brock-new.webp',
    'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497734534_Bella-new.webp',
  ];
  
  // 默认头像URL (使用第一个头像)
  static const String defaultAvatarUrl = 'https://file.89tgame.com/cms/base-admin/308/prd/dynamic/1752810310497742815_MilesPubrick-new.webp';
}
