import 'package:get/get.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/sessions/controller/sessions_controller.dart';
import 'package:rolio/modules/sessions/repository/sessions_repository.dart';
import 'package:rolio/modules/sessions/service/session_service.dart';
import 'package:rolio/common/di/service_bindings.dart';

/// 会话模块绑定类
/// 
/// 负责会话模块相关的依赖注入和控制器绑定
class SessionsBinding implements Bindings {
  @override
  void dependencies() {
    try {
      LogUtil.info('开始注册SessionsBinding依赖...');
      
      _registerRepositories();
      _registerServices();
      _registerControllers();
      
      LogUtil.info('SessionsBinding依赖注册完成');
    } catch (e) {
      LogUtil.error('SessionsBinding依赖注册失败: $e');
      rethrow;
    }
  }
  
  void _registerRepositories() {
    if (!Get.isRegistered<SessionsRepository>()) {
      Get.put<SessionsRepository>(SessionsRepository(), permanent: true);
      LogUtil.debug('注册仓库: SessionsRepository');
    }
  }
  
  void _registerServices() {
    // 创建SessionService实例
    final sessionService = SessionService(
      repository: Get.find<SessionsRepository>(),
      globalState: Get.find<GlobalState>()
    );
    
    // 使用ServiceBindings安全替换服务
    ServiceBindings.replaceService<SessionService>(sessionService);
    ServiceBindings.replaceService<ISessionProvider>(sessionService);
    
    LogUtil.debug('注册服务: SessionService 和接口: ISessionProvider（通过ServiceBindings替换）');
  }
  
  void _registerControllers() {
    // 如果已经存在SessionsController，先移除它
    if (Get.isRegistered<SessionsController>()) {
      LogUtil.debug('移除已存在的SessionsController');
      Get.delete<SessionsController>(force: true);
    }
    
    Get.lazyPut<SessionsController>(
      () => SessionsController(sessionService: Get.find<ISessionProvider>()),
      fenix: true
    );
    LogUtil.debug('注册控制器: SessionsController (依赖ISessionProvider接口)');
  }
}