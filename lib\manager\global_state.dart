import 'package:get/get.dart';
import 'package:rolio/common/models/user.dart' as app;
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/cache/secure_storage.dart'; // 导入SecureStorage
import 'dart:math'; // 导入min函数

/// 全局状态管理
///
/// 负责管理应用的全局状态，如当前用户、认证状态等
class GlobalState extends GetxController {
  /// 获取GlobalState实例
  static GlobalState get to => Get.find<GlobalState>();
  /// 构造函数
  GlobalState() {
    _setupTokenExpirationListener();
  }

  /// 设置token过期监听器
  void _setupTokenExpirationListener() {
    // 监听自身的token过期状态变化
    ever(tokenExpired, (bool expired) {
      if (expired) {
        LogUtil.info('GlobalState检测到token过期，执行清理操作');
        // 清理所有token，该方法会同步清理SecureStorage和HttpManager中的token
        clearAccessToken().catchError((error) {
          LogUtil.error('处理token过期时清除token失败: $error');
        });

        LogUtil.info('Token清理完成，响应token过期');
      }
    });
  }

  /// 当前用户
  final Rx<app.User?> currentUser = Rx<app.User?>(null);

  /// 访问令牌
  final RxString accessToken = RxString('');

  /// 初始化状态
  final Rx<InitState> initState = Rx<InitState>(InitState.initializing);
  
  /// 认证流程状态
  final RxBool isAuthProcessing = RxBool(false);

  /// Token过期状态
  final RxBool tokenExpired = RxBool(false);

  /// Token验证状态
  final RxBool tokenValidated = RxBool(false);

  /// WebSocket连接状态
  final RxBool websocketConnected = RxBool(false);

  /// WebSocket连接允许状态
  final RxBool websocketConnectionAllowed = RxBool(false);

  /// 数据刷新触发器
  final RxBool needsRefreshRecommendData = RxBool(false);
  final RxBool needsRefreshSessionsData = RxBool(false);

  /// 最近删除的会话ID
  final RxString lastDeletedconversationid = RxString('');

  /// 当前选中的AI角色
  final Rx<AiRole?> currentAiRole = Rx<AiRole?>(null);

  /// AI是否正在生成回复
  final RxBool isAiGenerating = RxBool(false);

  /// 用户是否已登录
  bool get isLoggedIn =>
      currentUser.value != null && accessToken.value.isNotEmpty;

  // ==================== 状态管理方法 ====================

  /// 设置Token过期状态
  void setTokenExpired(bool expired) {
    tokenExpired.value = expired;
    if (expired) {
      LogUtil.info('Token已过期，执行清理操作');
      clearAccessToken().catchError((error) {
        LogUtil.error('处理token过期时清除token失败: $error');
      });
    }
  }

  /// 开始认证流程
  void startAuthProcess() {
    isAuthProcessing.value = true;
    LogUtil.info('开始认证流程');
  }

  /// 完成认证流程
  void completeAuthProcess() {
    isAuthProcessing.value = false;
    LogUtil.info('认证流程完成');
  }

  /// 设置Token验证状态
  void setTokenValidated(bool validated) {
    tokenValidated.value = validated;
    LogUtil.info('Token验证状态: $validated');
  }

  /// 设置WebSocket连接状态
  void setWebsocketConnected(bool connected) {
    websocketConnected.value = connected;
    LogUtil.info('WebSocket连接状态: $connected');
  }

  /// 设置WebSocket连接允许状态
  void setWebsocketConnectionAllowed(bool allowed) {
    websocketConnectionAllowed.value = allowed;
    LogUtil.info('WebSocket连接允许状态: $allowed');
  }

  /// 触发推荐数据刷新
  void triggerRefreshRecommendData() {
    needsRefreshRecommendData.toggle();
    LogUtil.info('触发推荐数据刷新');
  }

  /// 触发会话数据刷新
  void triggerRefreshSessionsData() {
    needsRefreshSessionsData.toggle();
    LogUtil.info('触发会话数据刷新');
  }

  /// 设置当前用户
  void setCurrentUser(app.User user) {
    currentUser.value = user;
  }

  /// 设置访问令牌（异步版本，统一入口）
  Future<void> setAccessToken(String token) async {
    if (accessToken.value == token) {
      // 如果token未变化，不重复设置
      return;
    }

    // 记录token长度，便于调试
    final tokenLength = token.length;
    LogUtil.debug('GlobalState设置token，长度: $tokenLength');

    // 1. 设置token到全局状态
    accessToken.value = token;

    // 2. 异步更新HttpManager中的token
    await HttpManager.setTokenAsync(token);
    
    // 3. 保存到安全存储
    try {
      if (Get.isRegistered<SecureStorage>()) {
        final secureStorage = Get.find<SecureStorage>();
        await secureStorage.saveToken(token);
        LogUtil.debug('已同步Token到安全存储');
      }
    } catch (e) {
      LogUtil.error('同步Token到安全存储失败: $e');
    }
  }
  


  /// 清除访问令牌（异步版本，统一入口）
  Future<void> clearAccessToken() async {
    final hadToken = accessToken.value.isNotEmpty;
    if (hadToken) {
      LogUtil.debug('清除访问令牌: ${accessToken.value.substring(0, min(10, accessToken.value.length))}...');
    }

    // 1. 清除全局状态中的token
    accessToken.value = '';

    // 2. 异步清除HttpManager中的token
    await HttpManager.clearTokenAsync();
    LogUtil.debug('已同步清除HttpManager中的token');
    
    // 3. 清除SecureStorage中的token
    try {
      if (Get.isRegistered<SecureStorage>()) {
        final secureStorage = Get.find<SecureStorage>();
        await secureStorage.deleteToken();
        LogUtil.debug('已同步清除SecureStorage中的token');
      }
    } catch (e) {
      LogUtil.error('清除SecureStorage中的Token失败: $e');
    }
  }
  


  /// 清除当前用户（异步版本，推荐使用）
  Future<void> clearCurrentUser() async {
    LogUtil.debug('清除当前用户信息和访问令牌');

    // 清除用户信息
    currentUser.value = null;

    // 清除令牌 - 使用统一的清除方法
    await clearAccessToken();

    // 清除当前AI角色
    clearCurrentAiRole();
  }
  
  

  /// 设置初始化状态
  void setInitState(InitState state) {
    initState.value = state;
  }

  /// 通知会话被删除
  void notifySessionDeleted(String conversationid) {
    lastDeletedconversationid.value = conversationid;
    // 触发事件，可以被其他控制器监听
    update(['session_deleted']);
  }

  /// 设置当前选中的AI角色
  void setCurrentAiRole(AiRole role) {
    currentAiRole.value = role;
    update(['current_ai_role']);
  }

  /// 清除当前选中的AI角色
  void clearCurrentAiRole() {
    currentAiRole.value = null;
  }

  /// 设置AI生成状态
  void setAiGeneratingState(bool isGenerating) {
    isAiGenerating.value = isGenerating;
    update(['ai_generating_state']);
  }

  /// 通知角色会话ID更新
  void notifyRoleConversationUpdate(int roleId, int conversationId) {
    // 触发事件，可以被其他控制器监听
    update(['role_conversation_updated', 'role_$roleId']);
  }
}

/// 应用初始化状态
enum InitState {
  /// 正在初始化
  initializing,

  /// 初始化完成
  completed,

  /// 初始化出错
  error,
}
