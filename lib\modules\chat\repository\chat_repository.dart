import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/http_url_constants.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/enums/message_type.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/common/utils/datetime_utils.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/env.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:uuid/uuid.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';

/// 聊天仓库
/// 
/// 负责处理聊天相关的数据操作，包括API调用和数据转换
class ChatRepository {
  // 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  

  // API基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;

  
  // 获取历史消息
  Future<List<Message>> getHistoryMessages(int conversationId, int page, int pageSize) async {
    try {
      LogUtil.debug('获取历史消息: conversationId=$conversationId, page=$page, pageSize=$pageSize');
      
      // 检查参数有效性
      if (conversationId <= 0) {
        LogUtil.warn('会话ID无效(${conversationId})，返回空消息列表');
        return [];
      }
      
      // 构建API URL
      final url = '$_baseUrl${HttpUrl.messagesPath}$conversationId';
      
      // 使用PageRequest构建查询参数
      final pageRequest = PageRequest(page: page, size: pageSize);
      final queryParams = pageRequest.toParams();
      
      LogUtil.debug('使用PageRequest获取历史消息: $queryParams');
      
      // 发送API请求
      final response = await HttpManager.get(
        url: url,
        params: queryParams,
      );
      
      if (response.isSuccess && response.rawData != null) {
        final rawData = response.rawData;
        
        // 解析API响应
        List<dynamic>? messagesList;
        
        // 检查标准API响应格式
        if (rawData is Map<String, dynamic>) {
          // 检查是否有data字段
          if (rawData.containsKey('data') && rawData['data'] is Map<String, dynamic>) {
            final data = rawData['data'] as Map<String, dynamic>;
            
            // 从data.items获取消息列表
            if (data.containsKey('items') && data['items'] is List) {
              messagesList = data['items'] as List;
            }
          }
        }
        
        // 如果没有找到消息列表，返回空列表
        if (messagesList == null || messagesList.isEmpty) {
          LogUtil.debug('没有找到历史消息或消息列表为空');
          return [];
        }
        
        // 处理每条消息
        final List<Message> historyMessages = [];
        
        for (var messageData in messagesList) {
          if (messageData is Map<String, dynamic>) {
            final message = _parseHistoryMessage(messageData);
            if (message != null) {
              historyMessages.add(message);
            }
          }
        }
        
        return historyMessages;
      } else {
        // 使用ErrorHandler处理API错误
        final errorMessage = '获取历史消息失败: ${response.msg}';
        LogUtil.error(errorMessage);
        
        ErrorHandler.handleException(
          ApiException(response.code, response.msg, errorCode: response.errorCode),
        );
        
        // 返回空列表
        return [];
      }
    } catch (e) {
      // 使用ErrorHandler处理异常
      final errorMessage = '获取历史消息异常: $e';
      LogUtil.error(errorMessage);
      
      ErrorHandler.handleException(e);
      
      // 返回空列表
      return [];
    }
  }
  
  // 解析历史消息
  Message? _parseHistoryMessage(Map<String, dynamic> messageData) {
    try {
      // 记录原始数据用于调试
      
      // 根据服务器实际返回的数据格式转换
      final String? content = messageData['content']?.toString();
      final String? sender = messageData['sender']?.toString();
      final String? createdAt = messageData['created_at']?.toString();
      final String? conversationIdStr = messageData['conversation_id']?.toString();
      
      // 检查核心字段
      if (content == null) {
        LogUtil.warn('历史消息缺少content字段: $messageData');
        return null;
      }
      
      // 使用默认值处理缺失字段
      final String effectiveSender = sender ?? 'user'; // 默认为用户消息
      final String effectiveCreatedAt = createdAt ?? DateTime.now().toIso8601String();
      
      // 为缺少的id字段生成一个唯一ID
      final String id = messageData['id']?.toString() ?? 
                       messageData['message_id']?.toString() ?? 
                       const Uuid().v4();
      
      // 尝试解析会话ID为整数，如果为null则尝试从上下文获取
      int? conversationId;
      if (conversationIdStr != null) {
        conversationId = int.tryParse(conversationIdStr);
      }
      
      if (conversationId == null) {
        // 如果无法从消息中获取会话ID，尝试使用当前活跃会话ID
        try {
          conversationId = Get.find<ChatManager>().activeConversationId.value;
          LogUtil.debug('使用当前活跃会话ID: $conversationId');
        } catch (e) {
          LogUtil.warn('无法获取当前活跃会话ID: $e');
          // 最后使用默认值0
          conversationId = 0;
        }
      }
      
      // 获取当前活跃的角色ID
      int activeAiRoleId;
      try {
        activeAiRoleId = Get.find<ChatManager>().activeAiRoleId.value;
      } catch (e) {
        LogUtil.warn('无法获取当前活跃角色ID: $e');
        activeAiRoleId = 1; // 默认角色ID
      }
      
      final String messageAiRoleId = messageData['ai_role_id']?.toString() ?? 
                                    messageData['role_id']?.toString() ?? 
                                    activeAiRoleId.toString();
      
      // 获取当前用户ID
      final currentUserId = _globalState.currentUser.value?.uid ?? 'user';
      
      // 构建消息对象
      return Message(
        id: id,
        lastMessage: content,
        senderUserId: effectiveSender == 'assistant' ? 'ai_$messageAiRoleId' : currentUserId,
        receiverUserId: effectiveSender == 'assistant' ? currentUserId : 'ai_$messageAiRoleId',
        conversationId: conversationId,
        messageType: MessageType.text, // 默认为文本消息类型
        time: DateTimeUtils.parseServerTimeToLocal(effectiveCreatedAt),
      );
    } catch (e) {
      LogUtil.error('解析历史消息异常: $e');
      return null;
    }
  }

  
  /// 解析实时消息
  /// 
  /// 解析从WebSocket获取的实时消息，支持多种格式
  /// [messageData] 消息数据
  /// [aiRoleId] 当前AI角色ID
  /// 返回解析后的消息对象，如果解析失败则返回null
  Message? parseRealTimeMessage(Map<String, dynamic> messageData, int aiRoleId) {
    try {
      // 记录原始数据用于调试
      LogUtil.debug('解析实时消息: $messageData');
      
      // 提取基本信息
      final String? content = messageData['content']?.toString();
      
      // 检查核心字段
      if (content == null || content.trim().isEmpty) {
        LogUtil.warn('实时消息缺少content字段或内容为空: $messageData');
        return null;
      }
      
      // 增强AI消息识别逻辑，检查多个可能的字段
      final String? event = messageData['event']?.toString();
      final String? sender = messageData['sender']?.toString();
      final String? role = messageData['role']?.toString();
      final String? messageType = messageData['message_type']?.toString();
      
      // 特殊处理：如果是chat_message事件，需要检查消息内容和其他特征来判断是否为AI回复
      bool isAiMessage = false;
      
      if (event == 'chat_message' || event == WsEvent.chat_message.toString()) {
        // 对于chat_message事件，如果sender字段明确指示，则使用sender字段判断
        if (sender == 'assistant') {
          isAiMessage = true;
        } else if (sender == 'user') {
          isAiMessage = false;
        } else {
          // 没有明确的sender字段，使用历史逻辑
          isAiMessage = true;
          LogUtil.debug('检测到chat_message事件但没有明确sender，判定为AI回复');
        }
      } else {
        // 其他情况下使用标准判断逻辑
        isAiMessage = event == 'ai_reply' || 
                     event == WsEvent.ai_reply.toString() ||
                     sender == 'assistant' || 
                     role == 'assistant' || 
                     messageType == 'ai_message' ||
                     (messageData['is_ai_message'] == true);
      }
      
      // 记录详细的消息类型判断过程
      LogUtil.debug('消息类型判断: event=$event, sender=$sender, role=$role, messageType=$messageType, 判定为${isAiMessage ? "AI" : "用户"}消息');
      
      // 检查必要字段
      if (content == null || content.trim().isEmpty) {
        LogUtil.warn('实时消息缺少必要字段或内容为空: $messageData');
        return null;
      }
      
      // 提取会话ID（优先使用conversation_id字段）
      String? conversationIdStr;
      
      // 按优先级尝试多个可能的字段名
      if (messageData.containsKey('conversation_id')) {
        conversationIdStr = messageData['conversation_id'].toString();
      } else if (messageData.containsKey('conversationid')) {
        conversationIdStr = messageData['conversationid'].toString();
      } else if (messageData.containsKey('channel_id')) {
        conversationIdStr = messageData['channel_id'].toString();
      }
      
      // 如果在顶层没找到，检查data子字段
      if (conversationIdStr == null && messageData['data'] is Map) {
        final subData = messageData['data'] as Map;
        if (subData.containsKey('conversation_id')) {
          conversationIdStr = subData['conversation_id'].toString();
        } else if (subData.containsKey('conversationid')) {
          conversationIdStr = subData['conversationid'].toString();
        } else if (subData.containsKey('channel_id')) {
          conversationIdStr = subData['channel_id'].toString();
        }
      }
      
      // 解析会话ID为整数，如果解析失败则尝试使用当前活跃的会话ID
      int? conversationId;
      if (conversationIdStr != null) {
        conversationId = int.tryParse(conversationIdStr);
      }
      
      if (conversationId == null) {
        // 如果无法从消息中获取会话ID，尝试使用当前活跃会话ID
        try {
          conversationId = Get.find<ChatManager>().activeConversationId.value;
          LogUtil.debug('使用当前活跃会话ID: $conversationId');
        } catch (e) {
          LogUtil.warn('无法获取当前活跃会话ID: $e');
          // 如果仍然无法获取，则直接使用0
          conversationId = 0;
        }
      }
      
      LogUtil.debug('提取到的会话ID: $conversationId');
      
      // 获取时间戳字段，增加更多可能的字段名
      final timeStamp = messageData['created_at']?.toString() ?? 
                       messageData['timestamp']?.toString() ??
                       messageData['time']?.toString() ??
                       messageData['date']?.toString();
      
      // 解析时间
      DateTime messageTime;
      if (timeStamp != null && timeStamp.isNotEmpty) {
        try {
          // 使用DateTimeUtils解析服务器时间
          messageTime = DateTimeUtils.parseServerTimeToLocal(timeStamp);
        } catch (e) {
          LogUtil.warn('解析消息时间失败: $e，使用当前时间');
          messageTime = DateTime.now();
        }
      } else {
        messageTime = DateTime.now();
      }
      
      // 获取当前用户ID
      final currentUserId = _globalState.currentUser.value?.uid ?? 'user';
      
      // 获取消息ID，增加更多可能的字段名，如果没有则生成新的
      final id = messageData['id']?.toString() ?? 
                messageData['message_id']?.toString() ?? 
                const Uuid().v4();
      
      // 获取消息中的ai_role_id（如果存在）
      final messageAiRoleId = messageData['ai_role_id']?.toString() ?? 
                             messageData['role_id']?.toString() ?? 
                             aiRoleId.toString();
      
      // 构建发送者和接收者ID
      String senderUserId;
      String receiverUserId;
      
      // 根据判断设置发送者和接收者
      if (isAiMessage) {
        // AI发送的消息
        senderUserId = 'ai_$messageAiRoleId';
        receiverUserId = currentUserId;
      } else {
        // 用户发送的消息
        senderUserId = currentUserId;
        receiverUserId = 'ai_$messageAiRoleId';
      }
      
      // 直接创建消息对象
      final message = Message(
        id: id,
        lastMessage: content,
        senderUserId: senderUserId,
        receiverUserId: receiverUserId,
        conversationId: conversationId,
        messageType: MessageType.text, // 默认为文本类型
        time: messageTime,
      );
      
      LogUtil.debug('已创建消息: ID=${message.id}, 内容=${message.lastMessage.length > 20 ? message.lastMessage.substring(0, 20) + "..." : message.lastMessage}, 会话ID=${message.conversationId}');
      
      return message;
    } catch (e) {
      LogUtil.error('解析实时消息异常: $e');
      return null;
    }
  }
  
  /// 创建用户消息
  /// 
  /// 创建用户发送的消息对象，确保时间处理一致
  /// [content] 消息内容
  /// [senderUserId] 发送者用户ID
  /// [receiverUserId] 接收者用户ID（AI角色ID）
  /// [conversationId] 会话ID
  /// [conversationid] 会话标识ID
  /// 返回新创建的消息对象
  Message createUserMessage(
    String content, 
    String senderUserId, 
    String receiverUserId,
    {int? conversationId}
  ) {
    // 使用当前本地时间
    final messageTime = DateTime.now();
    LogUtil.debug('创建用户消息: 内容="${content.length > 30 ? content.substring(0, 30) + "..." : content}", 时间=$messageTime');
    
    // 直接创建消息对象，避免通过JSON转换
    return Message(
      id: const Uuid().v4(),
      lastMessage: content,
      senderUserId: senderUserId,
      receiverUserId: receiverUserId,
      conversationId: conversationId,
      messageType: MessageType.text,
      time: messageTime,
      status: MessageStatus.sending,
    );
  }
  
  /// 创建AI消息
  /// 
  /// 创建AI回复的消息对象
  /// [content] 消息内容
  /// [senderUserId] 发送者用户ID（AI角色ID）
  /// [receiverUserId] 接收者用户ID
  /// [conversationId] 会话ID
  /// [conversationid] 会话标识ID
  /// 返回新创建的AI消息对象
  Message createAiMessage(
    String content, 
    String senderUserId, 
    String receiverUserId, 
    {int? conversationId}
  ) {
    // 使用当前本地时间
    final messageTime = DateTime.now();
    LogUtil.debug('创建AI消息: 内容="${content.length > 30 ? content.substring(0, 30) + "..." : content}", 时间=$messageTime');
    
    // 直接创建消息对象
    return Message(
      id: const Uuid().v4(),
      lastMessage: content,
      senderUserId: senderUserId,
      receiverUserId: receiverUserId,
      conversationId: conversationId,
      messageType: MessageType.text,
      time: messageTime,
      status: MessageStatus.sent,
    );
  }
  
  /// 创建系统消息
  /// 
  /// 创建系统消息对象，确保时间处理一致
  /// [content] 系统消息内容
  /// [receiverUserId] 接收者用户ID
  /// [conversationId] 会话ID
  /// [conversationid] 会话标识ID
  /// 返回新创建的系统消息对象
  Message createSystemMessage(
    String content, 
    String receiverUserId, 
    {int? conversationId}
  ) {
    // 使用当前本地时间
    final messageTime = DateTime.now();
    LogUtil.debug('创建系统消息: 内容="$content", 时间=$messageTime');
    
    // 直接创建消息对象，避免通过JSON转换
    return Message(
      id: const Uuid().v4(),
      lastMessage: content,
      senderUserId: 'system',
      receiverUserId: receiverUserId,
      conversationId: conversationId,
      messageType: MessageType.system,
      time: messageTime,
    );
  }
  
  /// 验证消息内容
  /// 
  /// [content] 消息内容
  /// 返回验证结果对象
  Map<String, dynamic> validateMessage(String content) {
    // 基础验证：检查消息是否为空
    if (content.trim().isEmpty) {
      LogUtil.warn('消息内容为空，验证失败');
      return {
        'isValid': false,
        'errorField': 'content',
        'errorMessage': '消息内容不能为空'
      };
    }
    
    // 检查消息长度
    if (content.length > 4000) {
      LogUtil.warn('消息内容过长，验证失败');
      return {
        'isValid': false,
        'errorField': 'content',
        'errorMessage': '消息内容不能超过4000个字符'
      };
    }
    
    return {
      'isValid': true,
      'errorField': '',
      'errorMessage': ''
    };
  }

  /// 提取AI消息数据
  /// 
  /// 从WebSocket接收的数据中提取标准化的AI消息数据
  /// [data] 原始WebSocket数据
  /// [currentAiRoleId] 当前活跃的AI角色ID
  /// 返回包含以下字段的Map:
  /// - messageData: 标准化的消息数据
  /// - receivedRoleId: 从消息中提取的角色ID
  /// - receivedConversationId: 从消息中提取的会话ID
  /// - replyToMessageId: 回复的消息ID
  Map<String, dynamic> extractAiMessageData(Map<String, dynamic> data, int currentAiRoleId) {
    // 提取role_id
    int? receivedRoleId;
    
    // 直接从data中获取
    receivedRoleId = data['role_id'] is int ? data['role_id'] as int :
                   (data['role_id'] is String ? int.tryParse(data['role_id'].toString()) : null);
    
    // 如果data没有，可能是在data的子字段中
    if (receivedRoleId == null && data['data'] is Map) {
      final subData = data['data'] as Map;
      receivedRoleId = subData['role_id'] is int ? subData['role_id'] as int :
                     (subData['role_id'] is String ? int.tryParse(subData['role_id'].toString()) : null);
    }
    
    // 如果没有找到role_id，使用当前活跃的role_id
    if (receivedRoleId == null) {
      LogUtil.warn('消息中没有role_id，使用当前活跃角色ID: $currentAiRoleId');
      receivedRoleId = currentAiRoleId;
    } else if (receivedRoleId != currentAiRoleId) {
      LogUtil.debug('收到非当前活跃角色的消息: received role_id=$receivedRoleId, current role_id=$currentAiRoleId');
    }
    
    // 提取conversation_id
    int? receivedConversationId;
    
    // 直接从data中获取
    receivedConversationId = data['conversation_id'] is int ? data['conversation_id'] as int : 
                          (data['conversation_id'] is String ? int.tryParse(data['conversation_id'].toString()) : null);
    
    if (receivedConversationId == null) {
      receivedConversationId = data['channel_id'] is int ? data['channel_id'] as int : 
                            (data['channel_id'] is String ? int.tryParse(data['channel_id'].toString()) : null);
    }
    
    // 如果data没有，可能是在data的子字段中
    if (receivedConversationId == null && data['data'] is Map) {
      final subData = data['data'] as Map;
      receivedConversationId = subData['conversation_id'] is int ? subData['conversation_id'] as int : 
                           (subData['conversation_id'] is String ? int.tryParse(subData['conversation_id'].toString()) : null);
      
      if (receivedConversationId == null) {
        receivedConversationId = subData['channel_id'] is int ? subData['channel_id'] as int : 
                             (subData['channel_id'] is String ? int.tryParse(subData['channel_id'].toString()) : null);
      }
    }
    
    // 提取reply_to_message_id
    String? replyToMessageId;
    
    // 尝试从data中提取reply_to_message_id
    if (data.containsKey('reply_to_message_id')) {
      replyToMessageId = data['reply_to_message_id'].toString();
    } else if (data['data'] is Map && data['data'].containsKey('reply_to_message_id')) {
      replyToMessageId = data['data']['reply_to_message_id'].toString();
    } else if (data.containsKey('message_id')) {
      replyToMessageId = data['message_id'].toString();
    } else if (data['data'] is Map && data['data'].containsKey('message_id')) {
      replyToMessageId = data['data']['message_id'].toString();
    }
    
    // 提取内容
    String? content;
    
    // 直接从data中获取content
    content = data['content']?.toString();
    
    // 如果data没有，可能是在data的子字段中
    if ((content == null || content.isEmpty) && data['data'] is Map) {
      final subData = data['data'] as Map;
      content = subData['content']?.toString();
    }
    
    if (content == null || content.isEmpty) {
      LogUtil.warn('无法提取AI消息内容');
      return {
        'messageData': null,
        'receivedRoleId': receivedRoleId,
        'receivedConversationId': receivedConversationId,
        'replyToMessageId': replyToMessageId
      };
    }
    
    // 构建标准化的消息数据结构
    Map<String, dynamic> standardData = Map<String, dynamic>.from(data);
    standardData['content'] = content;
    
    // 保留会话ID用于传递
    if (receivedConversationId != null) {
      standardData['conversation_id'] = receivedConversationId;
    }
    
    // 确保role_id存在
    standardData['role_id'] = receivedRoleId;
    
    return {
      'messageData': standardData,
      'receivedRoleId': receivedRoleId,
      'receivedConversationId': receivedConversationId,
      'replyToMessageId': replyToMessageId
    };
  }

  /// 处理会话信息
  /// 
  /// 判断是否接收到新的会话信息，并返回相关状态
  /// [currentConversationId] 当前会话ID
  /// [currentAiRoleId] 当前AI角色ID
  /// [receivedConversationId] 接收到的会话ID
  /// [receivedRoleId] 接收到的角色ID
  /// 返回包含以下字段的Map:
  /// - isNewSessionInfo: 是否为新会话信息
  /// - useConversationId: 应该使用的会话ID
  /// - useRoleId: 应该使用的角色ID
  Map<String, dynamic> processSessionInfo(
    int currentConversationId,
    int currentAiRoleId,
    int? receivedConversationId,
    int? receivedRoleId,
    int? pendingMessageRoleId
  ) {
    // 确定要使用的roleId，优先使用待处理消息的roleId
    final useRoleId = pendingMessageRoleId ?? receivedRoleId ?? currentAiRoleId;
    
    // 确定要使用的会话ID，优先使用服务器返回的会话ID
    final useConversationId = receivedConversationId != null && receivedConversationId > 0
        ? receivedConversationId  // 优先使用服务器返回的会话ID
        : currentConversationId;  // 如果服务器没有返回，则使用当前会话ID
    
    // 检查是否接收到新的会话信息
    bool isNewSessionInfo = false;
    String logMessage = '';
    
    // 第一次聊天时，如果收到了会话ID且之前没有会话ID，就是新会话信息
    if (currentConversationId <= 0 && receivedConversationId != null && receivedConversationId > 0) {
      isNewSessionInfo = true;
      logMessage = '首次收到会话ID: $receivedConversationId，为当前角色($currentAiRoleId)创建新会话';
    }
    // 如果收到的会话ID与当前会话ID不同，这可能是会话切换或更新
    else if (receivedConversationId != null && receivedConversationId > 0 && receivedConversationId != currentConversationId) {
      isNewSessionInfo = true;
      logMessage = '收到不同的会话ID: 当前=$currentConversationId, 收到=$receivedConversationId';
    }
    // 如果收到的角色ID与当前角色ID不同，这可能是角色切换
    else if (receivedRoleId != null && receivedRoleId > 0 && receivedRoleId != currentAiRoleId) {
      isNewSessionInfo = true;
      logMessage = '收到不同的角色ID: 当前=$currentAiRoleId, 收到=$receivedRoleId';
    }
    
    if (isNewSessionInfo) {
      LogUtil.info(logMessage);
    }
    
    return {
      'isNewSessionInfo': isNewSessionInfo,
      'useConversationId': useConversationId,
      'useRoleId': useRoleId
    };
  }

  /// 验证并纠正消息发送者
  /// 
  /// 验证消息的发送者是否正确，如果不正确则进行纠正
  /// [message] 原始消息
  /// [aiRoleId] AI角色ID
  /// [currentUserId] 当前用户ID
  /// 返回验证并可能纠正后的消息
  Message validateMessageSender(Message message, int aiRoleId, String currentUserId) {
    final isAiSender = message.senderUserId.startsWith('ai_');
    
    // 如果不是AI发送者但应该是AI消息，则纠正发送者
    if (!isAiSender) {
      LogUtil.warn('收到了非AI发送者的消息，但应该是AI消息。将进行纠正。');
      return message.copyWith(
        senderUserId: 'ai_$aiRoleId',
        receiverUserId: currentUserId
      );
    }
    
    return message;
  }
}