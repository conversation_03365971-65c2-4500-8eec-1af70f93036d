import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/role_card_skeleton.dart';

/// 角色列表骨架组件
///
/// 用于收藏页面等列表布局的骨架屏
class RoleListSkeleton extends StatelessWidget {
  /// 列表项数量
  final int itemCount;
  
  /// 列表项间距
  final double spacing;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 是否可滚动
  final bool scrollable;
  
  /// 构造函数
  const RoleListSkeleton({
    Key? key,
    this.itemCount = 8,
    this.spacing = 12,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.scrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: scrollable 
          ? const AlwaysScrollableScrollPhysics() 
          : const NeverScrollableScrollPhysics(),
      slivers: [
        // 顶部间距
        const SliverToBoxAdapter(
          child: <PERSON><PERSON><PERSON><PERSON>(height: 8),
        ),
      
        // 列表骨架
        SliverPadding(
          padding: padding,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => Padding(
                padding: EdgeInsets.only(bottom: spacing),
                child: const RoleCardSkeleton(
                  type: RoleCardSkeletonType.list,
                ),
              ),
              childCount: itemCount,
            ),
          ),
        ),
      ],
    );
  }
} 