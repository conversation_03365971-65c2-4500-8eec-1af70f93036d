import 'package:flutter/material.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';

/// 角色卡片骨架组件
/// 
/// 统一的角色卡片骨架屏，支持网格和列表两种布局
class RoleCardSkeleton extends StatelessWidget {
  /// 卡片类型：网格或列表
  final RoleCardSkeletonType type;
  
  /// 是否显示头像
  final bool showAvatar;
  
  /// 描述行数
  final int descriptionLines;
  
  /// 自定义高度（仅列表模式有效）
  final double? height;
  
  /// 自定义宽度
  final double? width;
  
  /// 自定义圆角
  final double? borderRadius;
  
  /// 自定义内边距
  final EdgeInsetsGeometry? padding;
  
  /// 构造函数
  const RoleCardSkeleton({
    Key? key,
    this.type = RoleCardSkeletonType.grid,
    this.showAvatar = true,
    this.descriptionLines = 3,
    this.height,
    this.width,
    this.borderRadius,
    this.padding,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 根据类型选择不同布局
    return type == RoleCardSkeletonType.grid
        ? _buildGridCard(context)
        : _buildListCard(context);
  }
  
  /// 构建网格卡片骨架
  Widget _buildGridCard(BuildContext context) {
    return Container(
      width: width,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(
          borderRadius ?? 12.0),
      ),
      child: ShimmerWidget(
        child: Stack(
          children: [
            // 卡片背景
            Container(
              decoration: BoxDecoration(
                color: Colors.grey[850],
                borderRadius: BorderRadius.circular(
                  borderRadius ?? 12.0),
              ),
            ),
            
            // 底部内容区域
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: padding ?? const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 头像和标题行
                    if (showAvatar) _buildAvatarTitleRow(),
                    
                    const SizedBox(height: 8),
                    
                    // 描述骨架
                    ...List.generate(
                      descriptionLines,
                      (index) => Padding(
                        padding: EdgeInsets.only(
                          bottom: index < descriptionLines - 1 ? 4 : 0,
                        ),
                        child: SkeletonContainer(
                          height: 12,
                          width: index == descriptionLines - 1 ? 160 : double.infinity,
                          borderRadius: 4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建列表卡片骨架
  Widget _buildListCard(BuildContext context) {
    return Container(
      height: height ?? 84,
      width: width,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(borderRadius ?? 16.0),
      ),
      padding: padding ?? const EdgeInsets.all(12),
      child: ShimmerWidget(
        child: Row(
          children: [
            // 头像骨架
            if (showAvatar) ...[
              const SkeletonContainer(
                width: 60,
                height: 60,
                borderRadius: 16,
              ),
              const SizedBox(width: 12),
            ],
            
            // 角色信息骨架
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 名字骨架
                  const SkeletonContainer(
                    height: 16,
                    width: 120,
                    borderRadius: 4,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // 描述骨架
                  ...List.generate(
                    descriptionLines > 2 ? 2 : descriptionLines,
                    (index) => Padding(
                      padding: EdgeInsets.only(
                        bottom: index < (descriptionLines > 2 ? 1 : descriptionLines - 1) ? 6 : 0,
                      ),
                      child: SkeletonContainer(
                        height: 12,
                        width: index == 0 ? double.infinity : 180,
                        borderRadius: 4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 8),
            
            // 右侧箭头占位
            const SkeletonContainer(
              width: 16,
              height: 16,
              borderRadius: 4,
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建头像和标题行
  Widget _buildAvatarTitleRow() {
    return Row(
      children: [
        // 头像骨架
        const SkeletonContainer(
          width: 32,
          height: 32,
          borderRadius: 16,
        ),
        const SizedBox(width: 8),
        // 标题骨架
        const Expanded(
          child: SkeletonContainer(
            height: 18,
            borderRadius: 4,
          ),
        ),
      ],
    );
  }
}

/// 角色卡片骨架类型
enum RoleCardSkeletonType {
  /// 网格布局
  grid,
  
  /// 列表布局
  list,
} 