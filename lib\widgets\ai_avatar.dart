import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/common/utils/logger.dart';

class AiAvatar extends StatelessWidget {
  final int characterId;
  final String? avatarUrl;
  final double size;
  final Color borderColor;
  final double borderWidth;
  final bool showBorder;
  final double? placeholderSize;
  
  const AiAvatar({
    Key? key,
    required this.characterId,
    this.avatarUrl,
    this.size = 48.0,
    this.borderColor = Colors.white,
    this.borderWidth = 2.0,
    this.showBorder = true,
    this.placeholderSize,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 确保URL是有效的
    final String url = _sanitizeUrl(avatarUrl);
    
    // 计算最佳缓存尺寸
    final int cacheSize = (size * 2).ceil(); // 为高分辨率设备优化
    
    
    return Container(
      width: size,
      height: size,
      decoration: showBorder ? BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: borderColor,
          width: borderWidth,
        ),
      ) : null,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: url.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: url,
                fit: BoxFit.cover,
                memCacheWidth: cacheSize,
                memCacheHeight: cacheSize,
                maxWidthDiskCache: cacheSize * 2,
                maxHeightDiskCache: cacheSize * 2,
                fadeInDuration: const Duration(milliseconds: 150), // 缩短淡入时间，提供更快的响应
                fadeOutDuration: const Duration(milliseconds: 50), // 缩短淡出时间
                placeholder: (context, url) {
                  return _buildPlaceholder();
                },
                errorWidget: (context, url, error) {
                  LogUtil.error('AiAvatar - 加载头像失败: $url, 错误: $error');
                  return _buildErrorWidget();
                },
              )
            : _buildPlaceholder(),
      ),
    );
  }
  
  // 确保URL是有效的格式 - 与ChatController保持一致的清理逻辑
  String _sanitizeUrl(String? url) {
    if (url == null || url.isEmpty) {
      LogUtil.debug('AiAvatar - URL为空或null');
      return '';
    }
    
    // 清理URL字符串
    String cleaned = url.trim();
    
    // 移除可能的反引号或其他特殊字符
    cleaned = cleaned.replaceAll(RegExp(r'^`+|`+$'), '');
    cleaned = cleaned.replaceAll(RegExp(r'^\"+|\"+$'), '');
    cleaned = cleaned.trim();
    
    // 验证URL格式
    if (!cleaned.startsWith('http://') && !cleaned.startsWith('https://')) {
      LogUtil.debug('AiAvatar - 无效的头像URL格式: $url');
      return '';
    }

    return cleaned;
  }
  
  // 构建占位符 - 改进加载状态显示
  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey.shade800,
      child: Center(
        child: characterId > 0
            ? SizedBox(
                width: size * 0.4,
                height: size * 0.4,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: borderColor.withValues(alpha: 0.7), // 使用半透明的边框颜色
                ),
              )
            : Icon(
                Icons.smart_toy_outlined,
                size: placeholderSize ?? (size * 0.5),
                color: Colors.white70, // 使用稍微透明的白色
              ),
      ),
    );
  }
  
  // 构建错误显示 - 统一错误状态显示
  Widget _buildErrorWidget() {
    return Container(
      color: Colors.grey.shade800,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.smart_toy_outlined,
              size: placeholderSize ?? (size * 0.4),
              color: Colors.white60,
            ),
            if (size > 40) // 只在较大的头像上显示文字
              Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(
                  '头像',
                  style: TextStyle(
                    color: Colors.white60,
                    fontSize: size * 0.12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
} 