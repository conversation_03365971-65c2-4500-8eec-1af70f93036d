import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/sessions/controller/sessions_controller.dart';
import 'package:rolio/modules/sessions/view/empty_sessions_view.dart';
import 'package:rolio/modules/sessions/view/chat_list_item.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_list_view.dart';
import 'package:rolio/widgets/skeleton/skeleton_avatar_text.dart';
import 'package:rolio/widgets/loader.dart';

class SessionsPage extends GetView<SessionsController> {
  const SessionsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: LoadingOverlay(
        isLoading: controller.isDeletingSession,
        child: _buildChatsList(context),
      ),
    );
  }

  Widget _buildChatsList(BuildContext context) {
    return Obx(() {
      final sessions = controller.sessions;
      
      // 错误状态显示
      if (controller.errorMessage.value.isNotEmpty && sessions.isEmpty) {
        return _buildErrorView();
      }
      
      // 首次加载显示骨架屏
      if (controller.isLoading.value && sessions.isEmpty) {
        return _buildSkeletonView();
      }
      
      // 空数据状态
      if (sessions.isEmpty) {
        return const EmptySessionsView();
      }
      
      return RefreshIndicator(
        onRefresh: controller.refreshSessions,
        color: Colors.white,
        child: NotificationListener<ScrollNotification>(
          onNotification: (notification) => controller.handleScrollNotification(notification),
          child: ListView.separated(
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: sessions.length + (controller.hasMoreDataBool ? 1 : 0),
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            separatorBuilder: (context, index) {
              // 检查当前会话和下一个会话的置顶状态
              if (index < sessions.length - 1) {
                bool currentIsPinned = sessions[index].isPinned;
                bool nextIsPinned = sessions[index + 1].isPinned;
                
                // 只在置顶和非置顶会话之间添加分隔线
                if (currentIsPinned && !nextIsPinned) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Container(
                      height: 1,
                      color: Colors.grey.shade800,
                    ),
                  );
                }
              }
              return const SizedBox(height: 0);
            },
            itemBuilder: (context, index) {
              // 加载更多指示器
              if (index == sessions.length) {
                return _buildLoadMoreIndicator();
              }
              
              final session = sessions[index];
              return ChatListItem(
                session: session,
                onTap: () => controller.openSession(session),
                onDelete: (conversationid) => controller.deleteSession(conversationid),
                onTogglePin: (conversationid) => controller.togglePinSession(conversationid),
                onHide: (conversationid) => controller.hideSession(conversationid),
              );
            },
          ),
        ),
      );
    });
  }
  
  // 骨架屏视图
  Widget _buildSkeletonView() {
    return SkeletonListView(
      itemCount: 6, // 减少骨架屏数量，提高性能
      itemHeight: 80,
      padding: const EdgeInsets.all(16.0),
      itemBuilder: (context, index) => ShimmerWidget(
        baseColor: Colors.grey.shade900,
        highlightColor: Colors.grey.shade700,
        child: const SkeletonAvatarText(
        avatarSize: 56.0,
        textLines: 2,
        textLineHeight: 16.0,
        textLineSpacing: 8.0,
        ),
      ),
    );
  }
  
  // 错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey.shade600),
          const SizedBox(height: 16),
          Text(
            controller.errorMessage.value,
            style: TextStyle(color: Colors.grey.shade400, fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => controller.loadSessions(refresh: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade800,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
  
  // 加载更多指示器
  Widget _buildLoadMoreIndicator() {
    return Obx(() {
      // 只有在加载中且有更多数据时才显示加载指示器
      if (controller.isLoadingMore.value) {
        return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(
              child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
            ),
        );
      }
      
      // 如果有更多数据但未加载，显示加载更多提示
      if (controller.hasMoreDataBool) {
        return InkWell(
          onTap: () => controller.loadMoreSessions(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Text(
                'Load More',
                style: TextStyle(color: Colors.grey.shade400, fontSize: 14),
              ),
            ),
          ),
        );
      }
      
      // 没有更多数据时显示到底提示
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No more chats',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
        ),
      );
    });
  }
}