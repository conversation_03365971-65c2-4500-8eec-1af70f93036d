# Rolio 聊天应用

## 项目简介

Rolio 是一个基于 Flutter 的跨平台即时通讯应用，支持 Android、iOS、Web、Windows、macOS 和 Linux。项目采用
GetX 进行状态管理，集成了 WebSocket 实时通信，并提供了丰富的聊天功能，界面美观且响应式设计。

## 主要功能

- 用户认证与登录
- 一对一聊天与群聊
- 支持文本、图片、GIF、音频、视频等多种消息类型
- 消息已读回执、在线/离线状态显示
- 消息回复功能
- 实时消息通知
- 用户状态管理

## 技术栈

- 前端：Flutter 3.24+，Dart 3.4+
- 状态管理：GetX
- 后端通信：WebSocket
- 多媒体：audioplayers
- 依赖管理：pubspec.yaml

## 依赖环境

- Flutter SDK >= 3.24.0
- Dart SDK >= 3.4.0
- Android Studio/VSCode/Xcode（根据平台选择）
- Firebase 项目配置（需在 android/app、ios/Runner 下放置对应的 google-services.json 和
  GoogleService-Info.plist）

## 目录结构

```
rolio-client/
├── lib/                           # Flutter 主代码目录
│   ├── common/                    # 通用组件和工具
│   │   ├── constants/             # 常量定义
│   │   ├── enums/                 # 枚举类型
│   │   ├── helper_methods/        # 辅助方法
│   │   └── utils/                 # 工具类
│   │       ├── http_manager_util.dart  # HTTP请求管理工具
│   │       └── logger.dart             # 日志工具
│   ├── manager/                   # 全局管理器
│   │   ├── app_initializer.dart   # 应用初始化管理（Firebase、用户认证、WebSocket）
│   │   ├── global_state.dart      # 全局状态管理（当前用户、初始化状态）
│   │   └── ws_manager.dart        # WebSocket连接与事件管理
│   ├── modules/                   # 功能模块（采用MVCS架构）
│   │   ├── chat/                  # 聊天模块
│   │   │   ├── binding/           # 依赖注入绑定
│   │   │   ├── controller/        # 控制器层（业务逻辑与状态管理）
│   │   │   ├── model/             # 数据模型
│   │   │   ├── service/           # 服务层（数据处理与业务逻辑）
│   │   │   └── view/              # 视图层（UI组件）
│   │   ├── home/                  # 主页模块
│   │   ├── login/                 # 登录模块
│   │   └── user/                  # 用户模块
│   ├── routes/                    # 路由管理
│   │   ├── router_manager.dart    # 路由管理器
│   │   └── routes.dart            # 路由定义
│   ├── widgets/                   # 公共UI组件
│   ├── env.dart                   # 环境配置（开发、测试、生产环境）
│   ├── firebase_options.dart      # Firebase配置选项
│   ├── generated/                 # 自动生成的代码
│   └── main.dart                  # 应用入口点
├── assets/                        # 静态资源
├── pubspec.yaml                   # 依赖声明和项目配置
├── DevGuide.md                    # 开发指南
└── README.md                      # 项目说明文档
```

## 快速开始

1. 克隆项目到本地：
   ```bash
   git clone <本项目地址>
   cd rolio-client
   ```
2. 安装依赖：
   ```bash
   flutter pub get
   ```
3. 配置 Firebase：
    - 在 Firebase 控制台创建项目，下载 `google-services.json`（Android）和 `GoogleService-Info.plist`
      （iOS），分别放到对应目录。

4. 运行项目：
   ```bash
   flutter run --dart-define=APP_ENV=debug
   ```

## 环境配置

项目支持多环境配置，通过 `--dart-define=APP_ENV=xxx` 参数指定：

- debug: 开发环境
- test: 测试环境
- release: 生产环境

每个环境的具体配置在 `lib/env.dart` 文件中定义。