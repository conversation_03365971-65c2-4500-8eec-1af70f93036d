import 'package:get/get.dart';
import 'package:rolio/common/interfaces/base_repository.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/retry_util.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/env.dart';
import 'dart:async';

/// 搜索记录模型
class SearchRecord {
  final int id;
  final String keyword;
  final int searchCount;
  final DateTime lastSearchAt;
  
  SearchRecord({
    required this.id,
    required this.keyword,
    required this.searchCount,
    required this.lastSearchAt,
  });
  
  factory SearchRecord.fromJson(Map<String, dynamic> json) {
    return SearchRecord(
      id: json['id'] ?? 0,
      keyword: json['search_keyword'] ?? '',
      searchCount: json['search_count'] ?? 0,
      lastSearchAt: json['last_search_at'] != null 
        ? DateTime.parse(json['last_search_at']) 
        : DateTime.now(),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'search_keyword': keyword,
      'search_count': searchCount,
      'last_search_at': lastSearchAt.toIso8601String(),
    };
  }
}

/// 搜索仓库
/// 
/// 处理角色搜索相关的API请求
class SearchRepository extends BaseRepository {
  // 缓存管理器
  // 构造函数
  SearchRepository();

  // 获取API基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;

  // 请求去重 - 通用的请求跟踪Map
  final Map<String, Future<dynamic>> _pendingRequests = {};

  /// 通用请求去重方法
  ///
  /// [requestKey] 请求的唯一标识键
  /// [requestExecutor] 执行实际请求的函数
  /// [debugInfo] 调试信息，用于日志输出
  /// 返回请求结果
  Future<T> _executeWithDeduplication<T>(
    String requestKey,
    Future<T> Function() requestExecutor,
    String debugInfo,
  ) async {
    // 检查是否有相同请求正在进行
    if (_pendingRequests.containsKey(requestKey)) {
      LogUtil.debug('SearchRepository: 请求去重 - $debugInfo');
      return _pendingRequests[requestKey]! as Future<T>;
    }

    // 创建新请求
    final requestFuture = requestExecutor();
    _pendingRequests[requestKey] = requestFuture;

    // 请求完成后清理
    requestFuture.whenComplete(() {
      _pendingRequests.remove(requestKey);
    });

    return requestFuture;
  }
  
  /// 搜索角色
  /// 
  /// 根据关键词搜索角色
  /// [keyword] 搜索关键词
  /// [page] 页码
  /// [size] 每页大小
  /// [forceRefresh] 是否强制刷新，不使用缓存
  /// 返回搜索结果及分页信息
  Future<Map<String, dynamic>> searchRoles({
    required String keyword,
    int page = 1, 
    int size = 10,
    bool forceRefresh = false,
  }) async {
    return await RetryUtil.executeWithRetry<Map<String, dynamic>>(
      () async {
        LogUtil.debug('搜索角色，关键词: $keyword, 页码: $page, 大小: $size');
        
        // 构建请求体
        final requestBody = {
          'keyword': keyword,
          'page': page,
          'size': size,
        };
        
        // 发送请求
        final response = await HttpManager.post(
          url: '${_baseUrl}/search',
          body: requestBody,
        );
        
        // 解析响应
        final responseData = response.rawData;
        if (responseData is! Map<String, dynamic>) {
          LogUtil.warn('搜索角色响应数据结构异常，不是Map类型');
          throw ErrorHandler.createAppException(
            'Invalid search response format'
          );
        }
        
        // 检查数据字段
        if (!responseData.containsKey('data')) {
          LogUtil.warn('搜索角色响应数据结构异常，data字段不存在');
          throw ErrorHandler.createAppException(
            'Invalid search response structure, missing data field'
          );
        }
        
        final dynamic dataField = responseData['data'];
        if (dataField is! Map<String, dynamic>) {
          LogUtil.warn('搜索角色响应数据结构异常，data不是Map类型');
          throw ErrorHandler.createAppException(
            'Invalid search response data format'
          );
        }
        
        // 解析角色列表
        List<AiRole> roles = [];
        
        if (dataField.containsKey('items') && dataField['items'] is List) {
          final List<dynamic> items = dataField['items'];
          
          for (var item in items) {
            try {
              if (item is Map<String, dynamic>) {
                final role = AiRole.fromJson(item);
                
                // 验证必要字段
                if (role.id <= 0 || role.name.isEmpty) {
                  LogUtil.warn('角色数据验证失败，跳过: $item');
                  continue;
                }
                
                roles.add(role);
              }
            } catch (e) {
              LogUtil.error('解析角色数据失败: $e, 数据: $item');
            }
          }
        }
        
        // 提取分页信息
        final int total = dataField['total'] ?? 0;
        final int currentPage = dataField['page'] ?? page;
        final int pageSize = dataField['size'] ?? size;
        final int pages = dataField['pages'] ?? 0;
        
        LogUtil.debug('成功搜索角色: ${roles.length}个角色，总共: $total 个');
        
        // 准备结果
        final result = {
          'items': roles,
          'total': total,
          'page': currentPage,
          'size': pageSize,
          'pages': pages
        };
        
        return result;
      },
      config: const RetryConfig(
        maxRetries: 2,
        baseDelayMs: 500,
        maxDelayMs: 5000,
        enableNetworkCheck: true,
        enableJitter: true,
      ),
      operationName: '搜索角色',
      onRetry: (retryState) {
        LogUtil.info('搜索角色重试中，第${retryState.currentAttempt}次尝试，延迟${retryState.delayMs}ms');
      },
    );
  }
  
  /// 获取搜索建议
  ///
  /// 根据输入关键词返回匹配的角色名称建议列表
  /// [keyword] 关键词
  /// [limit] 返回数量限制
  /// 返回建议列表
  Future<List<String>> getSearchSuggestions(String keyword, {int limit = 10}) async {
    try {
      if (keyword.isEmpty) {
        return [];
      }

      // 生成请求键用于去重
      final requestKey = 'suggestions_${_normalizeKeyword(keyword)}_$limit';

      // 使用通用去重方法
      return await _executeWithDeduplication<List<String>>(
        requestKey,
        () => _executeSuggestionRequest(keyword, limit),
        '搜索建议 - $keyword',
      );
    } catch (e) {
      LogUtil.error('获取搜索建议失败: $e');
      throw ErrorHandler.createAppException(
        e,
        'Failed to get search suggestions, please try again later'
      );
    }
  }

  /// 执行实际的搜索建议请求
  Future<List<String>> _executeSuggestionRequest(String keyword, int limit) async {
    return await RetryUtil.executeWithRetry<List<String>>(
      () async {
        LogUtil.debug('SearchRepository: 开始获取搜索建议 - 关键词=$keyword, 限制=$limit');

        // 构建请求参数
        final params = {
          'keyword': keyword,
          'limit': limit.toString(),
        };

        // 发送请求
        final response = await HttpManager.get(
          url: '${_baseUrl}/search/match',
          params: params,
        );

        if (response.rawData == null) {
          LogUtil.warn('获取搜索建议失败，响应为空');
          throw ErrorHandler.createAppException(
            'Empty response when getting search suggestions'
          );
        }

        // 解析响应
        final responseData = response.rawData;
        if (responseData is! Map<String, dynamic> ||
            !responseData.containsKey('data') ||
            responseData['data'] is! Map<String, dynamic> ||
            !responseData['data'].containsKey('suggestions')) {
          LogUtil.warn('获取搜索建议响应数据结构异常');
          throw ErrorHandler.createAppException(
            'Invalid search suggestions response format'
          );
        }

        final suggestions = responseData['data']['suggestions'];
        if (suggestions is! List) {
          LogUtil.warn('搜索建议数据不是列表类型');
          throw ErrorHandler.createAppException(
            'Invalid search suggestions data type'
          );
        }

        // 转换为字符串列表
        final List<String> suggestionList = List<String>.from(suggestions.map((item) => item.toString()));

        LogUtil.debug('SearchRepository: 获取搜索建议成功 - 关键词=$keyword, 建议数量=${suggestionList.length}');
        return suggestionList;
      },
      config: const RetryConfig(
        maxRetries: 1,
        baseDelayMs: 300,
        maxDelayMs: 2000,
        enableNetworkCheck: true,
        enableJitter: true,
      ),
      operationName: '获取搜索建议',
      onRetry: (retryState) {
        LogUtil.info('获取搜索建议重试中，第${retryState.currentAttempt}次尝试，延迟${retryState.delayMs}ms');
      },
    );
  }
  
  /// 标准化关键词（去除空格、转小写、去重复字符）
  String _normalizeKeyword(String keyword) {
    return keyword.trim().toLowerCase().replaceAll(RegExp(r'\s+'), '_');
  }
  

  
  /// 获取随机角色名
  ///
  /// 用于搜索框的默认提示
  /// 返回随机角色名
  Future<String> getRandomRoleName() async {
    return await RetryUtil.executeWithRetry<String>(
      () async {
        // 发送请求
        final response = await HttpManager.get(
          url: '${_baseUrl}/search/random-name',
        );
        
        if (response.rawData == null) {
          LogUtil.warn('获取随机角色名失败，响应为空');
          throw ErrorHandler.createAppException(
            'Empty response when getting random role name'
          );
        }

        // 解析响应
        final responseData = response.rawData;
        if (responseData is! Map<String, dynamic> ||
            !responseData.containsKey('data') ||
            responseData['data'] is! Map<String, dynamic> ||
            !responseData['data'].containsKey('role_name')) {
          LogUtil.warn('获取随机角色名响应数据结构异常');
          throw ErrorHandler.createAppException(
            'Invalid random role name response format'
          );
        }
        
        final roleName = responseData['data']['role_name']?.toString() ?? '';
        
        LogUtil.debug('成功获取随机角色名: $roleName');
        return roleName;
      },
      config: const RetryConfig(
        maxRetries: 1,
        baseDelayMs: 300,
        maxDelayMs: 2000,
        enableNetworkCheck: true,
        enableJitter: true,
      ),
      operationName: '获取随机角色名',
      onRetry: (retryState) {
        LogUtil.info('获取随机角色名重试中，第${retryState.currentAttempt}次尝试，延迟${retryState.delayMs}ms');
      },
    );
  }
  
  /// 获取搜索历史
  ///
  /// 返回用户的搜索历史记录
  /// [page] 页码
  /// [size] 每页大小
  /// [forceRefresh] 是否强制刷新
  /// 返回搜索历史及分页信息
  Future<Map<String, dynamic>> getSearchHistory({
    int page = 1,
    int size = 10,
    bool forceRefresh = false,
  }) async {
    try {
      // 生成请求键用于去重
      final requestKey = 'history_${page}_$size';

      // 使用通用去重方法
      return await _executeWithDeduplication<Map<String, dynamic>>(
        requestKey,
        () => _executeHistoryRequest(page, size, forceRefresh),
        '搜索历史 - page=$page, size=$size',
      );
    } catch (e) {
      LogUtil.error('获取搜索历史失败: $e');
      throw ErrorHandler.createAppException(
        e,
        'Failed to get search history, please try again later'
      );
    }
  }

  /// 执行实际的搜索历史请求
  Future<Map<String, dynamic>> _executeHistoryRequest(int page, int size, bool forceRefresh) async {
    return await RetryUtil.executeWithRetry<Map<String, dynamic>>(
      () async {
        // 生成缓存键
        final cacheKey = '${CacheConstants.searchHistoryCacheKey}_${page}_$size';

        // 如果不是强制刷新，先检查缓存
        if (!forceRefresh) {
          final cacheManager = Get.find<CacheManager>();
          final cachedHistory = await cacheManager.get<Map<String, dynamic>>(
            cacheKey,
            strategy: CacheStrategy.memoryThenPersistent,
            maxAge: CacheConstants.getExpiryForModule('search', type: 'history'),
            fromJson: (json) => json,
          );

          if (cachedHistory != null) {
            LogUtil.debug('SearchRepository: 搜索历史缓存命中 - page=$page, size=$size, 记录数=${(cachedHistory['items'] as List?)?.length ?? 0}');
            return cachedHistory;
          }
        }

        // 构建请求参数
        final params = {
          'page': page.toString(),
          'size': size.toString(),
        };

        // 发送请求
        final response = await HttpManager.get(
          url: '${_baseUrl}/search/history',
          params: params,
        );

        if (response.rawData == null) {
          LogUtil.warn('获取搜索历史失败，响应为空');
          throw ErrorHandler.createAppException(
            'Empty response when getting search history'
          );
        }

        // 解析响应
        final responseData = response.rawData;
        if (responseData is! Map<String, dynamic> || !responseData.containsKey('data')) {
          LogUtil.warn('获取搜索历史响应数据结构异常');
          throw ErrorHandler.createAppException(
            'Invalid search history response format'
          );
        }

        final dataField = responseData['data'];
        if (dataField is! Map<String, dynamic>) {
          LogUtil.warn('搜索历史数据不是Map类型');
          throw ErrorHandler.createAppException(
            'Invalid search history data format'
          );
        }

        // 解析搜索记录
        List<SearchRecord> records = [];

        if (dataField.containsKey('items') && dataField['items'] is List) {
          final List<dynamic> items = dataField['items'];

          for (var item in items) {
            try {
              if (item is Map<String, dynamic>) {
                final record = SearchRecord.fromJson(item);
                records.add(record);
              }
            } catch (e) {
              LogUtil.error('解析搜索历史记录失败: $e, 数据: $item');
            }
          }
        }

        // 提取分页信息
        final int total = dataField['total'] ?? 0;
        final int currentPage = dataField['page'] ?? page;
        final int pageSize = dataField['size'] ?? size;
        final int pages = dataField['pages'] ?? 0;

        // 准备结果
        final result = {
          'items': records,
          'total': total,
          'page': currentPage,
          'size': pageSize,
          'pages': pages
        };

        // 缓存结果（只有当有数据时才缓存）
        if (records.isNotEmpty) {
          final cacheManager = Get.find<CacheManager>();
          await cacheManager.set(
            cacheKey,
            result,
            strategy: CacheStrategy.both,
            expiry: CacheConstants.getExpiryForModule('search', type: 'history'),
            toJson: (data) => data,
          );
          LogUtil.debug('SearchRepository: 缓存搜索历史 - page=$page, size=$size, 记录数=${records.length}');
        }

        LogUtil.debug('成功获取搜索历史: ${records.length}条记录，总共: $total 条');
        return result;
      },
      config: const RetryConfig(
        maxRetries: 2,
        baseDelayMs: 500,
        maxDelayMs: 3000,
        enableNetworkCheck: true,
        enableJitter: true,
      ),
      operationName: '获取搜索历史',
      onRetry: (retryState) {
        LogUtil.info('获取搜索历史重试中，第${retryState.currentAttempt}次尝试，延迟${retryState.delayMs}ms');
      },
    );
  }
  
  /// 删除搜索记录
  ///
  /// 删除指定的搜索记录
  /// [recordId] 记录ID
  /// 返回是否成功
  Future<bool> deleteSearchRecord(int recordId) async {
    return await RetryUtil.executeWithRetry<bool>(
      () async {
        LogUtil.debug('删除搜索记录，ID: $recordId');
        
        // 发送请求
        final response = await HttpManager.put(
          url: '${_baseUrl}/search/history/$recordId',
        );
        
        if (response.rawData == null) {
          LogUtil.warn('删除搜索记录失败，响应为空');
          throw ErrorHandler.createAppException(
            'Empty response when deleting search record'
          );
        }
        
        // 解析响应
        final responseData = response.rawData;
        if (responseData is! Map<String, dynamic> || !responseData.containsKey('data')) {
          LogUtil.warn('删除搜索记录响应数据结构异常');
          throw ErrorHandler.createAppException(
            'Invalid delete search record response format'
          );
        }
        
        final bool success = responseData['data'] == true;
        
        if (success) {
          // 清理搜索历史缓存
          await _clearSearchHistoryCache();
          LogUtil.debug('成功删除搜索记录: $recordId，已清理缓存');
        } else {
          LogUtil.warn('删除搜索记录失败: $recordId');
        }

        return success;
      },
      config: const RetryConfig(
        maxRetries: 1,
        baseDelayMs: 300,
        maxDelayMs: 2000,
        enableNetworkCheck: true,
        enableJitter: true,
      ),
      operationName: '删除搜索记录',
      onRetry: (retryState) {
        LogUtil.info('删除搜索记录重试中，第${retryState.currentAttempt}次尝试，延迟${retryState.delayMs}ms');
      },
    );
  }
  
  /// 删除所有搜索记录
  ///
  /// 清空用户的所有搜索历史
  /// 返回删除的数量和消息
  Future<Map<String, dynamic>> deleteAllSearchRecords() async {
    return await RetryUtil.executeWithRetry<Map<String, dynamic>>(
      () async {
        LogUtil.debug('开始删除所有搜索记录');
        
        // 发送请求
        final response = await HttpManager.put(
          url: '${_baseUrl}/search/history/batch',
        );
        
        if (response.rawData == null) {
          LogUtil.warn('删除所有搜索记录失败，响应为空');
          throw ErrorHandler.createAppException(
            'Empty response when deleting all search records'
          );
        }
        
        // 解析响应
        final responseData = response.rawData;
        if (responseData is! Map<String, dynamic> ||
            !responseData.containsKey('data') ||
            responseData['data'] is! Map<String, dynamic>) {
          LogUtil.warn('删除所有搜索记录响应数据结构异常');
          throw ErrorHandler.createAppException(
            'Invalid delete all search records response format'
          );
        }
        
        final dataField = responseData['data'];
        final int deletedCount = dataField['deleted_count'] ?? 0;
        final String message = dataField['message'] ?? '';
        
        if (deletedCount > 0) {
          // 清理搜索历史缓存
          await _clearSearchHistoryCache();
          LogUtil.debug('成功删除所有搜索记录: $deletedCount 条，已清理缓存');

          return {
            'deleted_count': deletedCount,
            'message': message
          };
        } else {
          LogUtil.warn('删除所有搜索记录失败: $message');
          throw ErrorHandler.createAppException(
            'Failed to delete search records: $message'
          );
        }
      },
      config: const RetryConfig(
        maxRetries: 1,
        baseDelayMs: 300,
        maxDelayMs: 2000,
        enableNetworkCheck: true,
        enableJitter: true,
      ),
      operationName: '删除所有搜索记录',
      onRetry: (retryState) {
        LogUtil.info('删除所有搜索记录重试中，第${retryState.currentAttempt}次尝试，延迟${retryState.delayMs}ms');
      },
    );
  }



  /// 清理搜索历史缓存和相关搜索记录缓存
  Future<void> _clearSearchHistoryCache() async {
    try {
      final cacheManager = Get.find<CacheManager>();

      // 清理搜索历史缓存键
      final historyKeys = [
        '${CacheConstants.searchHistoryCacheKey}_1_10',
        '${CacheConstants.searchHistoryCacheKey}_1_20',
        '${CacheConstants.searchHistoryCacheKey}_1_50',
      ];

      for (final key in historyKeys) {
        await cacheManager.remove(key);
      }

      // 清理搜索记录缓存
      // 直接清理以搜索记录缓存前缀开头的缓存键
      await _clearSearchResultCache(cacheManager);

      LogUtil.debug('SearchRepository: 已清理搜索历史缓存和搜索记录缓存');
    } catch (e) {
      LogUtil.error('SearchRepository: 清理搜索历史缓存失败 - $e');
    }
  }

  /// 清理搜索记录缓存
  Future<void> _clearSearchResultCache(CacheManager cacheManager) async {
    try {
      // 清理常见的搜索记录缓存键模式
      // 由于搜索记录缓存键格式为: search_result_${keyword}_${page}_${size}
      // 我们清理一些常见的缓存键，这样可以避免依赖SearchStateManager

      // 清理内存缓存中以搜索结果前缀开头的键
      final memoryKeys = await cacheManager.getKeys(strategy: CacheStrategy.memoryOnly);
      int clearedCount = 0;

      for (final key in memoryKeys) {
        if (key.startsWith(CacheConstants.searchResultCachePrefix)) {
          await cacheManager.remove(key, strategy: CacheStrategy.both);
          clearedCount++;
        }
      }

      LogUtil.debug('SearchRepository: 清理了${clearedCount}个搜索记录缓存');
    } catch (e) {
      LogUtil.error('SearchRepository: 清理搜索记录缓存失败 - $e');
    }
  }
}