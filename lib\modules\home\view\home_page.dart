import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/modules/home/<USER>/home_controller.dart';
import 'package:rolio/modules/role/view/recommend_page.dart';
import 'package:rolio/modules/sessions/view/sessions_page.dart';
import 'package:rolio/modules/user/view/user_page.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Obx(() {
        // 根据当前选中的tab返回对应页面
        switch (controller.currentTabIndex.value) {
          case 0:
            return const RecommendPage();
          case 1:
            return const SessionsPage();
          case 2:
            return const UserPage();
          default:
            return const RecommendPage();
        }
      }),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  // 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return Obx(() => BottomNavigationBar(
      currentIndex: controller.currentTabIndex.value,
      onTap: controller.changeTabIndex,
      backgroundColor: Colors.black,
      selectedItemColor: Colors.white,
      unselectedItemColor: Colors.grey,
      showSelectedLabels: false,
      showUnselectedLabels: false,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.recommend),
          label: '推荐',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat),
          label: '会话',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: '用户',
        ),
      ],
    ));
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      centerTitle: false,
      backgroundColor: Colors.black,
      elevation: 0,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.black,
        statusBarIconBrightness: Brightness.light,
      ),
      title: const Text(
        StringsConsts.appName,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 22,
        ),
      ),
      actions: [
        // 仅在推荐页面显示搜索按钮
        Obx(() => controller.currentTabIndex.value == 0 
          ? IconButton(
              onPressed: controller.goToSearch,
              icon: const Icon(
                Icons.search,
                color: Colors.white,
              ),
              tooltip: 'Search',
            )
          : const SizedBox.shrink()
        ),
        IconButton(
          onPressed: controller.forceRefreshCurrentTab,
          icon: const Icon(
            Icons.refresh,
            color: Colors.white,
          ),
          tooltip: 'Refresh',
        ),
      ],
    );
  }
}
