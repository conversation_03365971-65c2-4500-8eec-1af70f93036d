import 'package:get/get.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/sessions/model/session.dart';
import 'package:rolio/common/models/page_data.dart';

/// 接口提供者绑定
/// 
/// 负责提供所有接口的默认实现，确保系统在任何情况下都能找到接口实现
class InterfaceProvidersBinding implements Bindings {
  @override
  void dependencies() {
    LogUtil.info('注册接口默认实现...');
    
    // 注册轻量级的接口实现
    if (!Get.isRegistered<IRoleProvider>()) {
      Get.put<IRoleProvider>(DefaultRoleProvider(), permanent: true);
      LogUtil.debug('注册默认实现: DefaultRoleProvider');
    }
    
    if (!Get.isRegistered<ISessionProvider>()) {
      Get.put<ISessionProvider>(DefaultSessionProvider(), permanent: true);
      LogUtil.debug('注册默认实现: DefaultSessionProvider');
    }
    
    if (!Get.isRegistered<IChatService>()) {
      Get.put<IChatService>(DefaultChatService(), permanent: true);
      LogUtil.debug('注册默认实现: DefaultChatService');
    }
    
    LogUtil.info('接口默认实现注册完成');
  }
}

/// 默认角色提供者
/// 
/// 提供基础的角色信息，在完整实现加载前作为备用
class DefaultRoleProvider implements IRoleProvider {
  @override
  Future<AiRole?> getRoleById(int roleId) async {
    return AiRole(
      id: roleId,
      name: 'AI助手',
      description: '默认AI助手',
      avatarUrl: StringsConsts.recommendDefaultAvatarUrl,
      coverUrl: StringsConsts.recommendDefaultCoverUrl,
      tags: ['助手'],
      position: 0,
    );
  }
  
  @override
  Stream<List<AiRole>> getRoles() {
    return Stream.value([]);
  }
  
  @override
  Future<String?> getAvatarUrlById(int roleId) async {
    return StringsConsts.recommendDefaultAvatarUrl;
  }
  
  @override
  Future<String?> getCoverUrlById(int roleId) async {
    return StringsConsts.recommendDefaultCoverUrl;
  }
  
  @override
  Future<String?> getRoleNameById(int roleId) async {
    return 'AI助手';
  }
  
  @override
  Future<AiRole?> getNextRecommendRole(int currentRoleId) async {
    return null;
  }
  
  @override
  Future<AiRole?> getPreviousRecommendRole(int currentRoleId) async {
    return null;
  }
  
  @override
  Future<AiRole?> getNextSessionRole(int currentRoleId) async {
    return null;
  }
  
  @override
  Future<AiRole?> getPreviousSessionRole(int currentRoleId) async {
    return null;
  }
  
  /// 更新角色的会话ID - 默认实现
  /// 
  /// 为与RoleProvider兼容而添加的方法
  /// [roleId] 角色ID
  /// [conversationId] 会话ID
  /// [updatedRole] 更新后的角色对象
  Future<void> updateRoleConversationId(int roleId, int conversationId, [AiRole? updatedRole]) async {
    LogUtil.warn('DefaultRoleProvider.updateRoleConversationId被调用，但不执行实际操作');
    // 默认实现只记录日志，不执行实际操作
    return;
  }
}

/// 默认会话提供者
/// 
/// 提供基础的会话功能，在完整实现加载前作为备用
class DefaultSessionProvider implements ISessionProvider {
  @override
  final RxBool isLoading = false.obs;
  
  @override
  final RxList<Session> sessions = <Session>[].obs;
  
  // 内部存储
  final _pageDataRx = PageData<Session>(
    items: [],
    total: 0,
    page: 1,
    size: 10,
  );
  
  @override
  PageData<Session> get pageData => _pageDataRx;
  
  @override
  Future<void> refreshSessions({bool refresh = false}) async {
    LogUtil.warn('使用默认会话提供者刷新会话，无实际功能');
  }
  
  @override
  Stream<List<dynamic>> getSessions() {
    return Stream.value([]);
  }
  
  // 额外提供的方法，在接口中不需要
  Future<void> updateConversation({
    required int conversationId,
    required String lastMessage,
    required DateTime timestamp,
  }) async {
    LogUtil.warn('使用默认会话提供者更新会话，无实际功能');
  }
  
  @override
  Future<void> loadSessions({int page = 1, int pageSize = 10}) async {
    LogUtil.warn('使用默认会话提供者加载会话，无实际功能');
  }
  
  @override
  Future<void> loadMoreSessions({required int page, required int pageSize}) async {
    LogUtil.warn('使用默认会话提供者加载更多会话，无实际功能');
  }
  
  @override
  Future<bool> deleteSession(int sessionId) async {
    LogUtil.warn('使用默认会话提供者删除会话，无实际功能');
    return false;
  }
  
  @override
  Future<bool> pinSession(int sessionId) async {
    LogUtil.warn('使用默认会话提供者置顶会话，无实际功能');
    return false;
  }
  
  @override
  Future<bool> unpinSession(int sessionId) async {
    LogUtil.warn('使用默认会话提供者取消置顶会话，无实际功能');
    return false;
  }
  
  @override
  Future<bool> hideSession(int sessionId) async {
    LogUtil.warn('使用默认会话提供者隐藏会话，无实际功能');
    return false;
  }

  @override
  void updateLatestMessage({required int conversationId, required String latestMessage, required DateTime timestamp}) {
    // TODO: implement updateLatestMessage
    throw UnimplementedError();
  }
}

/// 默认聊天服务
/// 
/// 提供基础的聊天功能，在完整实现加载前作为备用
/// 简化版本：只保留基本属性和必要的实现
class DefaultChatService implements IChatService {
  // 基本属性
  @override
  final RxList<Message> messages = <Message>[].obs;
  
  @override
  final RxBool isAiReplying = false.obs;
  
  @override
  final RxBool isLoadingMore = false.obs;
  
  @override
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  
  @override
  final RxBool isLoadingRoleInfo = false.obs;
  
  int _currentConversationId = 0;
  int _currentAiRoleId = 0;
  bool _isConnected = false;
  bool _hasMoreMessages = false;
  bool _isLoadingHistory = false;
  
  @override
  int get currentConversationId => _currentConversationId;
  
  @override
  int get currentAiRoleId => _currentAiRoleId;
  
  @override
  bool get isConnected => _isConnected;
  
  @override
  bool get hasMoreMessages => _hasMoreMessages;
  
  @override
  bool get isLoadingHistory => _isLoadingHistory;
  
  @override
  bool get aiReplyingState => isAiReplying.value;
  
  @override
  set aiReplyingState(bool value) {
    isAiReplying.value = value;
  }
  
  // 简化的方法实现
  @override
  Future<void> sendMessage(String content) async {
    LogUtil.warn('使用默认聊天服务发送消息，无实际功能');
  }
  
  @override
  void sendTypingStatus(bool isTyping) {
    LogUtil.warn('使用默认聊天服务发送输入状态，无实际功能');
  }
  
  @override
  Future<void> loadHistoryMessages(int conversationId, {bool resetPage = false, bool forceRefresh = false}) async {
    LogUtil.warn('使用默认聊天服务加载历史消息，无实际功能');
  }
  
  @override
  Future<void> loadMoreHistoryMessages(int conversationId) async {
    LogUtil.warn('使用默认聊天服务加载更多历史消息，无实际功能');
  }
  
  @override
  void switchConversation(int conversationId, int aiRoleId) {
    _currentConversationId = conversationId;
    _currentAiRoleId = aiRoleId;
    LogUtil.warn('使用默认聊天服务切换会话，无实际功能');
  }
  
  @override
  Future<void> disconnect() async {
    _isConnected = false;
    LogUtil.warn('使用默认聊天服务断开连接，无实际功能');
  }
  
  @override
  void handleAiReplyStart() {
    aiReplyingState = true;
  }
  
  @override
  void handleAiReplyEnd() {
    aiReplyingState = false;
  }
  
  @override
  Stream<List<dynamic>> getMessages() {
    return messages.stream.map((list) => list.cast<dynamic>().toList());
  }
  
  @override
  Stream<bool> getIsAiReplying() {
    return isAiReplying.stream;
  }
  
  @override
  Stream<bool> getIsLoadingHistory() {
    return Stream.value(_isLoadingHistory);
  }
  
  // 其他必要方法的简化实现
  @override
  void addSystemMessage(String content) {
    LogUtil.warn('使用默认聊天服务添加系统消息，无实际功能');
  }
  
  @override
  void clearSystemMessage(String content) {
    LogUtil.warn('使用默认聊天服务清除系统消息，无实际功能');
  }
  
  @override
  void clearAllConnectionMessages() {
    LogUtil.warn('使用默认聊天服务清除所有连接消息，无实际功能');
  }
  
  @override
  Future<bool> sendMessageViaWebSocket(Message message, String content) async {
    LogUtil.warn('使用默认聊天服务通过WebSocket发送消息，无实际功能');
    return false;
  }
  
  @override
  Future<AiRole?> loadRoleInfo(int aiRoleId) async {
    LogUtil.warn('使用默认聊天服务加载角色信息，无实际功能');
    return null;
  }
  
  @override
  void updateSessionsList() {
    LogUtil.warn('使用默认聊天服务更新会话列表，无实际功能');
  }
  
  @override
  void presetRoleInfo(AiRole role) {
    currentRole.value = role;
  }
  
  @override
  Future<void> setActiveConversation(int conversationId, int aiRoleId) async {
    _currentConversationId = conversationId;
    _currentAiRoleId = aiRoleId;
    LogUtil.warn('使用默认聊天服务设置活跃会话，无实际功能');
  }
  
  @override
  Future<void> addRoleIntroMessage(String intro, {bool forceAdd = false}) async {
    LogUtil.warn('使用默认聊天服务添加角色简介，无实际功能');
  }
  
  @override
  Future<AiRole?> switchRole({bool isNext = true, bool fromSessionsList = false, bool fromRecommendList = false}) async {
    LogUtil.warn('使用默认聊天服务切换角色，无实际功能');
    return null;
  }
} 