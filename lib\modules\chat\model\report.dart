import 'package:rolio/common/enums/report_reason.dart';

class Report {
  final String? id;
  final int conversationId;
  final int aiRoleId;
  final ReportReason reason;
  final String description;
  final List<String> imageUrls;
  final DateTime? createdAt;
  
  Report({
    this.id,
    required this.conversationId,
    required this.aiRoleId,
    required this.reason,
    required this.description,
    this.imageUrls = const [],
    this.createdAt,
  });
  
  // 从JSON创建Report对象
  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      id: json['id'] as String?,
      conversationId: json['conversationId'] as int,
      aiRoleId: json['aiRoleId'] as int,
      reason: ReportReason.fromString(json['reason'] as String),
      description: json['description'] as String,
      imageUrls: (json['imageUrls'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
    );
  }
  
  // 将Report对象转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversationId': conversationId,
      'aiRoleId': aiRoleId,
      'reason': reason.toString().split('.').last,
      'description': description,
      'imageUrls': imageUrls,
      'createdAt': createdAt?.toIso8601String(),
    };
  }
} 