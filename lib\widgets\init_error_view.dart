import 'package:flutter/material.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:get/get.dart';
import 'package:rolio/manager/app_initializer.dart';
import 'package:rolio/routes/routes.dart';

class InitErrorView extends StatelessWidget {
  const InitErrorView({
    super.key, 
    required this.onRetry,
    this.errorMessage = 'Initialization failed',
    this.buttonText = 'Try again',
  });

  final VoidCallback onRetry;
  final String errorMessage;
  final String buttonText;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBGChat,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 错误图标
            Icon(
              Icons.error_outline_rounded,
              size: 80,
              color: AppColors.grey.withOpacity(0.6),
            ),
            
            const SizedBox(height: 40),
            
            // 错误信息
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                errorMessage,
                style: const TextStyle(
                  fontSize: 24,
                  color: AppColors.white,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 60),
            
            // 重试按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: Text(
                  buttonText,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class InitErrorScreen extends StatelessWidget {
  const InitErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return InitErrorView(
      onRetry: () async {
        try {
          // 重新初始化应用
          final appInitializer = AppInitializer();
          await appInitializer.initialize();
          
          // 初始化成功后，导航到主页
          Get.offAllNamed(Routes.homeScreen);
        } catch (e) {
          // 显示错误提示
          ErrorHandler.handleException(
            e,
            message: 'Failed to initialize app',
            showSnackbar: true,
          );
        }
      },
      errorMessage: 'Network connection failed',
    );
  }
} 