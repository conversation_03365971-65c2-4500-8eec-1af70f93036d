import 'package:flutter/material.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/widgets/skeleton/role_card_skeleton.dart';

/// 角色网格骨架组件
///
/// 用于推荐页面等网格布局的骨架屏
class RoleGridSkeleton extends StatelessWidget {
  /// 网格项数量
  final int itemCount;
  
  /// 交叉轴数量（列数）
  final int? crossAxisCount;
  
  /// 子项宽高比
  final double childAspectRatio;
  
  /// 交叉轴间距
  final double crossAxisSpacing;
  
  /// 主轴间距
  final double mainAxisSpacing;
  
  /// 内边距
  final EdgeInsetsGeometry padding;
  
  /// 是否可滚动
  final bool scrollable;
  
  /// 构造函数
  const RoleGridSkeleton({
    Key? key,
    this.itemCount = 6,
    this.crossAxisCount,
    this.childAspectRatio = StringsConsts.recommendCardAspectRatio,
    this.crossAxisSpacing = StringsConsts.recommendGridSpacing,
    this.mainAxisSpacing = StringsConsts.recommendGridSpacing,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    this.scrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: scrollable 
          ? const AlwaysScrollableScrollPhysics() 
          : const NeverScrollableScrollPhysics(),
      slivers: [
        // 顶部间距
        const SliverToBoxAdapter(
          child: SizedBox(height: 12),
        ),
      
        // 网格骨架
        SliverPadding(
          padding: padding,
          sliver: SliverLayoutBuilder(
            builder: (context, constraints) {
              // 计算最佳列数
              final effectiveCrossAxisCount = crossAxisCount ?? 
                  _calculateOptimalColumnCount(constraints.crossAxisExtent);
              
              return SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: effectiveCrossAxisCount,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: crossAxisSpacing,
                  mainAxisSpacing: mainAxisSpacing,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) => const RoleCardSkeleton(
                    type: RoleCardSkeletonType.grid,
                  ),
                  childCount: itemCount,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  /// 根据屏幕宽度计算最佳列数
  int _calculateOptimalColumnCount(double width) {
    if (width < 600) {
      return 2; // 手机屏幕
    } else if (width < 900) {
      return 3; // 小平板
    } else if (width < 1200) {
      return 4; // 大平板
    } else {
      return 5; // 桌面
    }
  }
} 