import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/enums/report_reason.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/modules/chat/controller/report_controller.dart';
import 'package:rolio/modules/chat/view/image_upload_widget.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';

class ReportPage extends GetView<ReportController> {
  final int roleId;
  final String roleName;
  
  const ReportPage({
    Key? key,
    required this.roleId,
    required this.roleName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text('Report "$roleName"', style: const TextStyle(fontSize: 16)),
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: _ReportForm(
          roleId: roleId,
        ),
      ),
    );
  }
}

class _ReportForm extends StatefulWidget {
  final int roleId;
  
  const _ReportForm({
    Key? key,
    required this.roleId,
  }) : super(key: key);
  
  @override
  _ReportFormState createState() => _ReportFormState();
}

class _ReportFormState extends State<_ReportForm> {
  final TextEditingController _descriptionController = TextEditingController();
  ReportReason _selectedReason = ReportReason.incoherent;
  List<String> _imageUrls = []; // For UI display only, won't be actually uploaded
  
  // Get controller
  ReportController get reportController => Get.find<ReportController>();
  
  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            _buildReasonSelector(),
            const SizedBox(height: 10),
            _buildDescriptionField(),
            const SizedBox(height: 10),
            _buildImageUpload(),
            const SizedBox(height: 16),
            _buildSubmitButton(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
  
  Widget _buildReasonSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Reason for Report',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade900,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade800, width: 1),
          ),
          child: ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: ReportReason.values.map((reason) => _buildReasonOption(reason)).toList(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildReasonOption(ReportReason reason) {
    return Theme(
      data: ThemeData.dark(),
      child: RadioListTile<ReportReason>(
        title: Text(
          reason.description,
          style: const TextStyle(color: Colors.white, fontSize: 13),
        ),
        value: reason,
        groupValue: _selectedReason,
        onChanged: (ReportReason? value) {
          if (value != null) {
            setState(() {
              _selectedReason = value;
            });
          }
        },
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
        activeColor: Colors.blueAccent,
        tileColor: Colors.transparent,
      ),
    );
  }
  
  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Problem Description',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          maxLength: StringsConsts.reportMaxDescriptionLength,
          style: const TextStyle(color: Colors.white, fontSize: 13),
          cursorColor: Colors.blueAccent,
          decoration: InputDecoration(
            hintText: 'Please describe the issue in detail',
            hintStyle: TextStyle(color: Colors.grey.shade400, fontSize: 13),
            counterStyle: const TextStyle(color: Colors.grey, fontSize: 11),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade700),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blueAccent, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade700),
            ),
            contentPadding: const EdgeInsets.all(10),
            filled: true,
            fillColor: Colors.grey.shade900,
          ),
        ),
      ],
    );
  }
  
  Widget _buildImageUpload() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Add Images (Max 3)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 80,
          child: ImageUploadWidget(
            imageUrls: _imageUrls,
            onImagesChanged: (updatedUrls) {
              setState(() {
                _imageUrls = updatedUrls;
              });
            },
          ),
        ),
      ],
    );
  }
  
  Widget _buildSubmitButton() {
    return Center(
      child: Obx(() => ElevatedButton(
        onPressed: reportController.isSubmitting.value ? null : _submitReport,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blueAccent,
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          minimumSize: const Size(180, 42),
          disabledBackgroundColor: Colors.blueAccent.withAlpha(102), // 0.4转换为Alpha值
        ),
        child: reportController.isSubmitting.value
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Submit Report',
                style: TextStyle(fontSize: 15, color: Colors.white),
              ),
      )),
    );
  }
  
  void _submitReport() async {
    // Form validation
    if (_descriptionController.text.trim().isEmpty) {
      ToastUtil.error('Please describe the issue');
      return;
    }
    
    try {
      // Submit report - note we don't pass image URLs
      final success = await reportController.submitReport(
        roleId: widget.roleId,
        reason: _selectedReason,
        description: _descriptionController.text.trim(),
      );
      
      if (success) {
        Get.back(); // Return to previous page
        
        // 显示简洁的成功提示，单行文本，无留白
        Get.rawSnackbar(
          messageText: const Text(
            'Report submitted',
            style: TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontWeight: FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.grey.shade800.withAlpha(179), // 0.7转换为Alpha值
          borderRadius: 4,
          margin: const EdgeInsets.fromLTRB(64, 6, 64, 0),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          duration: const Duration(seconds: 2),
          isDismissible: true,
          maxWidth: 200,
          icon: const Icon(
            Icons.check, 
            color: Colors.white, 
            size: 16,
          ),
          snackStyle: SnackStyle.FLOATING,
        );
      } else {
        ErrorHandler.handleException(
          AppException(
            'Report submission failed. Please try again.',
            code: ErrorCodes.GENERAL_ERROR
          ),
        );
      }
    } catch (e) {
      ErrorHandler.handleException(
        AppException(
          'Report submission failed. Please try again.',
          code: ErrorCodes.GENERAL_ERROR,
          originalError: e
        ),
      );
    }
  }
} 