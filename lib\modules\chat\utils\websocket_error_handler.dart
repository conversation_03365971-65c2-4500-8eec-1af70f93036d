
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/common/utils/logger.dart';

/// WebSocket连接错误处理类
class WebSocketErrorHandler {
  /// 显示WebSocket连接失败对话框
  static void showWebSocketErrorDialog(
    BuildContext context, {
    required VoidCallback onRetry,
    String errorMessage = 'WebSocket connection failed',
    String buttonText = 'Try again',
  }) {
    ErrorHandler.showWebSocketErrorPage(
      context,
      onRetry: onRetry,
      errorMessage: errorMessage,
      buttonText: buttonText,
    );
  }

  /// 处理WebSocket连接失败
  static void handleWebSocketError(
    BuildContext context, {
    required VoidCallback onRetry,
    dynamic error,
    String? errorMessage,
  }) {
    // 检查当前context是否有效
    if (context.mounted) {
      // 创建WebSocket异常对象
      final wsException = WebSocketException(
        errorMessage ?? 'WebSocket connection failed',
        code: ErrorCodes.WS_CONNECTION_FAILED,
        originalError: error
      );
      
      // 使用统一的异常处理方法
      ErrorHandler.handleException(
        wsException,
        showSnackbar: false, // 使用全屏显示，不显示snackbar
      );
      
      // 显示全屏错误页面
      ErrorHandler.showWebSocketErrorPage(
        context,
        onRetry: onRetry,
        errorMessage: errorMessage ?? 'WebSocket connection failed',
      );
    }
  }
  
  /// 处理WebSocket消息发送失败
  static void handleMessageSendFailed(
    BuildContext context, {
    required VoidCallback onRetry,
    dynamic error,
  }) {
    if (context.mounted) {
      final errorMessage = 'Failed to send message, please check your connection';
      
      // 创建WebSocket异常对象
      final wsException = WebSocketException(
        errorMessage,
        code: ErrorCodes.WS_MESSAGE_SEND_FAILED,
        originalError: error
      );
      
      // 使用统一的异常处理方法
      ErrorHandler.handleException(
        wsException,
        showSnackbar: false, // 使用全屏显示，不显示snackbar
      );
      
      // 显示全屏错误页面
      ErrorHandler.showWebSocketErrorPage(
        context,
        onRetry: onRetry,
        errorMessage: errorMessage,
      );
    }
  }
  
  /// 设置WebSocket错误事件监听
  static Worker? setupWebSocketErrorListener(
    BuildContext context, {
    required Function(BuildContext, dynamic) onError,
  }) {
    LogUtil.info('设置WebSocket错误事件监听');
    return ever(GlobalEventState.to.websocketError, (Map<String, dynamic>? errorData) {
      if (errorData != null) {
        LogUtil.info('接收到WebSocket错误事件: $errorData');

        // 处理错误事件
        if (context.mounted) {
          final error = errorData['error'];
          onError(context, error);
        }
      }
    });
  }
  
  /// 移除WebSocket错误事件监听
  static void removeWebSocketErrorListener(Worker? worker) {
    if (worker != null) {
      worker.dispose();
      LogUtil.info('已移除WebSocket错误事件监听');
    }
  }
} 