import 'package:flutter/material.dart';

/// 闪烁效果组件
/// 
/// 用于骨架屏加载状态，提供从左到右的闪烁动画效果
class ShimmerWidget extends StatefulWidget {
  /// 子组件
  final Widget child;
  
  /// 高亮颜色
  final Color highlightColor;
  
  /// 基础颜色
  final Color baseColor;
  
  /// 动画持续时间
  final Duration duration;

  /// 构造函数
  const ShimmerWidget({
    Key? key,
    required this.child,
    this.highlightColor = const Color(0x33FFFFFF),
    this.baseColor = const Color(0x1AFFFFFF),
    this.duration = const Duration(milliseconds: 1500),
  }) : super(key: key);

  @override
  State<ShimmerWidget> createState() => _ShimmerWidgetState();
}

class _ShimmerWidgetState extends State<ShimmerWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    // 创建动画
    _animation = Tween<double>(begin: -2.0, end: 2.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOutSine,
      ),
    );
    
    // 重复动画
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: const [
                0.0,
                0.5,
                1.0,
              ],
              begin: Alignment(_animation.value, 0.0),
              end: Alignment(_animation.value + 1, 0.0),
              tileMode: TileMode.clamp,
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: widget.child,
    );
  }
} 