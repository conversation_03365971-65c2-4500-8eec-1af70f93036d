import 'package:rolio/modules/chat/model/message.dart';
import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';

/// 聊天服务接口
/// 
/// 定义与AI角色聊天的核心功能，解耦聊天逻辑与其他模块
abstract class IChatService {
  /// 当前消息列表
  RxList<Message> get messages;
  
  /// AI是否正在回复
  RxBool get isAiReplying;
  
  /// 当前会话ID
  int get currentConversationId;
  
  /// 当前AI角色ID
  int get currentAiRoleId;
  
  /// 是否已连接到会话
  bool get isConnected;
  
  /// 是否有更多历史消息
  bool get hasMoreMessages;
  
  /// 是否正在加载历史消息
  bool get isLoadingHistory;
  
  /// 是否正在加载更多消息
  RxBool get isLoadingMore;
  
  /// 当前角色信息
  Rx<AiRole?> get currentRole;
  
  /// 是否正在加载角色信息
  RxBool get isLoadingRoleInfo;
  
  /// 发送消息到当前会话
  /// 
  /// [content] 消息内容
  Future<void> sendMessage(String content);
  
  /// 发送正在输入状态
  /// 
  /// [isTyping] 是否正在输入
  void sendTypingStatus(bool isTyping);
  
  /// 加载历史消息
  /// 
  /// [conversationId] 会话ID
  /// [resetPage] 是否重置页码（默认为false，表示加载下一页）
  /// [forceRefresh] 是否强制刷新（默认为false，表示优先使用缓存）
  Future<void> loadHistoryMessages(int conversationId, {bool resetPage = false, bool forceRefresh = false});
  
  /// 加载更多历史消息
  /// 
  /// [conversationId] 会话ID
  /// 不重置页码，专门用于下滑加载更多历史消息
  Future<void> loadMoreHistoryMessages(int conversationId);
  
  /// 切换到指定会话
  /// 
  /// [conversationId] 会话ID
  /// [aiRoleId] AI角色ID
  void switchConversation(int conversationId, int aiRoleId);
  
  /// 添加系统消息
  /// 
  /// [content] 消息内容
  void addSystemMessage(String content);
  
  /// 添加角色简介系统消息
  /// 
  /// [intro] 角色简介内容
  /// [forceAdd] 是否强制添加，即使消息列表不为空
  Future<void> addRoleIntroMessage(String intro, {bool forceAdd = false});
  
  /// 清除特定内容的系统消息
  /// 
  /// [content] 系统消息内容
  void clearSystemMessage(String content);
  
  /// AI回复状态
  bool get aiReplyingState;
  
  /// 设置AI回复状态
  set aiReplyingState(bool value);
  
  /// 处理AI回复开始
  void handleAiReplyStart();
  
  /// 处理AI回复结束
  void handleAiReplyEnd();
  
  /// 通过WebSocket发送消息
  /// 
  /// [message] 要发送的消息对象
  /// [content] 消息内容
  /// 返回是否发送成功
  Future<bool> sendMessageViaWebSocket(Message message, String content);
  
  /// 加载角色信息
  /// 
  /// [aiRoleId] AI角色ID
  /// 返回角色信息，如果获取失败则返回null
  Future<AiRole?> loadRoleInfo(int aiRoleId);
  
  /// 切换角色
  /// 
  /// [isNext] 是否切换到下一个角色，true表示下一个，false表示上一个
  /// [fromSessionsList] 是否从会话列表获取角色，true表示从会话列表，false表示从推荐列表
  /// [fromRecommendList] 是否从推荐列表获取角色，true表示从推荐列表
  /// 返回新的角色信息，如果切换失败则返回null
  Future<AiRole?> switchRole({bool isNext = true, bool fromSessionsList = false, bool fromRecommendList = false});
  
  
  /// 更新会话列表
  /// 
  /// 确保会话列表显示最新消息
  void updateSessionsList();
  
  /// 预设角色信息
  /// 
  /// 在切换会话前调用，确保UI能立即显示正确的角色信息
  /// [role] 预设的角色信息
  void presetRoleInfo(AiRole role);
}