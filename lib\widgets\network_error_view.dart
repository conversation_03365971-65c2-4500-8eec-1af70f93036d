import 'package:flutter/material.dart';
import 'package:rolio/common/constants/colors_constants.dart';

class NetworkErrorView extends StatelessWidget {
  const NetworkErrorView({
    super.key, 
    required this.onRetry,
    this.errorMessage = 'Sorry, network error',
    this.buttonText = 'Try again',
  });

  final VoidCallback onRetry;
  final String errorMessage;
  final String buttonText;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.scaffoldBGChat,
      width: double.infinity,
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 网络错误图标
          Icon(
            Icons.wifi_off_rounded,
            size: 80,
            color: AppColors.grey.withOpacity(0.6),
          ),
          
          const SizedBox(height: 40),
          
          // 错误信息
          Text(
            errorMessage,
            style: const TextStyle(
              fontSize: 24,
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 60),
          
          // 重试按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100.0),
                ),
                padding: const EdgeInsets.symmetric(vertical: 15),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 创建一个全屏网络错误对话框的方法
void showNetworkErrorFullScreenDialog(
  BuildContext context, {
  required VoidCallback onRetry,
  String errorMessage = 'Sorry, network error',
  String buttonText = 'Try again',
  bool barrierDismissible = false,
}) {
  showDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    barrierColor: AppColors.scaffoldBGChat.withOpacity(0.9),
    builder: (context) => WillPopScope(
      onWillPop: () async => barrierDismissible,
      child: NetworkErrorView(
        onRetry: () {
          Navigator.of(context).pop();
          onRetry();
        },
        errorMessage: errorMessage,
        buttonText: buttonText,
      ),
    ),
  );
} 