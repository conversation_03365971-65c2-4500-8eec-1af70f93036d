import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/chat/controller/chat_controller.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';
import 'package:rolio/common/utils/image_preloader.dart'; // 添加图片预加载工具

/// AI输入中气泡组件
/// 
/// 显示AI正在输入的动画气泡
class AITypingBubble extends StatefulWidget {
  /// AI头像URL
  final String? avatarUrl;

  const AITypingBubble({
    Key? key,
    this.avatarUrl,
  }) : super(key: key);

  @override
  State<AITypingBubble> createState() => _AITypingBubbleState();
}

class _AITypingBubbleState extends State<AITypingBubble> with SingleTickerProviderStateMixin {
  // 动画控制器
  late AnimationController _controller;
  
  // 动画
  late Animation<double> _animation1;
  late Animation<double> _animation2;
  late Animation<double> _animation3;
  
  // 透明度控制
  double _opacity = 0.0;
  
  // 当前角色ID
  int _currentRoleId = 0;
  
  // 图片预加载器
  ImagePreloader get _imagePreloader => Get.find<ImagePreloader>();
  
  // 头像是否已预加载
  bool _isAvatarPreloaded = false;

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    
    // 创建三个点的动画，每个点有不同的延迟
    _animation1 = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
      ),
    );
    
    _animation2 = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.7, curve: Curves.easeOut),
      ),
    );
    
    _animation3 = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.6, 1.0, curve: Curves.easeOut),
      ),
    );
    
    // 循环播放动画
    _controller.repeat(reverse: true);
    
    // 延迟设置透明度，实现淡入效果
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {
          _opacity = 1.0;
        });
      }
    });
    
    // 获取当前角色ID
    _getCurrentRoleId();
    
    // 预加载头像
    _preloadAvatar();
  }
  
  // 预加载头像
  void _preloadAvatar() {
    if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty) {
      // 检查头像是否已经预加载
      _isAvatarPreloaded = _imagePreloader.isImagePreloaded(widget.avatarUrl!);
      
      if (!_isAvatarPreloaded) {
        // 高优先级预加载头像
        _imagePreloader.preloadImage(
          widget.avatarUrl!,
          width: 32,
          height: 32,
          priority: ImagePreloadPriority.high,
          onComplete: (success) {
            if (mounted) {
              setState(() {
                _isAvatarPreloaded = success;
              });
            }
          }
        );
      } else {
        LogUtil.debug('AITypingBubble: 头像已预加载: ${widget.avatarUrl}');
      }
    }
  }
  
  // 获取当前角色ID
  void _getCurrentRoleId() {
    try {
      // 尝试从ChatController中获取当前角色ID
      final ChatController chatController = Get.find<ChatController>();
      _currentRoleId = chatController.currentAiRoleId;
      LogUtil.debug('AITypingBubble: 获取到当前角色ID: $_currentRoleId');
    } catch (e) {
      LogUtil.error('AITypingBubble: 获取当前角色ID失败: $e');
    }
  }
  
  // 导航到角色详情页
  void _navigateToRoleDetail() {
    // 在点击时重新获取最新的角色ID，确保使用当前活跃的角色
    try {
      final ChatController chatController = Get.find<ChatController>();
      _currentRoleId = chatController.currentAiRoleId;
      LogUtil.debug('AITypingBubble: 点击时获取最新角色ID: $_currentRoleId');
    } catch (e) {
      LogUtil.error('AITypingBubble: 点击时获取当前角色ID失败: $e');
    }

    if (_currentRoleId <= 0) {
      LogUtil.error('AITypingBubble: 无法导航到角色详情页：无效的角色ID $_currentRoleId');
      return;
    }

    LogUtil.info('AITypingBubble: 导航到角色详情页，角色ID: $_currentRoleId');
    RouterManager.navigateTo(
      Routes.roleDetailScreen,
      arguments: {'roleId': _currentRoleId}
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _opacity,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // AI头像
          if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: GestureDetector(
                onTap: _navigateToRoleDetail, // 添加点击头像跳转到角色详情页
                child: _buildAvatar(widget.avatarUrl!, size: 32),
              ),
            ),
          
          // 气泡容器
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A2A), // 深色背景
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildDot(_animation1),
                    const SizedBox(width: 4),
                    _buildDot(_animation2),
                    const SizedBox(width: 4),
                    _buildDot(_animation3),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
  
  // 构建头像，使用缓存优化加载
  Widget _buildAvatar(String url, {double size = 32}) {
    return CircleAvatar(
      radius: size / 2,
      backgroundColor: Colors.transparent,
      child: ClipOval(
        child: CachedNetworkImage(
          imageUrl: url,
          fit: BoxFit.cover,
          width: size,
          height: size,
          placeholder: (context, url) => Container(
            color: Colors.grey[200],
            width: size,
            height: size,
          ),
          errorWidget: (context, url, error) => Icon(
            Icons.person,
            size: size * 0.6,
            color: Colors.grey[400],
          ),
          // 如果图片已预加载，立即显示
          fadeInDuration: _isAvatarPreloaded
              ? Duration.zero
              : const Duration(milliseconds: 300),
          // 添加内存缓存
          memCacheHeight: size.toInt() * 2,
          memCacheWidth: size.toInt() * 2,
        ),
      ),
    );
  }
  
  // 构建动画点
  Widget _buildDot(Animation<double> animation) {
    return Transform.translate(
      offset: Offset(0, -3 * animation.value),
      child: Container(
        width: 6,
        height: 6,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
} 