class RefreshTokenReq {
  final String curToken;

  RefreshTokenReq({required this.curToken});

  factory RefreshTokenReq.fromJson(Map<String, dynamic> json) {
    return RefreshTokenReq(
      curToken: json['curToken'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'curToken': curToken,
    };
  }
}

class RefreshTokenResp {
  final String newToken;

  RefreshTokenResp({required this.newToken});

  factory RefreshTokenResp.fromJson(Map<String, dynamic> json) {
    return RefreshTokenResp(
      newToken: json['newToken'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'newToken': newToken,
    };
  }
}
