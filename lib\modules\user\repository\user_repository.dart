import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'dart:math' as math;

/// 用户头像仓库
/// 负责管理用户头像的相关操作
class UserAvatarRepository {
  // Firebase Auth实例
  final firebase_auth.FirebaseAuth _auth = firebase_auth.FirebaseAuth.instance;
  
  // 获取默认头像URL
  String get defaultAvatarUrl => StringsConsts.defaultAvatarUrl;
  
  // 随机获取一个头像URL
  String getRandomAvatarUrl() {
    final random = math.Random();
    return StringsConsts.avatarUrls[random.nextInt(StringsConsts.avatarUrls.length)];
  }
  
  /// 更新用户显示名称
  Future<bool> updateDisplayName(String displayName) async {
    try {
      final firebaseUser = _auth.currentUser;
      if (firebaseUser == null) {
        LogUtil.warn('无法更新用户名：当前没有登录用户');
        return false;
      }
      
      // 更新Firebase用户名
      await firebaseUser.updateDisplayName(displayName);
      LogUtil.info('用户名已更新: $displayName');
      return true;
    } catch (e) {
      LogUtil.error('更新用户名失败: $e');
      ErrorHandler.handleException(
        AppException('failed to update display name', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: true,
      );
      return false;
    }
  }
  
  /// 更新用户头像
  Future<bool> updatePhotoURL(String photoURL) async {
    try {
      final firebaseUser = _auth.currentUser;
      if (firebaseUser == null) {
        LogUtil.warn('无法更新头像：当前没有登录用户');
        return false;
      }
      
      // 验证头像URL是否在预定义列表中
      if (!StringsConsts.avatarUrls.contains(photoURL)) {
        LogUtil.warn('头像URL不在预定义列表中: $photoURL');
        ErrorHandler.handleException(
          AppException('invalid avatar URL', code: ErrorCodes.BUSINESS_ERROR),
          showSnackbar: true,
        );
        return false;
      }
      
      // 更新Firebase用户头像
      await firebaseUser.updatePhotoURL(photoURL);
      LogUtil.info('用户头像已更新: $photoURL');
      return true;
    } catch (e) {
      LogUtil.error('更新用户头像失败: $e');
      ErrorHandler.handleException(
        AppException('failed to update user avatar', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: true,
      );
      return false;
    }
  }
  
  /// 为当前用户设置随机头像（如果没有头像）
  Future<bool> setRandomAvatarIfNeeded() async {
    try {
      final firebaseUser = _auth.currentUser;
      if (firebaseUser == null) {
        LogUtil.warn('无法设置随机头像：当前没有登录用户');
        return false;
      }
      
      // 如果用户没有头像，则设置一个随机头像
      if (firebaseUser.photoURL == null || firebaseUser.photoURL!.isEmpty) {
        final randomAvatar = getRandomAvatarUrl();
        await firebaseUser.updatePhotoURL(randomAvatar);
        LogUtil.info('已为用户设置随机头像: $randomAvatar');
        return true;
      }
      
      return false; // 用户已有头像，未进行更改
    } catch (e) {
      LogUtil.error('设置随机头像失败: $e');
      return false;
    }
  }
  
  /// 获取所有可用头像列表
  List<String> getAvatarUrls() {
    return StringsConsts.avatarUrls;
  }
} 