import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/role/controller/search_controller.dart' as app_search;
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/modules/role/view/ai_role_card.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';

class SearchPage extends StatelessWidget {
  final app_search.SearchController controller = Get.find<app_search.SearchController>();
  final RecommendController recommendController = Get.find<RecommendController>();

  SearchPage({Key? key}) : super(key: key);

  // 构建搜索框
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: AppColors.black,
      child: Row(
        children: [
          Expanded(
            child: Obx(() {
              // 如果有随机角色名且未输入内容，则显示灰色提示
              final hintText = controller.searchKeyword.isEmpty && controller.randomRoleName.isNotEmpty
                  ? controller.randomRoleName.value
                  : 'Search roles...';
                  
              return TextField(
                controller: controller.textEditingController,
                focusNode: controller.searchFocusNode,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey.shade500,
                    fontStyle: FontStyle.italic,
                  ),
                  prefixIcon: const Icon(Icons.search, color: Colors.grey),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: AppColors.lightBlack,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0.0, horizontal: 16.0),
                ),
                textInputAction: TextInputAction.search,
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    controller.search(value);
                  } else if (controller.randomRoleName.isNotEmpty) {
                    // 如果输入为空但有随机角色名，使用随机角色名搜索
                    controller.search(controller.randomRoleName.value);
                  }
                },
              );
            }),
          ),
          Obx(() {
            // 当有搜索关键词时显示清除按钮，否则显示"cancel"按钮
            return controller.searchKeyword.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: controller.clearSearch,
                  )
                : TextButton(
                    onPressed: () => Get.back(),
                    child: const Text(
                      'cancel',
                      style: TextStyle(color: Colors.white),
                    ),
                  );
          }),
        ],
      ),
    );
  }

  // 构建搜索历史
  Widget _buildSearchHistory() {
    return Obx(() {
      if (controller.searchHistory.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.history,
                size: 64,
                color: Colors.grey.shade600,
              ),
              const SizedBox(height: 16),
              Text(
                'No search history',
                style: TextStyle(
                  color: Colors.grey.shade400,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
      }

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            // 搜索历史标题和编辑/清除按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'History',
                  style: TextStyle(
                    color: Colors.grey.shade300,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // 编辑/清除按钮区
                Obx(() {
                  if (controller.isEditMode.value) {
                    // 编辑模式下显示"Clear All | Done"
                    return Row(
                      children: [
                        TextButton(
                          onPressed: controller.clearAllSearchHistory,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          ),
                          child: const Text(
                            'Clear All',
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ),
                        Text(
                          '|',
                          style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
                        ),
                        TextButton(
                          onPressed: controller.toggleEditMode,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          ),
                          child: const Text(
                            'Done',
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ),
                      ],
                    );
                  } else {
                    // 非编辑模式下显示垃圾桶图标
                    return IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.grey),
                      onPressed: controller.toggleEditMode,
                    );
                  }
                }),
              ],
            ),
            const SizedBox(height: 12),
            // 搜索历史标签流式布局
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: controller.searchHistory.map((record) {
                return Obx(() {
                  // 根据是否为编辑模式显示不同样式
                  return GestureDetector(
                    onTap: () {
                      if (controller.isEditMode.value) {
                        controller.deleteSearchRecord(record.id);
                      } else {
                        controller.searchFromHistory(record);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: controller.isEditMode.value 
                            ? Colors.red.withOpacity(0.2) 
                            : Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: controller.isEditMode.value 
                              ? Colors.red.withOpacity(0.5) 
                              : Colors.grey.withOpacity(0.3)
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            record.keyword,
                            style: TextStyle(
                              color: controller.isEditMode.value 
                                  ? Colors.red.shade200
                                  : Colors.white,
                              fontSize: 14,
                            ),
                          ),
                          if (controller.isEditMode.value) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.close,
                              color: Colors.red.shade300,
                              size: 16,
                            ),
                          ]
                        ],
                      ),
                    ),
                  );
                });
              }).toList(),
            ),
            // 移除之前的Done按钮，因为现在它已经放在顶部了
          ],
        ),
      );
    });
  }
  
  // 构建搜索建议
  Widget _buildSearchSuggestions() {
    return Obx(() {
      if (controller.searchSuggestions.isEmpty) {
        return const SizedBox.shrink();
      }
      
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Text(
              'Suggestions',
              style: TextStyle(
                color: Colors.grey.shade300,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: controller.searchSuggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = controller.searchSuggestions[index];
                  return ListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                    leading: const Icon(
                      Icons.search,
                      color: Colors.grey,
                      size: 20,
                    ),
                    title: Text(
                      suggestion,
                      style: const TextStyle(
                        color: Colors.white, 
                        fontSize: 15,
                      ),
                    ),
                    onTap: () => controller.searchFromSuggestion(suggestion),
                    dense: true,
                    visualDensity: const VisualDensity(vertical: -2),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }

  // 构建搜索结果
  Widget _buildSearchResults() {
    return Obx(() {
      // 显示加载中
      if (controller.isSearching) {
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        );
      }

      // 显示搜索结果为空
      if (controller.searchResults.isEmpty) {
        // 如果是刚开始输入（用户输入后且没有搜索结果且没有获取到建议），显示加载指示器而不是"无结果"
        if (controller.searchKeyword.isNotEmpty &&
            controller.isWaitingSuggestions &&
            controller.searchSuggestions.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          );
        }
        
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'No results found',
                style: TextStyle(
                  color: Colors.grey.shade300,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Try with different keywords',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        );
      }

      // 显示搜索结果
      return Column(
        children: [
          // 结果数量
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              children: [
                Obx(() => Text(
                  '${controller.totalItems} results found',
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 14,
                  ),
                )),
              ],
            ),
          ),
          
          // 搜索结果网格
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => controller.search(controller.searchKeyword.value),
              color: AppColors.primary,
              backgroundColor: Colors.black,
              child: GridView.builder(
                controller: controller.scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.7,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: controller.searchResults.length + (controller.isLoadingMore.value ? 1 : 0),
                itemBuilder: (context, index) {
                  // 加载更多指示器
                  if (index == controller.searchResults.length) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade400),
                        ),
                      ),
                    );
                  }
                  
                  // 角色卡片
                  final role = controller.searchResults[index];
                  return AiRoleCard(
                    role: role,
                    onTap: () => recommendController.startChatWithRole(role),
                  );
                },
              ),
            ),
          ),
        ],
      );
    });
  }

  // 恢复修改后的AppBar
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Search'),
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Column(
        children: [
          // 搜索框
          _buildSearchBar(),
          
          // 主体内容
          Expanded(
            child: Obx(() {
              // 显示搜索建议
              if (controller.searchKeyword.isNotEmpty && 
                  controller.searchResults.isEmpty &&
                  controller.searchSuggestions.isNotEmpty) {
                return _buildSearchSuggestions();
              }
              
              // 显示搜索历史
              if (controller.searchKeyword.isEmpty && controller.searchResults.isEmpty) {
                return _buildSearchHistory();
              }
              
              // 显示搜索结果
              return _buildSearchResults();
            }),
          ),
        ],
      ),
    );
  }
} 