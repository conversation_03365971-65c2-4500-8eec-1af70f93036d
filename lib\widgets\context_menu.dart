import 'package:flutter/material.dart';

/// 自定义长按菜单选项
class ContextMenuItem {
  /// 菜单项图标
  final IconData icon;
  
  /// 菜单项文本
  final String text;
  
  /// 菜单项点击回调
  final Function() onTap;
  
  /// 菜单项图标颜色
  final Color? iconColor;
  
  /// 菜单项文本颜色
  final Color? textColor;
  
  ContextMenuItem({
    required this.icon,
    required this.text,
    required this.onTap,
    this.iconColor,
    this.textColor,
  });
}

/// 全局长按菜单组件
/// 
/// 用于显示长按操作菜单，使用 Dialog 实现，自动处理位置调整
class ContextMenu extends StatefulWidget {
  /// 菜单项列表
  final List<ContextMenuItem> menuItems;
  
  /// 菜单触发点在屏幕上的位置
  final Offset position;
  
  /// 菜单宽度
  final double? width;
  
  /// 消息气泡宽度，用于调整菜单宽度
  final double? bubbleWidth;
  
  /// 菜单背景色
  final Color? backgroundColor;
  
  /// 菜单边框颜色
  final Color? borderColor;
  
  /// 菜单圆角
  final double borderRadius;
  
  /// 菜单阴影
  final double elevation;
  
  /// 是否在菜单外点击时关闭
  final bool closeOnTapOutside;
  
  const ContextMenu({
    Key? key,
    required this.menuItems,
    required this.position,
    this.width,
    this.bubbleWidth,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 8.0,
    this.elevation = 4.0,
    this.closeOnTapOutside = true,
  }) : super(key: key);

  /// 显示长按菜单的静态方法
  static Future<void> show({
    required BuildContext context,
    required List<ContextMenuItem> menuItems,
    required Offset position,
    double? width,
    double? bubbleWidth,
    Color? backgroundColor,
    Color? borderColor,
    double borderRadius = 8.0,
    double elevation = 4.0,
    bool closeOnTapOutside = true,
  }) async {
    // 计算菜单的理想位置
    final Size screenSize = MediaQuery.of(context).size;
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    
    // 使用 showDialog 显示菜单，设置 barrierDismissible 为 false，确保点击外部不会关闭
    await showDialog(
      context: context,
      barrierColor: Colors.transparent, // 透明背景
      barrierDismissible: false, // 点击外部不关闭
      builder: (BuildContext context) {
        return _ContextMenuDialog(
          menuItems: menuItems,
          position: position,
          screenSize: screenSize,
          statusBarHeight: statusBarHeight,
          width: width,
          bubbleWidth: bubbleWidth,
          backgroundColor: backgroundColor,
          borderColor: borderColor,
          borderRadius: borderRadius,
          elevation: elevation,
          closeOnTapOutside: closeOnTapOutside,
        );
      },
    );
  }

  @override
  State<ContextMenu> createState() => _ContextMenuState();
}

class _ContextMenuState extends State<ContextMenu> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    );
    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: widget.width,
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? const Color(0xFF333333),
            border: Border.all(
              color: widget.borderColor ?? Colors.grey.shade800,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(widget.borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: widget.elevation,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: widget.menuItems.map((item) {
              return _buildMenuItem(item);
            }).toList(),
          ),
        ),
      ),
    );
  }
  
  Widget _buildMenuItem(ContextMenuItem item) {
    return InkWell(
      onTap: () {
        // 先关闭菜单，再执行回调
        Navigator.of(context).pop();
        item.onTap();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              item.icon,
              size: 20.0,
              color: item.iconColor ?? Colors.white,
            ),
            const SizedBox(width: 12.0),
            Text(
              item.text,
              style: TextStyle(
                color: item.textColor ?? Colors.white,
                fontSize: 14.0,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 内部使用的菜单对话框组件，处理位置调整
class _ContextMenuDialog extends StatefulWidget {
  final List<ContextMenuItem> menuItems;
  final Offset position;
  final Size screenSize;
  final double statusBarHeight;
  final double? width;
  final double? bubbleWidth;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderRadius;
  final double elevation;
  final bool closeOnTapOutside;

  const _ContextMenuDialog({
    Key? key,
    required this.menuItems,
    required this.position,
    required this.screenSize,
    required this.statusBarHeight,
    this.width,
    this.bubbleWidth,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 8.0,
    this.elevation = 4.0,
    this.closeOnTapOutside = true,
  }) : super(key: key);

  @override
  State<_ContextMenuDialog> createState() => _ContextMenuDialogState();
}

class _ContextMenuDialogState extends State<_ContextMenuDialog> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  // 菜单位置
  late double _left;
  late double _top;
  late double _menuWidth;
  late double _menuHeight;
  
  // 最小菜单宽度
  final double _minMenuWidth = 120.0;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    );
    _controller.forward();

    // 初始化菜单位置和大小
    // 确保有一个最小宽度，防止短消息导致的问题
    _menuWidth = widget.width ?? 
                 (widget.bubbleWidth != null && widget.bubbleWidth! > _minMenuWidth ? 
                  widget.bubbleWidth! : _minMenuWidth);
    
    _menuHeight = widget.menuItems.length * 44.0; // 估算高度
    _calculateMenuPosition();

    // 根据菜单位置确定动画方向
    // 确定菜单是在点击位置上方还是下方
    bool isBelow = widget.position.dy < _top; // 如果菜单的顶部位置比点击位置的y坐标大，说明菜单在下方

    // 设置菜单的动画效果
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _controller.forward();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  // 计算菜单位置
  void _calculateMenuPosition() {
    // 获取点击位置相对于屏幕的y坐标，用于判断菜单是在点击位置上方还是下方
    final double pointY = widget.position.dy;

    // 设置菜单水平位置，确保居中对齐
    _left = widget.position.dx - (_menuWidth / 2);
    
    // 初始菜单位置 - 根据传入position判断应该显示在上方还是下方
    // 默认优先显示在上方，除非明确指示显示在下方
    bool isAboveClickPoint = true; // 默认在点击位置上方显示

    // 调整上方和下方的安全距离
    final double aboveSafeDistance = 58.0; // 上方安全距离
    final double belowSafeDistance = 8.0; // 下方安全距离

    if (pointY - _menuHeight - aboveSafeDistance < widget.statusBarHeight + 10) {
      // 如果上方空间不足，则显示在下方
      isAboveClickPoint = false;
      // 调整垂直偏移量，使菜单更靠近气泡
      _top = pointY + belowSafeDistance;
    } else {
      // 否则显示在上方，增加与气泡的距离
      _top = pointY - _menuHeight - aboveSafeDistance;
    }

    // 检查右边界
    if (_left + _menuWidth > widget.screenSize.width - 10) {
      _left = widget.screenSize.width - _menuWidth - 10;
    }
    
    // 检查左边界
    if (_left < 10) {
      _left = 10;
    }
    
    // 检查下边界
    if (_top + _menuHeight > widget.screenSize.height - 10) {
      // 如果下方空间不足，则尝试显示在点击位置上方
      _top = pointY - _menuHeight - aboveSafeDistance;
      isAboveClickPoint = true;

      // 如果上方空间也不足，则居中显示
      if (_top < widget.statusBarHeight + 10) {
        _top = (widget.screenSize.height - _menuHeight) / 2;
      }
    }

    // 最后的安全检查，确保完全在屏幕内
    _top = _top.clamp(widget.statusBarHeight + 10, widget.screenSize.height - _menuHeight - 10);
  }
  
  @override
  Widget build(BuildContext context) {
    // 根据菜单位置决定动画原点
    bool isBelow = widget.position.dy < _top;
    Alignment animationAlignment = isBelow ? Alignment.topCenter : Alignment.bottomCenter;

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: EdgeInsets.zero,
      alignment: Alignment.topLeft,
      child: Stack(
        children: [
          // 添加一个全屏透明层，用于处理点击外部事件
          if (widget.closeOnTapOutside)
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
          Positioned(
            left: _left,
            top: _top,
            child: ScaleTransition(
              scale: _scaleAnimation,
              alignment: animationAlignment,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  width: _menuWidth,
                  decoration: BoxDecoration(
                    color: widget.backgroundColor ?? const Color(0xFF262626),
                    border: Border.all(
                      color: widget.borderColor ?? Colors.grey.shade900,
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.25),
                        blurRadius: widget.elevation,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: widget.menuItems.map((item) {
                      return _buildMenuItem(item);
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildMenuItem(ContextMenuItem item) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        item.onTap();
      },
      borderRadius: BorderRadius.circular(widget.borderRadius - 1),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 14.0),
        child: Row(
          children: [
            Icon(
              item.icon,
              size: 20.0,
              color: item.iconColor ?? Colors.white,
            ),
            const SizedBox(width: 12.0),
            Text(
              item.text,
              style: TextStyle(
                color: item.textColor ?? Colors.white,
                fontSize: 15.0,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 