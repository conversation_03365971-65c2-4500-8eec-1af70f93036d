enum ReportReason {
  incoherent('Incoherent Response'),
  inappropriate('Sexual or Violent Content'),
  repetitive('Repetitive Responses'),
  misinformation('Misinformation or Misleading'),
  offensive_language('Offensive Language'),
  discriminatory('Discriminatory Content'),
  other('Other Reasons');
  
  const ReportReason(this.description);
  final String description;
  
  static ReportReason fromString(String value) {
    return ReportReason.values.firstWhere(
      (e) => e.toString().split('.').last == value,
      orElse: () => ReportReason.other,
    );
  }
} 