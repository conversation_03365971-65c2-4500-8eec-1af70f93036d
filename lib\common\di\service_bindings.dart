import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/common/services/interface_providers.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/modules/sessions/service/session_service.dart';

/// 服务绑定类
/// 
/// 负责服务的注册、替换和管理，确保依赖注入的安全性和可靠性
class ServiceBindings extends Bindings {
  @override
  void dependencies() {
    // 注册接口的默认实现
    _registerDefaultImplementations();
    
    // 注册具体服务（如果可用）
    _registerConcreteServices();
    
    // 注册服务聚合器
    _registerAggregators();
  }
  
  /// 注册接口的默认实现
  void _registerDefaultImplementations() {
    try {
      LogUtil.info('开始注册接口默认实现...');
      
      // 使用接口提供者绑定来注册默认实现
      if (!Get.isRegistered<InterfaceProvidersBinding>()) {
        final binding = InterfaceProvidersBinding();
        binding.dependencies();
        LogUtil.debug('已注册接口默认实现');
      } else {
        LogUtil.debug('接口默认实现已注册，跳过');
      }
      
      LogUtil.info('接口默认实现注册完成');
    } catch (e) {
      LogUtil.error('注册接口默认实现失败: $e');
      rethrow;
    }
  }
  
  /// 注册具体服务实现
  void _registerConcreteServices() {
    try {
      // 可以在这里添加更多服务的具体实现注册
      
      // 注册角色提供者
      _registerRoleProvider();
      
      // 注册会话服务关系
      _registerSessionProvider();
      
      LogUtil.info('具体服务注册完成');
    } catch (e) {
      LogUtil.error('注册具体服务失败: $e');
      rethrow;
    }
  }
  
  /// 注册角色提供者服务
  void _registerRoleProvider() {
    // 确保有IRoleProvider实现可用
    if (!Get.isRegistered<IRoleProvider>()) {
      LogUtil.error('未找到IRoleProvider实现，无法创建RoleProvider');
      throw Exception('IRoleProvider未注册，无法创建RoleProvider');
    }
    
    // 获取IRoleProvider实现
    final provider = Get.find<IRoleProvider>();
    
    // 如果已存在RoleProvider，则替换它
    if (Get.isRegistered<RoleProvider>()) {
      LogUtil.debug('替换RoleProvider实现');
      Get.delete<RoleProvider>(force: true);
    }
    
    // 注册新的RoleProvider
    Get.put<RoleProvider>(
      RoleProvider(delegate: provider),
      permanent: true
    );
    LogUtil.debug('已注册RoleProvider（委托给${provider.runtimeType}）');
  }
  
  /// 注册会话提供者服务
  void _registerSessionProvider() {
    // 确保有ISessionProvider实现可用
    if (!Get.isRegistered<ISessionProvider>()) {
      LogUtil.warn('未找到ISessionProvider实现，将使用默认实现');
      return;
    }
    
    // 如果SessionService也已经注册，确保它们是同一个实例
    if (Get.isRegistered<SessionService>() && Get.isRegistered<ISessionProvider>()) {
      final sessionService = Get.find<SessionService>();
      final sessionProvider = Get.find<ISessionProvider>();
      
      // 如果不是同一个实例，需要处理
      if (sessionService != sessionProvider && sessionProvider is SessionService) {
        LogUtil.debug('发现ISessionProvider和SessionService不一致，进行同步');
        Get.delete<SessionService>(force: true);
        Get.put<SessionService>(sessionProvider as SessionService, permanent: true);
        LogUtil.debug('已同步SessionService实现');
      }
    }
    
    LogUtil.debug('会话提供者服务关系已验证');
  }
  
  /// 替换服务实现（供其他模块调用）
  /// 
  /// 安全地替换服务实现，确保依赖关系正确更新
  static void replaceService<T>(T implementation) {
    try {
      LogUtil.info('开始替换服务实现: ${T.toString()}...');
      
      // 如果已注册，则安全替换
      if (Get.isRegistered<T>()) {
        LogUtil.debug('替换已存在的${T.toString()}实现');
        Get.delete<T>(force: true);
      }
      
      // 注册新实现
      Get.put<T>(implementation, permanent: true);
      LogUtil.debug('已注册${T.toString()}新实现');
      
      // 处理特殊情况
      _handleSpecialReplacement<T>(implementation);
      
      LogUtil.info('服务替换完成: ${T.toString()}');
    } catch (e) {
      LogUtil.error('替换服务实现失败: $e');
      rethrow;
    }
  }
  
  /// 处理特殊的服务替换情况
  static void _handleSpecialReplacement<T>(dynamic implementation) {
    // 处理IRoleProvider替换时更新RoleProvider
    if (T == IRoleProvider && Get.isRegistered<RoleProvider>()) {
      LogUtil.debug('检测到IRoleProvider更新，同步更新RoleProvider');
      
      // 删除旧的RoleProvider
      Get.delete<RoleProvider>(force: true);
      
      // 创建新的RoleProvider
      Get.put<RoleProvider>(
        RoleProvider(delegate: implementation as IRoleProvider),
        permanent: true
      );
      
      LogUtil.debug('已更新RoleProvider委托实现');
    }
    
    // 如果是RecommendService, 也注册为IRoleProvider
    if (implementation is RecommendService && T != IRoleProvider) {
      LogUtil.debug('检测到RecommendService注册，同时注册为IRoleProvider');
      
      // 替换IRoleProvider
      if (Get.isRegistered<IRoleProvider>()) {
        Get.delete<IRoleProvider>(force: true);
      }
      
      // 注册新的IRoleProvider
      Get.put<IRoleProvider>(implementation, permanent: true);
      
      // 同步更新RoleProvider
      _handleSpecialReplacement<IRoleProvider>(implementation);
    }
    
    // 处理ISessionProvider和SessionService的同步更新
    if (T == ISessionProvider && implementation is SessionService) {
      LogUtil.debug('检测到ISessionProvider更新(SessionService实现)，同步更新SessionService');
      
      // 确保SessionService也被更新
      if (Get.isRegistered<SessionService>() && implementation != Get.find<SessionService>()) {
        Get.delete<SessionService>(force: true);
        Get.put<SessionService>(implementation, permanent: true);
        LogUtil.debug('已同步更新SessionService');
      }
    }
    
    if (T == SessionService && implementation is ISessionProvider) {
      LogUtil.debug('检测到SessionService更新，同步更新ISessionProvider');
      
      // 确保ISessionProvider也被更新
      if (Get.isRegistered<ISessionProvider>() && implementation != Get.find<ISessionProvider>()) {
        Get.delete<ISessionProvider>(force: true);
        Get.put<ISessionProvider>(implementation, permanent: true);
        LogUtil.debug('已同步更新ISessionProvider');
      }
    }
  }
  
  /// 注册服务聚合器
  void _registerAggregators() {
    // 这里可以添加其他服务聚合器的注册
    // 例如：UserServiceAggregator、ChatServiceAggregator等
  }
} 