import 'package:intl/intl.dart';
import 'package:rolio/common/constants/datetime_constants.dart';
import 'package:rolio/common/utils/logger.dart';

/// 日期时间工具类
/// 
/// 提供统一的日期时间处理方法，解决时区问题
class DateTimeUtils {
  /// 获取当前时间
  /// 
  /// 返回当前UTC时间，可选添加偏移
  /// [offset] 可选的时区偏移
  static DateTime getCurrentDateTime({Duration? offset}) {
    final now = DateTime.now().toUtc();
    return offset != null ? now.add(offset) : now;
  }
  
  /// 将UTC时间转换为本地显示时间
  static DateTime utcToLocal(DateTime utcTime) {
    return utcTime.toLocal();
  }
  
  /// 将本地时间转换为UTC(用于发送到服务器)
  static DateTime localToUtc(DateTime localTime) {
    return localTime.toUtc();
  }
  
  /// 将日期时间格式化为ISO8601标准格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式化后的时间字符串，包含时区信息
  static String formatToIso8601(DateTime dateTime) {
    final String formatted = DateTimeConstants.ISO_8601_FORMAT.format(dateTime);
    
    // 获取时区偏移
    final timeZoneOffset = dateTime.timeZoneOffset;
    final sign = timeZoneOffset.isNegative ? "-" : "+";
    final hours = timeZoneOffset.inHours.abs().toString().padLeft(2, '0');
    final minutes = (timeZoneOffset.inMinutes.abs() % 60).toString().padLeft(2, '0');
    
    return "$formatted$sign$hours:$minutes";
  }
  
  /// 解析ISO8601格式的时间字符串
  /// 
  /// [timeString] ISO8601格式的时间字符串
  /// 返回解析后的DateTime对象
  static DateTime parseIso8601(String timeString) {
    try {
      DateTime parsedTime = DateTime.parse(timeString);
      
      // 检查是否为未来时间（超过当前时间1天以上）
      final DateTime now = DateTime.now();
      if (parsedTime.isAfter(now.add(const Duration(days: 1)))) {
        // 如果是未来时间，替换为当前时间
        return now;
      }
      
      // 修复年份问题 - 使用有效年份范围检查
      if (parsedTime.year < DateTimeConstants.MIN_VALID_YEAR || 
          parsedTime.year > DateTimeConstants.MAX_VALID_YEAR) {
        return DateTime(
          now.year,
          parsedTime.month,
          parsedTime.day,
          parsedTime.hour,
          parsedTime.minute,
          parsedTime.second,
          parsedTime.millisecond,
          parsedTime.microsecond,
        );
      }
      
      // 如果没有时区信息，假设是UTC时间，转换为配置的时区
      if (timeString.endsWith('Z') || !timeString.contains('+')) {
        return parsedTime.add(Duration(hours: DateTimeConstants.TIMEZONE_OFFSET));
      }
      
      return parsedTime;
    } catch (e) {
      // 解析失败时返回当前时间
      return getCurrentDateTime();
    }
  }
  
  /// 格式化日期时间为消息显示格式
  static String formatMessageTime(DateTime dateTime) {
    return DateTimeConstants.MESSAGE_TIME_FORMAT.format(dateTime);
  }
  
  /// 格式化日期时间为日期显示格式
  static String formatDate(DateTime dateTime) {
    return DateTimeConstants.DATE_FORMAT.format(dateTime);
  }
  
  /// 格式化日期时间为完整日期时间显示格式
  static String formatDateTime(DateTime dateTime) {
    return DateTimeConstants.DATETIME_FORMAT.format(dateTime);
  }
  
  /// 判断日期是否为今天
  static bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year && 
           dateTime.month == now.month && 
           dateTime.day == now.day;
  }
  
  /// 判断日期是否为昨天
  static bool isYesterday(DateTime dateTime) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    return dateTime.year == yesterday.year && 
           dateTime.month == yesterday.month && 
           dateTime.day == yesterday.day;
  }
  
  /// 判断日期是否为明天
  static bool isTomorrow(DateTime dateTime) {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    return dateTime.year == tomorrow.year && 
           dateTime.month == tomorrow.month && 
           dateTime.day == tomorrow.day;
  }
  
  /// 获取标准化的日期表示，用于消息分组比较
  static String getStandardDateString(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }
  
  /// 获取用户友好的日期显示文本
  /// 
  /// 根据日期与当前日期的关系，返回"today"、"yesterday"、"tomorrow"或具体日期
  static String getFormattedDateString(DateTime dateTime) {
    if (isToday(dateTime)) {
      return 'today';
    } else if (isYesterday(dateTime)) {
      return 'yesterday';
    } else if (isTomorrow(dateTime)) {
      return 'tomorrow';
    } else {
      // 判断是否是当前年份，如果是，则不显示年份
      final now = DateTime.now();
      if (dateTime.year == now.year) {
        return DateFormat('MM月dd日').format(dateTime);
      } else {
        return DateFormat('yyyy年MM月dd日').format(dateTime);
      }
    }
  }
  
  /// 获取消息时间的显示文本，包含日期和时间信息
  /// 
  /// 对于今天的消息，只显示时间
  /// 对于昨天的消息，显示"yesterday 时间"
  /// 对于其他日期的消息，显示完整日期和时间
  static String getMessageTimeDisplay(DateTime dateTime) {
    // 确保使用本地时区时间
    final localTime = dateTime;
    
    // 获取今天的日期
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // 获取消息日期（只保留年月日）
    final messageDate = DateTime(localTime.year, localTime.month, localTime.day);
    
    // 计算日期差异（天数）
    final difference = today.difference(messageDate).inDays;
    
    // 根据日期显示不同格式
    if (difference == 0) {
      // 今天，只显示时间
      return formatMessageTime(localTime);
    } else if (difference == 1) {
      // 昨天，显示"昨天 时间"
      return 'yesterday ${formatMessageTime(localTime)}';
    } else if (difference > 1 && difference < 7) {
      // 一周内，显示"周几 时间"
      final weekdayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      final weekday = weekdayNames[messageDate.weekday % 7]; // 注意：DateTime的weekday是1-7，对应周一到周日
      return '$weekday ${formatMessageTime(localTime)}';
    } else if (localTime.year == now.year) {
      // 今年，显示"月/日 时间"
      return '${DateFormat('MM/dd').format(localTime)} ${formatMessageTime(localTime)}';
    } else {
      // 往年，显示"年/月/日 时间"
      return '${DateFormat('yyyy/MM/dd').format(localTime)} ${formatMessageTime(localTime)}';
    }
  }
  
  /// 将UTC时间转换为客户端本地时区时间
  /// 
  /// [serverTime] 服务器返回的UTC时间
  /// 返回转换后的本地时区时间
  static DateTime convertServerTimeToLocal(DateTime serverTime) {
    // 如果输入的时间已经有时区信息，直接转换为本地时区
    if (serverTime.isUtc) {
      final result = serverTime.toLocal();
      return result;
    }
    
    // 如果输入的时间没有时区信息，假设它是服务器时区时间
    // 首先将其转换为UTC时间
    final utcTime = DateTime.utc(
      serverTime.year, 
      serverTime.month, 
      serverTime.day,
      serverTime.hour,
      serverTime.minute,
      serverTime.second,
      serverTime.millisecond,
      serverTime.microsecond
    ).subtract(Duration(hours: DateTimeConstants.TIMEZONE_OFFSET));
    
    // 然后转换为本地时区
    final result = utcTime.toLocal();
    return result;
  }
  
  /// 将本地时区时间转换为服务器时区时间
  /// 
  /// [localTime] 本地时区时间
  /// 返回转换后的服务器时区时间
  static DateTime convertLocalTimeToServer(DateTime localTime) {
    // 先转换为UTC时间
    final utcTime = localTime.toUtc();
    
    // 根据服务器时区进行调整
    final serverTime = utcTime.add(Duration(hours: DateTimeConstants.TIMEZONE_OFFSET));
    return serverTime;
  }
  
  /// 解析服务器时间并转换为本地时区
  /// 
  /// [timeString] ISO8601格式的服务器时间字符串
  /// 返回转换为本地时区的时间
  static DateTime parseServerTimeToLocal(String timeString) {
    try {
      
      // 先解析为DateTime
      DateTime serverTime;
      
      // 尝试直接解析
      try {
        serverTime = DateTime.parse(timeString);
      } catch (e) {
        // 如果直接解析失败，尝试使用其他格式
        try {
          final dateFormatter = DateFormat("yyyy-MM-dd HH:mm:ss");
          serverTime = dateFormatter.parse(timeString);        
        } catch (e2) {
          // 如果所有解析都失败，返回当前时间
          LogUtil.warn('无法解析时间字符串: $timeString, 错误: $e2');
          return DateTime.now();
        }
      }
      
      // 检查是否为未来时间（超过当前时间1天以上）
      final DateTime now = DateTime.now();
      if (serverTime.isAfter(now.add(const Duration(days: 1)))) {
        LogUtil.warn('检测到未来时间: $serverTime, 使用当前时间');
        serverTime = now;
      }
      
      // 修复年份问题 - 使用有效年份范围检查
      if (serverTime.year < DateTimeConstants.MIN_VALID_YEAR || 
          serverTime.year > DateTimeConstants.MAX_VALID_YEAR) {
        LogUtil.warn('检测到无效年份: ${serverTime.year}, 使用当前年份');
        serverTime = DateTime(
          now.year,
          serverTime.month,
          serverTime.day,
          serverTime.hour,
          serverTime.minute,
          serverTime.second,
          serverTime.millisecond,
          serverTime.microsecond,
        );
      }
      
      // 判断时间字符串是否包含明确的时区信息
      // ISO8601格式的明确时区信息应该是类似"+08:00"或"Z"的后缀
      bool hasExplicitTimezone = (timeString.endsWith('Z') || 
                                 timeString.contains('+') && timeString.lastIndexOf('+') > 10 ||
                                 timeString.contains('-') && timeString.lastIndexOf('-') > 10);
      
      DateTime resultTime;
      // 检查是否为标准ISO8601格式（含T但无明确时区）
      bool isStandardIso8601 = timeString.contains('T') && !hasExplicitTimezone;
      
      if (isStandardIso8601) {
        // 标准ISO8601格式但无明确时区信息，应当视为服务器时区时间
        // 调整为考虑服务器时区偏移的转换
        resultTime = convertServerTimeToLocal(serverTime);
      } else if (!hasExplicitTimezone) {
        // 非标准ISO格式且没有明确时区信息，假设是服务器时区时间
        // 转换为本地时区，考虑服务器时区偏移
        resultTime = convertServerTimeToLocal(serverTime);
      } else if (timeString.endsWith('Z')) {
        // 如果是UTC时间（Z结尾），需要考虑服务器时区
        resultTime = serverTime.toLocal();
      } else {
        // 其他包含明确时区信息的情况
        // 直接转换为本地时区
        resultTime = serverTime.toLocal();
      }
      
      return resultTime;
    } catch (e) {
      // 解析失败时返回当前时间
      LogUtil.error('解析服务器时间并转换为本地时区失败: $e');
      return DateTime.now();
    }
  }
  
  /// 统一处理服务器返回的时间数据
  /// 
  /// 用于处理从服务器返回的各种时间格式，确保统一转换为本地时区
  /// [timeData] 可以是字符串、DateTime对象或null
  /// [defaultTime] 当timeData为null时的默认值，如果不提供则使用当前时间
  /// 返回转换后的本地时区DateTime对象
  static DateTime handleServerTime(dynamic timeData, {DateTime? defaultTime}) {
    
    // 如果为null，返回默认时间或当前时间
    if (timeData == null) {
      final result = defaultTime ?? DateTime.now();
      return result;
    }
    
    try {
      // 如果已经是DateTime对象
      if (timeData is DateTime) {
        // 假设服务器返回的DateTime对象是服务器时区时间
        return convertServerTimeToLocal(timeData);
      }
      
      // 如果是字符串
      if (timeData is String) {
        if (timeData.isEmpty) {
          final result = defaultTime ?? DateTime.now();
          return result;
        }
        
        // 使用现有方法解析字符串
        return parseServerTimeToLocal(timeData);
      }
      
      // 其他类型，返回默认时间
      LogUtil.warn('无法处理的时间数据类型: ${timeData.runtimeType}');
      return defaultTime ?? DateTime.now();
    } catch (e) {
      LogUtil.error('处理服务器时间数据失败: $e');
      return defaultTime ?? DateTime.now();
    }
  }
  
  /// 解析时间字符串为服务器时区的DateTime对象
  /// 
  /// 与parseServerTimeToLocal不同，此方法保留服务器时区，不转换为本地时区
  /// 用于需要在服务器时区进行操作的场景
  /// [timeString] 时间字符串
  /// 返回服务器时区的DateTime对象
  static DateTime parseToServerTime(String timeString) {
    try {
      // 先解析为DateTime
      DateTime dateTime;
      
      try {
        dateTime = DateTime.parse(timeString);
      } catch (e) {
        try {
          final dateFormatter = DateFormat("yyyy-MM-dd HH:mm:ss");
          dateTime = dateFormatter.parse(timeString);
        } catch (e2) {
          // 如果所有解析都失败，返回当前服务器时区时间
          LogUtil.warn('无法解析时间字符串: $timeString, 错误: $e2');
          return getCurrentDateTime(offset: Duration(hours: DateTimeConstants.TIMEZONE_OFFSET));
        }
      }
      
      // 判断时间字符串是否包含明确的时区信息
      bool hasExplicitTimezone = (timeString.endsWith('Z') || 
                               timeString.contains('+') && timeString.lastIndexOf('+') > 10 ||
                               timeString.contains('-') && timeString.lastIndexOf('-') > 10);
      
      // 如果包含明确时区信息，需要转换为服务器时区
      if (hasExplicitTimezone) {
        // 先转换为UTC
        final utcTime = dateTime.toUtc();
        // 再转换为服务器时区
        return utcTime.add(Duration(hours: DateTimeConstants.TIMEZONE_OFFSET));
      } else {
        // 如果不包含明确时区信息，假设已经是服务器时区
        return dateTime;
      }
    } catch (e) {
      LogUtil.error('解析为服务器时区时间失败: $e');
      return getCurrentDateTime(offset: Duration(hours: DateTimeConstants.TIMEZONE_OFFSET));
    }
  }
  
  /// 格式化会话列表项的时间显示
  /// 
  /// 根据时间与当前时间的关系，返回不同的格式：
  /// - 今天：显示时间（HH:mm）
  /// - 昨天：显示"昨天"
  /// - 今年内：显示"MM/dd"
  /// - 往年：显示"yyyy/MM/dd"
  static String formatSessionTime(DateTime time) {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = DateTime(today.year, today.month, today.day - 1);
      final messageDate = DateTime(time.year, time.month, time.day);
      
      // 如果是今天，显示时间
      if (messageDate == today) {
        return DateFormat('HH:mm').format(time);
      } 
      // 如果是昨天，显示"昨天"
      else if (messageDate == yesterday) {
        return 'yesterday';
      } 
      // 如果是今年，显示月日
      else if (time.year == now.year) {
        return DateFormat('MM/dd').format(time);
      } 
      // 如果是往年，显示年月日
      else {
        return DateFormat('yyyy/MM/dd').format(time);
      }
    } catch (e) {
      //LogUtil.error('格式化会话时间失败: $e');
      return '';
    }
  }
  
  /// 智能排序会话列表
  /// 
  /// 按照以下优先级排序：
  /// 1. 优先使用 lastMessageCreatedAt 时间
  /// 2. 如果没有 lastMessageCreatedAt，则使用 updatedAt 时间
  /// 
  /// [sessions] 要排序的会话列表
  /// [comparator] 可选的自定义比较器
  static void sortSessions<T>(List<T> sessions, {
    DateTime? Function(T session)? getLastMessageTime,
    DateTime Function(T session)? getUpdatedAt,
    int Function(T a, T b)? comparator,
  }) {
    // 如果提供了自定义比较器，直接使用
    if (comparator != null) {
      sessions.sort(comparator);
      return;
    }
    
    // 确保必要的访问器函数已提供
    assert(getLastMessageTime != null || getUpdatedAt != null, 
      '必须提供至少一个时间访问器函数');
    
    // 使用默认排序逻辑
    sessions.sort((a, b) {
      // 获取最后消息时间
      final aLastMessageTime = getLastMessageTime?.call(a);
      final bLastMessageTime = getLastMessageTime?.call(b);
      
      // 如果两者都有最后消息时间，按最后消息时间排序
      if (aLastMessageTime != null && bLastMessageTime != null) {
        return bLastMessageTime.compareTo(aLastMessageTime); // 降序
      }
      
      // 如果只有一个有最后消息时间
      if (aLastMessageTime != null) {
        return -1; // a 更新，排在前面
      }
      if (bLastMessageTime != null) {
        return 1;  // b 更新，排在前面
      }
      
      // 如果都没有最后消息时间，使用更新时间
      if (getUpdatedAt != null) {
        final aUpdatedAt = getUpdatedAt(a);
        final bUpdatedAt = getUpdatedAt(b);
        return bUpdatedAt.compareTo(aUpdatedAt); // 降序
      }
      
      return 0; // 无法比较
    });
  }
} 