import 'package:get/get.dart';
import 'package:rolio/common/enums/message_type.dart';
import 'package:rolio/common/utils/datetime_utils.dart';

/// 消息状态枚举
enum MessageStatus {
  /// 发送中
  sending,

  /// 已发送
  sent,

  /// 已送达
  delivered,

  /// 已读
  read,

  /// 发送失败
  failed
}

/// 消息模型
class Message {
  /// 消息ID
  final String id;

  /// 用于兼容ChatMessage的消息ID字段
  String get messageId => id;

  /// 消息内容
  final String lastMessage;

  /// 发送者用户ID
  final String senderUserId;

  /// 接收者用户ID
  final String receiverUserId;

  /// 会话ID
  final int? conversationId;

  /// 会话标识ID，用于区分不同的会话实例
  final String? conversationid;

  /// 消息类型 (使用common/enums/message_type.dart中的枚举)
  final MessageType messageType;

  /// 消息状态
  final MessageStatus status;

  /// 消息时间
  final DateTime time;

  /// 是否已删除
  final bool isDeleted;

  /// 是否已读
  final bool isSeen;

  /// 被回复的消息内容
  final String? repliedMessage;

  /// 被回复的消息ID或发送者
  final String? repliedTo;

  /// 被回复的消息类型
  final MessageType? repliedMessageType;

  /// 构造函数
  Message({
    required this.id,
    required this.lastMessage,
    required this.senderUserId,
    required this.receiverUserId,
    this.conversationId,
    this.conversationid,
    required this.messageType,
    this.status = MessageStatus.sent,
    DateTime? time,
    this.isDeleted = false,
    this.isSeen = false,
    this.repliedMessage,
    this.repliedTo,
    this.repliedMessageType,
  }) : time = time ?? DateTime.now();

  /// 从JSON创建
  factory Message.fromJson(Map<String, dynamic> json) {
    // 解析时间字段 - 优先使用time字段，如果没有则使用created_at字段
    final timeRaw = json['time'] ?? json['created_at'] ?? json['timestamp'];
    final time = _parseDateTime(timeRaw);

    // 处理发送者ID - 支持多种字段名
    String senderUserId = '';
    if (json.containsKey('sender_user_id') && json['sender_user_id'] != null) {
      senderUserId = json['sender_user_id'].toString();
    } else if (json.containsKey('sender') && json['sender'] != null) {
      senderUserId = json['sender'].toString();
    } else if (json.containsKey('from') && json['from'] != null) {
      senderUserId = json['from'].toString();
    }

    // 处理接收者ID - 支持多种字段名
    String receiverUserId = '';
    if (json.containsKey('receiver_user_id') &&
        json['receiver_user_id'] != null) {
      receiverUserId = json['receiver_user_id'].toString();
    } else if (json.containsKey('receiver') && json['receiver'] != null) {
      receiverUserId = json['receiver'].toString();
    } else if (json.containsKey('to') && json['to'] != null) {
      receiverUserId = json['to'].toString();
    }

    // 处理消息内容 - 支持多种字段名
    String messageContent = '';
    if (json.containsKey('content') && json['content'] != null) {
      messageContent = json['content'].toString();
    } else if (json.containsKey('last_message') &&
        json['last_message'] != null) {
      messageContent = json['last_message'].toString();
    } else if (json.containsKey('message') && json['message'] != null) {
      messageContent = json['message'].toString();
    } else if (json.containsKey('text') && json['text'] != null) {
      messageContent = json['text'].toString();
    }

    // 处理会话ID - 支持多种类型
    int? conversationId;
    if (json.containsKey('conversation_id') &&
        json['conversation_id'] != null) {
      final convId = json['conversation_id'];
      if (convId is int) {
        conversationId = convId;
      } else if (convId is String) {
        conversationId = int.tryParse(convId);
      } else {
        conversationId = int.tryParse(convId.toString());
      }
    }

    // 处理消息类型
    String? typeString =
        json['type'] as String? ?? json['message_type'] as String?;

    return Message(
      id: (json['id'] ?? json['message_id'] ?? '').toString(),
      lastMessage: messageContent,
      senderUserId: senderUserId,
      receiverUserId: receiverUserId,
      conversationId: conversationId,
      conversationid: json['conversationid'] as String? ??
          json['conversation_id']?.toString(),
      messageType: parseMessageType(typeString),
      status: _parseMessageStatus(json['status'] as String?),
      time: time,
      isDeleted:
          json['is_deleted'] as bool? ?? json['deleted'] as bool? ?? false,
      isSeen: json['is_seen'] as bool? ??
          json['seen'] as bool? ??
          json['read'] as bool? ??
          false,
      repliedMessage: json['replied_message'] as String? ??
          json['reply_to_message'] as String?,
      repliedTo: json['replied_to'] as String? ?? json['reply_to'] as String?,
      repliedMessageType: json['replied_message_type'] != null
          ? parseMessageType(json['replied_message_type'] as String?)
          : json['reply_message_type'] != null
              ? parseMessageType(json['reply_message_type'] as String?)
              : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final messageTypeValue = messageType.toString().split('.').last;
    final repliedMessageTypeValue =
        repliedMessageType?.toString().split('.').last;

    return {
      'id': id,
      'content': lastMessage,
      'last_message': lastMessage,
      'sender_user_id': senderUserId,
      'receiver_user_id': receiverUserId,
      'conversation_id': conversationId,
      'conversationid': conversationid,
      'type': messageTypeValue,
      'status': _messageStatusToString(status),
      'time': time.toString(), // 使用toString而不是toIso8601String，避免二次转换
      'is_deleted': isDeleted,
      'is_seen': isSeen,
      'replied_message': repliedMessage,
      'replied_to': repliedTo,
      'replied_message_type': repliedMessageTypeValue,
    };
  }

  /// 创建副本
  Message copyWith({
    String? id,
    String? lastMessage,
    String? senderUserId,
    String? receiverUserId,
    int? conversationId,
    String? conversationid,
    MessageType? messageType,
    MessageStatus? status,
    DateTime? time,
    bool? isDeleted,
    bool? isSeen,
    String? repliedMessage,
    String? repliedTo,
    MessageType? repliedMessageType,
  }) {
    return Message(
      id: id ?? this.id,
      lastMessage: lastMessage ?? this.lastMessage,
      senderUserId: senderUserId ?? this.senderUserId,
      receiverUserId: receiverUserId ?? this.receiverUserId,
      conversationId: conversationId ?? this.conversationId,
      conversationid: conversationid ?? this.conversationid,
      messageType: messageType ?? this.messageType,
      status: status ?? this.status,
      time: time ?? this.time,
      isDeleted: isDeleted ?? this.isDeleted,
      isSeen: isSeen ?? this.isSeen,
      repliedMessage: repliedMessage ?? this.repliedMessage,
      repliedTo: repliedTo ?? this.repliedTo,
      repliedMessageType: repliedMessageType ?? this.repliedMessageType,
    );
  }

  /// 解析消息类型（全局静态函数）
  static MessageType parseMessageType(String? typeString) {
    if (typeString == null) return MessageType.text;

    // 使用扩展方法转换字符串到枚举
    return (typeString).toEnum();
  }

  /// 消息状态转字符串
  static String _messageStatusToString(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return 'sending';
      case MessageStatus.delivered:
        return 'delivered';
      case MessageStatus.read:
        return 'read';
      case MessageStatus.failed:
        return 'failed';
      case MessageStatus.sent:
        return 'sent';
    }
  }

  /// 解析消息状态
  static MessageStatus _parseMessageStatus(String? statusString) {
    if (statusString == null) return MessageStatus.sent;

    switch (statusString.toLowerCase()) {
      case 'sending':
        return MessageStatus.sending;
      case 'delivered':
        return MessageStatus.delivered;
      case 'read':
        return MessageStatus.read;
      case 'failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent;
    }
  }

  /// 解析日期时间
  static DateTime _parseDateTime(dynamic dateTime) {
    // 如果已经是 DateTime 对象，说明是本地创建的消息，直接返回
    if (dateTime is DateTime) {
      return dateTime;
    }

    // 使用统一的处理方法处理服务器返回的时间字符串
    return DateTimeUtils.handleServerTime(dateTime);
  }
}

class ReplyMessage {
  final String message;
  final bool isMe;
  final MessageType messageType;
  final bool isSender;

  ReplyMessage({
    required this.message,
    required this.isMe,
    required this.messageType,
    required this.isSender,
  });
}

final replyMessageProvider = Rx<ReplyMessage?>(null);
