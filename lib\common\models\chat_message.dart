import 'package:rolio/common/enums/message_type.dart';

/// 通用聊天消息模型
/// 
/// 提供了独立于具体模块的聊天消息数据结构
class ChatMessage {
  ChatMessage({
    required this.senderUserId,
    required this.receiverUserId,
    required this.messageId,
    required this.isSeen,
    required this.content,
    required this.messageType,
    required this.time,
    this.repliedMessage = '',
    this.repliedTo = '',
    this.repliedMessageType = MessageType.text,
  });

  final String senderUserId;
  final String receiverUserId;
  final String messageId;
  final bool isSeen;
  final String content; // 使用更清晰的名称替代lastMessage
  final MessageType messageType;
  final DateTime time;
  final String repliedMessage;
  final String repliedTo;
  final MessageType repliedMessageType;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'senderUserId': senderUserId,
      'receiverUserId': receiverUserId,
      'messageId': messageId,
      'isSeen': isSeen,
      'content': content,
      'messageType': messageType.type,
      'time': time.millisecondsSinceEpoch,
      'repliedMessage': repliedMessage,
      'repliedTo': repliedTo,
      'repliedMessageType': repliedMessageType.type,
    };
  }

  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    return ChatMessage(
      senderUserId: map['senderUserId'] as String,
      receiverUserId: map['receiverUserId'] as String,
      messageId: map['messageId'] as String,
      isSeen: map['isSeen'] as bool,
      content: map['content'] ?? map['lastMessage'] as String,
      messageType: (map['messageType'] as String).toEnum(),
      time: DateTime.fromMillisecondsSinceEpoch(map['time'] as int),
      repliedMessage: map['repliedMessage'] as String? ?? '',
      repliedTo: map['repliedTo'] as String? ?? '',
      repliedMessageType: ((map['repliedMessageType'] as String?) ?? 'text').toEnum(),
    );
  }
} 