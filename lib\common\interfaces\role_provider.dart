import 'package:rolio/common/models/ai_role.dart';

/// 角色提供者接口
/// 
/// 提供获取AI角色信息的方法，用于解耦不同模块间的依赖
abstract class IRoleProvider {
  /// 根据角色ID获取角色信息
  Future<AiRole?> getRoleById(int roleId);
  
  /// 获取所有角色信息的流
  Stream<List<AiRole>> getRoles();
  
  /// 获取角色头像URL
  Future<String?> getAvatarUrlById(int roleId);
  
  /// 获取角色封面URL
  Future<String?> getCoverUrlById(int roleId);
  
  /// 获取角色名称
  Future<String?> getRoleNameById(int roleId);
  
  /// 获取下一个推荐角色
  Future<AiRole?> getNextRecommendRole(int currentRoleId);
  
  /// 获取上一个推荐角色
  Future<AiRole?> getPreviousRecommendRole(int currentRoleId);
  
  /// 获取下一个会话角色
  /// 
  /// [currentRoleId] 当前角色ID
  /// 返回下一个角色，如果没有则返回null
  Future<AiRole?> getNextSessionRole(int currentRoleId);
  
  /// 获取上一个会话角色
  /// 
  /// [currentRoleId] 当前角色ID
  /// 返回上一个角色，如果没有则返回null
  Future<AiRole?> getPreviousSessionRole(int currentRoleId);
} 