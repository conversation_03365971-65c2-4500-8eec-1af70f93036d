import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// 主题颜色常量
class ThemeColors {
  // 主色
  static const Color primaryColor = Colors.white;
  
  // 背景颜色
  static const Color primaryBackgroundColor = Colors.black;
  static const Color secondaryBackgroundColor = Color(0xFF121212);
  static const Color cardBackgroundColor = Color(0xFF1E1E1E);
  
  // 文本颜色
  static const Color primaryTextColor = Colors.white;
  static const Color secondaryTextColor = Color(0xFFB3B3B3);
  static const Color disabledTextColor = Color(0xFF666666);
  
  // 边框颜色
  static const Color borderColor = Color(0xFF333333);
  
  // 错误颜色
  static const Color errorColor = Colors.red;
  
  // 成功颜色
  static const Color successColor = Color(0xFF4CAF50);
}

final ThemeData appTheme = ThemeData(
  brightness: Brightness.dark,
  primaryColor: ThemeColors.primaryColor,
  scaffoldBackgroundColor: ThemeColors.primaryBackgroundColor,
  appBarTheme: _getAppBarTheme(),
  colorScheme: _getCustomColorScheme(),
  textTheme: GoogleFonts.poppinsTextTheme(
    _getTextTheme(),
  ),
  bottomNavigationBarTheme: const BottomNavigationBarThemeData(
    backgroundColor: ThemeColors.primaryBackgroundColor,
    selectedItemColor: ThemeColors.primaryColor,
    unselectedItemColor: ThemeColors.secondaryTextColor,
  ),
  progressIndicatorTheme: const ProgressIndicatorThemeData(
    color: ThemeColors.primaryColor,
  ),
);

AppBarTheme _getAppBarTheme() {
  return AppBarTheme(
    backgroundColor: ThemeColors.primaryBackgroundColor,
    elevation: 0,
    titleTextStyle: GoogleFonts.poppins(
      color: ThemeColors.primaryColor,
      fontSize: 22.0,
      fontWeight: FontWeight.bold,
    ),
    iconTheme: const IconThemeData(color: ThemeColors.primaryColor),
    actionsIconTheme: const IconThemeData(color: ThemeColors.primaryColor),
  );
}

TextTheme _getTextTheme() {
  return TextTheme(
    headlineSmall: GoogleFonts.poppins(
      color: ThemeColors.primaryColor,
      fontSize: 16.0,
      fontWeight: FontWeight.w600,
    ),
    headlineLarge: GoogleFonts.poppins(
      color: ThemeColors.primaryColor,
      fontSize: 22.0,
      fontWeight: FontWeight.w600,
    ),
    bodyLarge: GoogleFonts.poppins(
      color: ThemeColors.primaryColor,
      fontSize: 14.0,
      fontWeight: FontWeight.w500,
    ),
    bodyMedium: GoogleFonts.poppins(
      color: ThemeColors.secondaryTextColor,
      fontSize: 14.0,
      fontWeight: FontWeight.normal,
    ),
    bodySmall: GoogleFonts.poppins(
      color: ThemeColors.secondaryTextColor,
      fontSize: 10.0,
      fontWeight: FontWeight.normal,
    ),
    labelMedium: GoogleFonts.poppins(
      color: ThemeColors.primaryColor,
      fontSize: 14.0,
      fontWeight: FontWeight.w400,
    ),
    labelSmall: GoogleFonts.poppins(
      color: ThemeColors.secondaryTextColor,
      fontSize: 12.0,
      fontWeight: FontWeight.w400,
    ),
    displaySmall: GoogleFonts.poppins(
      color: ThemeColors.primaryColor,
      fontSize: 12.0,
      fontWeight: FontWeight.w500,
    ),
  );
}

ColorScheme _getCustomColorScheme() {
  return const ColorScheme.dark(
    primary: ThemeColors.primaryColor,
    onPrimary: ThemeColors.primaryColor,
    onError: ThemeColors.errorColor,
    secondary: ThemeColors.primaryColor,
    onSecondary: ThemeColors.primaryColor,
    surface: ThemeColors.primaryBackgroundColor,
    onSurface: ThemeColors.primaryColor,
  );
}
