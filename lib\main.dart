import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/constants/theme_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/config_manager.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/routes/routes.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/firebase_options.dart';
import 'package:rolio/common/di/app_binding.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/manager/app_initializer.dart'; // 添加AppInitializer导入
import 'package:rolio/widgets/init_error_view.dart'; // 添加InitErrorView导入
import 'package:rolio/widgets/loader.dart'; // 添加AuthLoadingOverlay导入
import 'package:rolio/common/cache/memory_monitor.dart'; // 添加内存监控器导入
import 'package:rolio/common/constants/cache_constants.dart'; // 添加缓存常量导入
import 'routes/router_manager.dart';

void main(List<String> args) async {
  final startTime = DateTime.now();
  WidgetsFlutterBinding.ensureInitialized();

  LogUtil.init(
      debugEnabled: true, verboseEnabled: true, timeStampEnabled: true);
  LogUtil.info('应用启动...');

  try {
    // 记录图片缓存配置开始时间
    final step1 = DateTime.now();
    _configureImageCache();
    LogUtil.info('图片缓存配置耗时: ${DateTime.now().difference(step1).inMilliseconds}ms');
    
    // 记录基础服务初始化开始时间
    final step2 = DateTime.now();
    await _initializeBasicServices();
    LogUtil.info('基础服务初始化耗时: ${DateTime.now().difference(step2).inMilliseconds}ms');
    
    // 自定义报错页面
    ErrorWidget.builder = (FlutterErrorDetails flutterErrorDetails) {
      LogUtil.error(flutterErrorDetails.toString());
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Center(
            child: Text(
              'Application error, please contact the developer for support.',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ),
      );
    };

    final totalTime = DateTime.now().difference(startTime).inMilliseconds;
    LogUtil.info('核心初始化总时间: ${totalTime}ms');

    // 先启动应用显示Splash页面
    runApp(MyApp());
    
    // 在UI显示Splash后，继续初始化非关键服务，后续由Splash页面控制导航
    _initializeNonCriticalServices();
  } catch (e) {
    LogUtil.error('应用初始化失败: $e');
    runApp(const ErrorApp());
  }
}

/// 基础服务初始化（并行优化）
Future<void> _initializeBasicServices() async {
  // 并行执行不相互依赖的初始化
  await Future.wait([
    ConfigManager.instance.init(),
    CacheManager.init(),
    _initializeFirebase(),
  ]);
  
  // Firebase初始化完成后再初始化AppBinding
  await AppBinding.init();
}

/// Firebase初始化（单独提取为方法以便并行执行）
Future<void> _initializeFirebase() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    LogUtil.info('Firebase初始化成功');
  } catch (e) {
    LogUtil.error('Firebase初始化失败: $e');
    throw e;
  }
}

/// 非关键服务初始化（在UI显示后执行）
Future<void> _initializeNonCriticalServices() async {
  try {
    final step3 = DateTime.now();
    final appInitializer = AppInitializer();
    
    // 初始化内存监控
    MemoryMonitor.init();
    
    // 立即触发初始化，但不等待完成
    appInitializer.initialize().then((_) {
      LogUtil.info('应用服务初始化耗时: ${DateTime.now().difference(step3).inMilliseconds}ms');
      // 初始化完成后，更新GlobalState状态，Splash页面会监听这个状态
      final globalState = Get.find<GlobalState>();
      if (globalState.initState.value != InitState.error) {
        globalState.setInitState(InitState.completed);
      }
    }).catchError((e) {
      LogUtil.error('应用服务后台初始化失败: $e');
      // 如果初始化失败，也需要更新状态
      final globalState = Get.find<GlobalState>();
      globalState.setInitState(InitState.error);
    });
    
    LogUtil.info('非关键服务初始化已在后台启动');
  } catch (e) {
    LogUtil.error('启动非关键服务初始化失败: $e');
    // 不抛出异常，允许应用继续运行
  }
}

/// 配置图片缓存
void _configureImageCache() {
  // 根据设备内存动态调整缓存大小
  final cacheSizeBytes = CacheConstants.getRecommendedImageCacheSize();
  
  PaintingBinding.instance.imageCache.maximumSizeBytes = cacheSizeBytes;
  LogUtil.debug('图片缓存大小设置为: ${cacheSizeBytes ~/ (1024 * 1024)}MB');

  // 配置CachedNetworkImage缓存
  CachedNetworkImage.logLevel = CacheManagerLogLevel.none; // 关闭日志

  // 配置图片缓存参数
  Get.put(CacheManager.getInstance()
    ..setDefaultExpiry(CacheConstants.imageCacheDuration.inMilliseconds)); // 使用常量定义缓存时间

  LogUtil.debug('图片缓存配置完成');
}

/// 错误启动应用
class ErrorApp extends StatelessWidget {
  const ErrorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: StringsConsts.appName,
      theme: appTheme,
      home: const InitErrorScreen(),
    );
  }
}

/// 主应用
class MyApp extends StatelessWidget {
  MyApp({Key? key}) : super(key: key);

  final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: StringsConsts.appName,
      theme: appTheme,
      initialRoute: RouterManager.initialRoute,
      getPages: RouterManager.pages,
      defaultTransition: Transition.fadeIn,
      builder: (context, child) {
        return AuthLoadingOverlay(child: child!);
      },
      navigatorObservers: [
        FirebaseAnalyticsObserver(analytics: analytics),
      ],
    );
  }
}

/// 初始化状态检查中间件
class InitStateMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final globalState = Get.find<GlobalState>();
    final initState = globalState.initState.value;

    if (initState == InitState.initializing) {
      return null;
    } else if (initState == InitState.completed) {
      return const RouteSettings(name: Routes.homeScreen);
    } else if (initState == InitState.error) {
      return null;
    }

    return null;
  }
}
