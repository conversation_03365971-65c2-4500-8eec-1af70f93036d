import 'package:flutter/foundation.dart';

/// 日志工具类（支持超长文本输出）
class LogUtil {
  /// 单条日志最大长度（可根据平台调整，建议1000-2000字符）
  static const int _maxLogLength = 1000;

  /// 是否启用调试日志
  static bool _debugEnabled = true;

  /// 是否启用详细日志
  static bool _verboseEnabled = false;

  /// 是否启用日志时间戳
  static bool _timeStampEnabled = false;

  /// 初始化日志工具
  static void init({
    bool debugEnabled = true,
    bool verboseEnabled = false,
    bool timeStampEnabled = true,
  }) {
    _debugEnabled = debugEnabled;
    _verboseEnabled = verboseEnabled;
    _timeStampEnabled = timeStampEnabled;

    debug('LogUtil初始化完成: debugEnabled=$_debugEnabled, verboseEnabled=$_verboseEnabled');
  }

  /// 输出调试日志
  static void debug(String message) {
    if (_debugEnabled) {
      _printLog('DEBUG', message);
    }
  }

  /// 输出详细日志
  static void verbose(String message) {
    if (_verboseEnabled) {
      _printLog('VERBOSE', message);
    }
  }

  /// 输出信息日志
  static void info(String message) {
    _printLog('INFO', message);
  }

  /// 输出警告日志
  static void warn(String message) {
    _printLog('WARN', message);
  }

  /// 输出错误日志
  static void error(String message) {
    _printLog('ERROR', message);
    // 错误日志额外通过FlutterError上报（仍可能截断，仅作补充）
    FlutterError.reportError(FlutterErrorDetails(
      exception: message,
      library: 'LogUtil',
      context: ErrorDescription('错误日志'),
    ));
  }

  /// 打印日志（核心：超长文本自动分段）
  static void _printLog(String level, String message) {
    // 处理时间戳
    String timeStamp = _timeStampEnabled ? '[${DateTime.now().toString()}] ' : '';
    String prefix = '$timeStamp$level: ';

    // 如果消息为空，直接输出前缀
    if (message.isEmpty) {
      debugPrint(prefix);
      return;
    }

    // 计算总长度：前缀长度 + 消息长度
    int totalLength = prefix.length + message.length;

    // 如果未超过最大长度，直接输出
    if (totalLength <= _maxLogLength) {
      debugPrint(prefix + message);
      return;
    }

    // 超长文本：拆分消息为多个片段，逐个输出
    int messageStart = 0;
    // 每次输出的消息部分长度 = 最大长度 - 前缀长度（预留前缀空间）
    int segmentLength = _maxLogLength - prefix.length;

    while (messageStart < message.length) {
      int messageEnd = messageStart + segmentLength;
      // 处理最后一段（避免索引越界）
      if (messageEnd > message.length) {
        messageEnd = message.length;
      }
      // 截取当前片段
      String segment = message.substring(messageStart, messageEnd);
      // 输出当前片段（带前缀）
      debugPrint(prefix + segment);
      // 移动到下一段
      messageStart = messageEnd;
    }
  }
}