import "dart:convert";

import "package:rolio/generated/json/account_server_bind_email_req_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

export "package:rolio/generated/json/account_server_bind_email_req_entity.g.dart";

@JsonSerializable()
class AccountServerBindEmailReqEntity {
  late int userId;
  late String email;
  late String code;

  AccountServerBindEmailReqEntity();

  factory AccountServerBindEmailReqEntity.fromJson(Map<String, dynamic> json) =>
      $AccountServerBindEmailReqEntityFromJson(json);

  Map<String, dynamic> toJson() => $AccountServerBindEmailReqEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
