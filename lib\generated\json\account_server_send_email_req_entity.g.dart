import 'package:rolio/generated/json/base/json_convert_content.dart';
import "package:rolio/modules/login/model/account_server_send_email_req_entity.dart";

AccountServerSendEmailReqEntity $AccountServerSendEmailReqEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerSendEmailReqEntity accountServerSendEmailReqEntity =
      AccountServerSendEmailReqEntity();
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    accountServerSendEmailReqEntity.email = email;
  }
  return accountServerSendEmailReqEntity;
}

Map<String, dynamic> $AccountServerSendEmailReqEntityToJson(
    AccountServerSendEmailReqEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['email'] = entity.email;
  return data;
}

extension AccountServerSendEmailReqEntityExtension
    on AccountServerSendEmailReqEntity {
  AccountServerSendEmailReqEntity copyWith({
    String? email,
  }) {
    return AccountServerSendEmailReqEntity()..email = email ?? this.email;
  }
}
