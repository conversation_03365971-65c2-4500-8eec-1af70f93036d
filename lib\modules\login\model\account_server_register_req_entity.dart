import "dart:convert";

import "package:rolio/generated/json/account_server_register_req_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

export 'package:rolio/generated/json/account_server_register_req_entity.g.dart';

@JsonSerializable()
class AccountServerRegisterReqEntity {
  late bool isBoot;

  AccountServerRegisterReqEntity();

  factory AccountServerRegisterReqEntity.fromJson(Map<String, dynamic> json) =>
      $AccountServerRegisterReqEntityFromJson(json);

  Map<String, dynamic> toJson() => $AccountServerRegisterReqEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
