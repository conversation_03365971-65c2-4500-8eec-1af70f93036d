import 'package:flutter/material.dart';
import 'package:rolio/common/constants/theme_constants.dart';

/// 应用通用加载指示器
class RoLioLoadingIndicator extends StatelessWidget {
  /// 指示器大小
  final double size;
  
  /// 指示器粗细
  final double strokeWidth;
  
  /// 指示器颜色
  final Color? color;

  /// 构造函数
  const RoLioLoadingIndicator({
    Key? key,
    this.size = 30.0,
    this.strokeWidth = 3.0,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? ThemeColors.primaryColor,
        ),
      ),
    );
  }
} 