import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/utils/datetime_utils.dart';
import 'chat_widgets.dart';
import '../../../modules/home/<USER>/home_controller.dart';

class ChatsList extends GetView<HomeController> {
  const ChatsList({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final messages = controller.messages;

      if (messages.isEmpty) {
        return const NoChat();
      }

      return ListView.builder(
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final message = messages[index];
          final currentUserId = Get.find<GlobalState>().currentUser.value?.uid ?? '';
          final bool isFromMe = message.senderUserId == currentUserId;

          return _buildMessageItem(
              context, message.lastMessage, isFromMe, message.time);
        },
      );
    });
  }

  Widget _buildMessageItem(
      BuildContext context, String message, bool isFromMe, DateTime time) {
    Size size = MediaQuery.of(context).size;

    return Align(
      alignment: isFromMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(
          left: isFromMe ? size.width * 0.2 : 10,
          right: isFromMe ? 10 : size.width * 0.2,
          top: 5,
          bottom: 5,
        ),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isFromMe ? Colors.blue.shade100 : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              message,
              style: TextStyle(
                fontSize: size.width * 0.04,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateTimeUtils.formatMessageTime(time),
              style: TextStyle(
                fontSize: size.width * 0.025,
                color: Colors.black54,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
