import 'package:rolio/generated/json/base/json_convert_content.dart';
import "package:rolio/modules/login/model/account_server_register_req_entity.dart";

AccountServerRegisterReqEntity $AccountServerRegisterReqEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerRegisterReqEntity accountServerRegisterReqEntity =
      AccountServerRegisterReqEntity();
  final bool? isBoot = jsonConvert.convert<bool>(json['isBoot']);
  if (isBoot != null) {
    accountServerRegisterReqEntity.isBoot = isBoot;
  }
  return accountServerRegisterReqEntity;
}

Map<String, dynamic> $AccountServerRegisterReqEntityToJson(
    AccountServerRegisterReqEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['isBoot'] = entity.isBoot;
  return data;
}

extension AccountServerRegisterReqEntityExtension
    on AccountServerRegisterReqEntity {
  AccountServerRegisterReqEntity copyWith({
    bool? isBoot,
  }) {
    return AccountServerRegisterReqEntity()..isBoot = isBoot ?? this.isBoot;
  }
}
