/// WebSocket常量
class WsConstants {
  // WebSocket基础URL
  static const String wsBaseUrl = 'wss://api.rolio.com';
  
  // 默认心跳间隔（毫秒）
  static const int defaultHeartbeatIntervalMs = 20000;
  
  // 默认心跳超时（毫秒）- 增加超时时间以更好地容忍网络延迟
  static const int defaultHeartbeatTimeoutMs = 20000;
  
  // 默认重连基础延迟（毫秒）
  static const int defaultReconnectBaseDelayMs = 400;
}

/// WebSocket连接状态
enum WsConnectionState {
  /// 未连接
  disconnected,
  
  /// 连接中
  connecting,
  
  /// 已连接
  connected,
  
  /// 重新连接中
  reconnecting,
  
  /// 等待应用恢复
  waitingForResume,
  
  /// 等待网络恢复
  waitingForNetwork,
  
  /// 连接失败
  failed
}

/// WebSocket事件类型
enum WsEvent {
  /// 未知事件
  unknown,
  
  /// 心跳ping
  ping,
  
  /// 心跳pong
  pong,
  
  /// 聊天消息
  chat_message,
  
  /// AI回复
  ai_reply,
  
  /// 系统通知
  system_notification,
  
  /// 会话更新
  session_update,
  
  /// 角色更新
  role_update,
  
  /// 错误
  error,
  
  /// 状态同步
  sync_state,

  ///用户问题
  force_disconnect,
  
  /// 输入状态
  typing
}

/// WebSocket连接配置
class WsConnectionConfig {
  /// 心跳间隔（毫秒）
  final int heartbeatIntervalMs;
  
  /// 心跳超时（毫秒）
  final int heartbeatTimeoutMs;
  
  /// 重连基础延迟（毫秒）
  final int reconnectBaseDelayMs;
  
  /// 是否启用自动重连
  final bool autoReconnect;
  
  /// 构造函数
  const WsConnectionConfig({
    required this.heartbeatIntervalMs,
    required this.heartbeatTimeoutMs,
    required this.reconnectBaseDelayMs,
    this.autoReconnect = true,
  });
  
  /// 默认配置
  static const WsConnectionConfig defaultConfig = WsConnectionConfig(
    heartbeatIntervalMs: WsConstants.defaultHeartbeatIntervalMs,
    heartbeatTimeoutMs: WsConstants.defaultHeartbeatTimeoutMs,
    reconnectBaseDelayMs: WsConstants.defaultReconnectBaseDelayMs,
    autoReconnect: true,
  );
  
  @override
  String toString() {
    return 'WsConnectionConfig{heartbeatIntervalMs: $heartbeatIntervalMs, heartbeatTimeoutMs: $heartbeatTimeoutMs, reconnectBaseDelayMs: $reconnectBaseDelayMs, autoReconnect: $autoReconnect}';
  }
}

/// 重连状态
class ReconnectStatus {
  /// 是否正在重连
  final bool isReconnecting;
  
  /// 重连尝试次数
  final int attempt;
  
  /// 重连延迟（毫秒）
  final int delayMs;
  
  /// 重连是否成功
  final bool? success;
  
  /// 最大重连尝试次数
  final int maxAttempts;
  
  /// 网络是否可用
  final bool? networkAvailable;
  
  /// 是否达到最大重连次数
  bool get isMaxAttempts => attempt >= maxAttempts;
  
  /// 构造函数
  const ReconnectStatus({
    required this.isReconnecting,
    required this.attempt,
    required this.delayMs,
    this.success,
    this.maxAttempts = 10,
    this.networkAvailable,
  });
}