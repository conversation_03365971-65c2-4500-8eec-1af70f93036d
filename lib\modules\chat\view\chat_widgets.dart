import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/common/constants/assets_constants.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/widgets/helper_widgets.dart';
import 'display_message.dart';

/// 通用的Material图标按钮
Widget buildMaterialIconButton({
  required IconData icon,
  required VoidCallback onTap,
}) {
  return Material(
    clipBehavior: Clip.antiAlias,
    color: Colors.transparent,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(100.0),
    ),
    child: IconButton(
      onPressed: onTap,
      splashColor: AppColors.grey,
      icon: Icon(
        icon,
        color: AppColors.chatScreenGrey,
      ),
    ),
  );
}

/// 构建弹出菜单项
PopupMenuItem buildPopUpMenuItem(
  IconData icon,
  String text,
  VoidCallback onPressed,
) {
  return PopupMenuItem(
    child: ListTile(
      leading: Icon(icon),
      title: Text(text),
      onTap: onPressed,
    ),
  );
}

/// 无聊天展示组件
class NoChat extends StatelessWidget {
  const NoChat({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.width * 0.9,
        child: Opacity(
          opacity: 0.8,
          child: Image.asset(
            ImagesConsts.icNoChat,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}

/// 回复消息预览组件
class ReplyMessagePreview extends StatelessWidget {
  const ReplyMessagePreview({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 使用 GetX 的 Obx 监听 replyMessageProvider
    return Obx(() {
      final replyMessage = replyMessageProvider.value;
      if (replyMessage == null) return const SizedBox.shrink();

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: replyMessage.isMe ? AppColors.primary : AppColors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(8.0),
            topRight: Radius.circular(8.0),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    replyMessage.isMe ? 'Me' : 'Opposite',
                    style: replyMessage.isMe
                        ? Theme.of(context).textTheme.headlineSmall
                        : Theme.of(context)
                            .textTheme
                            .headlineSmall!
                            .copyWith(color: AppColors.black),
                  ),
                ),
                IconButton(
                  onPressed: () => _cancelReply(),
                  icon: Icon(
                    Icons.close,
                    color:
                        replyMessage.isMe ? AppColors.white : AppColors.black,
                  ),
                )
              ],
            ),
            addVerticalSpace(8.0),
            DisplayMessage(
              message: replyMessage.message,
              messageType: replyMessage.messageType,
              isSender: replyMessage.isMe,
            ),
          ],
        ),
      );
    });
  }

  void _cancelReply() {
    // 使用 GetX 的方式更新 replyMessageProvider
    replyMessageProvider.value = null;
  }
} 