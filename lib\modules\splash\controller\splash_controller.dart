
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';
import 'package:rolio/manager/global_state.dart';

/// Splash页面控制器
/// 
/// 负责处理应用启动过程中的加载逻辑
class SplashController extends GetxController {
  // 加载状态文本
  final loadingStatus = 'Loading...'.obs;
  
  // 加载进度指示器
  final loadingProgress = 0.0.obs;
  
  // 加载阶段，用于确保进度只增不减
  int _loadingPhase = 0;
  
  // 登录服务
  final LoginService _loginService = Get.find<LoginService>();
  
  // 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  // 超时状态 - 使用GetX响应式变量
  final RxBool _isTimeoutActive = false.obs;
  
  // 最大加载时间（毫秒）
  static const int MAX_LOADING_TIME = 5000; // 5秒
  
  @override
  void onInit() {
    super.onInit();
    
    // 启动超时计时器
    _startTimeoutTimer();
    
    // 开始加载流程
    _startInitializationProcess();
  }
  
  @override
  void onClose() {
    // 取消超时状态
    _isTimeoutActive.value = false;
    super.onClose();
  }
  
  /// 启动超时计时器 - 使用Future.delayed替代Timer
  void _startTimeoutTimer() {
    _isTimeoutActive.value = true;
    Future.delayed(Duration(milliseconds: MAX_LOADING_TIME), () {
      if (_isTimeoutActive.value) {
        LogUtil.warn('启动加载超时，强制跳转到主页');
        _navigateToMainScreen();
      }
    });
  }
  
  /// 更新加载状态，确保进度只增不减
  void _updateLoadingStatus(String message, double progress) {
    // 确保进度值在0.0到1.0之间
    progress = progress.clamp(0.0, 1.0);
    
    // 只有当新的进度大于当前进度时才更新
    if (progress >= loadingProgress.value) {
      loadingProgress.value = progress;
      loadingStatus.value = message;
      _loadingPhase++;
    }
  }
  
  /// 开始初始化流程
  void _startInitializationProcess() async {
    try {
      // 1. 检查初始化状态
      if (_globalState.initState.value != InitState.completed) {
        _updateLoadingStatus('Getting things ready...', 0.2);
        // 等待初始化完成
        await _waitForInitializationComplete();
      }
      
      // 2. 确保用户已登录
      _updateLoadingStatus('Preparing your experience...', 0.4);
      final user = await _loginService.getCurrentUser();
      if (user == null) {
        LogUtil.info('用户未登录，执行匿名登录');
        _updateLoadingStatus('Setting up your profile...', 0.6);
        await _loginService.ensureAnonymousLogin();
      }
      
      // 3. 确保有有效的token - 不向用户显示认证细节
      _updateLoadingStatus('Almost there...', 0.8);
      final token = await _loginService.getIdToken(forceRefresh: true);
      if (token == null) {
        LogUtil.error('无法获取有效token');
        // 不向用户显示认证失败，使用通用提示
        _updateLoadingStatus('Connection issue, retrying...', 0.7);
        await Future.delayed(const Duration(seconds: 1));
        // 再次尝试获取token
        final retryToken = await _loginService.getIdToken(forceRefresh: true);
        if (retryToken == null) {
          LogUtil.error('重试获取token失败');
        }
      } else {
        LogUtil.info('已获取有效token');
      }
      
      // 4. 完成加载，跳转到主页
      _updateLoadingStatus('Ready!', 1.0);
      
      // 短暂停顿后再跳转，避免闪烁
      await Future.delayed(const Duration(milliseconds: 300));
      _navigateToMainScreen();
      
    } catch (e) {
      LogUtil.error('初始化流程失败: $e');
      _updateLoadingStatus('Just a moment...', 0.9);
      
      // 延迟1秒后仍然尝试跳转到主页
      await Future.delayed(const Duration(seconds: 1));
      _navigateToMainScreen();
    }
  }
  
  /// 等待应用初始化完成 - 使用GetX响应式变量替代Completer
  Future<void> _waitForInitializationComplete() async {
    // 检查当前状态
    if (_globalState.initState.value == InitState.completed) {
      return;
    }

    // 使用GetX响应式变量管理等待状态
    final RxBool isWaitingComplete = false.obs;
    final RxBool hasError = false.obs;
    final RxBool isTimeout = false.obs;

    // 设置监听器
    final subscription = _globalState.initState.listen((state) {
      if (state == InitState.completed) {
        isWaitingComplete.value = true;
      } else if (state == InitState.error) {
        hasError.value = true;
      }
    });

    // 设置超时 - 使用Future.delayed替代Timer
    Future.delayed(const Duration(seconds: 3), () {
      if (!isWaitingComplete.value && !hasError.value) {
        LogUtil.warn('等待初始化完成超时');
        isTimeout.value = true;
      }
    });

    // 等待完成、错误或超时
    while (!isWaitingComplete.value && !hasError.value && !isTimeout.value) {
      await Future.delayed(const Duration(milliseconds: 50));
    }

    // 清理监听器
    subscription.cancel();

    // 如果是错误状态，抛出异常
    if (hasError.value) {
      throw Exception('Initialization failed');
    }
  }
  
  /// 导航到主页面
  void _navigateToMainScreen() {
    // 取消超时状态
    _isTimeoutActive.value = false;

    // 清除所有路由并导航到主页
    RouterManager.clearAndNavigateTo(Routes.homeScreen);
  }
} 