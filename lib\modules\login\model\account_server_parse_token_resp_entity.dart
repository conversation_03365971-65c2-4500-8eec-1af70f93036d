import "dart:convert";

import "package:rolio/generated/json/account_server_parse_token_resp_entity.g.dart";
import "package:rolio/generated/json/base/json_field.dart";

export 'package:rolio/generated/json/account_server_parse_token_resp_entity.g.dart';

@JsonSerializable()
class AccountServerParseTokenRespEntity {
  late int userId;
  late String email;

  AccountServerParseTokenRespEntity();

  factory AccountServerParseTokenRespEntity.fromJson(
          Map<String, dynamic> json) =>
      $AccountServerParseTokenRespEntityFromJson(json);

  Map<String, dynamic> toJson() =>
      $AccountServerParseTokenRespEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
