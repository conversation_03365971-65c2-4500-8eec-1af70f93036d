import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/common/models/page_request.dart';

/// 统一分页错误处理混入类
/// 
/// 提供统一的分页状态管理、错误处理和重试机制
mixin PaginationMixin on GetxController {
  // 分页状态
  final RxBool isLoadingMore = false.obs;
  final RxBool hasMoreData = true.obs;
  final RxString paginationError = ''.obs;
  
  // 重试相关
  int _retryCount = 0;
  static const int maxRetryCount = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // 分页请求对象
  PageRequest? _currentPageRequest;
  
  /// 初始化分页状态
  void initializePagination({
    required PageRequest initialRequest,
    bool hasMore = true,
  }) {
    _currentPageRequest = initialRequest;
    hasMoreData.value = hasMore;
    isLoadingMore.value = false;
    paginationError.value = '';
    _retryCount = 0;
    LogUtil.debug('分页状态初始化完成');
  }
  
  /// 获取当前分页请求
  PageRequest get currentPageRequest => _currentPageRequest ?? PageRequest(page: 1, size: 20);
  
  /// 设置当前分页请求
  void setCurrentPageRequest(PageRequest request) {
    _currentPageRequest = request;
  }
  
  /// 重置分页状态
  void resetPagination() {
    if (_currentPageRequest != null) {
      _currentPageRequest = _currentPageRequest!.resetPage();
    }
    hasMoreData.value = true;
    isLoadingMore.value = false;
    paginationError.value = '';
    _retryCount = 0;
    LogUtil.debug('分页状态已重置');
  }
  
  /// 统一的加载更多处理
  /// 
  /// [loadMoreFunction] 实际的加载更多逻辑
  /// [enableRetry] 是否启用重试机制
  /// [showErrorToast] 是否显示错误提示
  Future<bool> handleLoadMore(
    Future<bool> Function(PageRequest request) loadMoreFunction, {
    bool enableRetry = true,
    bool showErrorToast = true,
  }) async {
    // 边界检查
    if (isLoadingMore.value) {
      LogUtil.debug('已有加载任务正在进行中，跳过重复请求');
      return false;
    }
    
    if (!hasMoreData.value) {
      LogUtil.debug('没有更多数据了，跳过加载');
      return false;
    }
    
    try {
      isLoadingMore.value = true;
      paginationError.value = '';
      
      // 创建下一页请求
      final nextPageRequest = currentPageRequest.nextPage();
      LogUtil.debug('加载更多数据：请求第${nextPageRequest.page}页');
      
      // 执行加载逻辑
      final success = await loadMoreFunction(nextPageRequest);
      
      if (success) {
        // 成功时更新分页请求
        _currentPageRequest = nextPageRequest;
        _retryCount = 0; // 重置重试计数
        LogUtil.debug('加载更多成功，当前页码: ${nextPageRequest.page}');
        return true;
      } else {
        // 失败时处理重试
        return await _handleLoadMoreFailure(
          loadMoreFunction,
          enableRetry: enableRetry,
          showErrorToast: showErrorToast,
        );
      }
    } catch (e) {
      LogUtil.error('加载更多数据失败: $e');
      return await _handleLoadMoreError(
        e,
        loadMoreFunction,
        enableRetry: enableRetry,
        showErrorToast: showErrorToast,
      );
    } finally {
      // 延迟关闭加载状态，确保UI动画平滑
      Future.delayed(const Duration(milliseconds: 300), () {
        isLoadingMore.value = false;
      });
    }
  }
  
  /// 处理加载更多失败
  Future<bool> _handleLoadMoreFailure(
    Future<bool> Function(PageRequest request) loadMoreFunction, {
    required bool enableRetry,
    required bool showErrorToast,
  }) async {
    final errorMessage = '加载更多数据失败';
    paginationError.value = errorMessage;
    
    if (enableRetry && _retryCount < maxRetryCount) {
      _retryCount++;
      LogUtil.info('加载更多失败，准备第$_retryCount次重试');
      
      if (showErrorToast) {
        ToastUtil.warning('Loading failed, retrying... ($_retryCount/$maxRetryCount)');
      }
      
      // 延迟重试
      await Future.delayed(retryDelay);
      
      // 递归重试
      return await handleLoadMore(
        loadMoreFunction,
        enableRetry: enableRetry,
        showErrorToast: showErrorToast,
      );
    } else {
      // 重试次数用完或不启用重试
      if (showErrorToast) {
        ToastUtil.error('Failed to load more data');
      }
      
      // 使用统一的错误处理
      ErrorHandler.handleException(
        Exception(errorMessage),
        showSnackbar: showErrorToast,
      );
      
      return false;
    }
  }
  
  /// 处理加载更多异常
  Future<bool> _handleLoadMoreError(
    dynamic error,
    Future<bool> Function(PageRequest request) loadMoreFunction, {
    required bool enableRetry,
    required bool showErrorToast,
  }) async {
    paginationError.value = '加载数据时发生异常';
    
    // 根据错误类型决定是否重试
    final shouldRetry = enableRetry && 
                       _retryCount < maxRetryCount && 
                       _isRetryableError(error);
    
    if (shouldRetry) {
      _retryCount++;
      LogUtil.info('加载更多异常，准备第$_retryCount次重试: $error');
      
      if (showErrorToast) {
        ToastUtil.warning('Network error, retrying... ($_retryCount/$maxRetryCount)');
      }
      
      // 延迟重试
      await Future.delayed(retryDelay);
      
      // 递归重试
      return await handleLoadMore(
        loadMoreFunction,
        enableRetry: enableRetry,
        showErrorToast: showErrorToast,
      );
    } else {
      // 不重试或重试次数用完
      if (showErrorToast) {
        ToastUtil.error('Failed to load more data');
      }
      
      // 使用统一的错误处理
      ErrorHandler.handleException(error, showSnackbar: showErrorToast);
      
      return false;
    }
  }
  
  /// 判断错误是否可重试
  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // 网络相关错误通常可以重试
    if (errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('connection') ||
        errorString.contains('socket')) {
      return true;
    }
    
    // 服务器5xx错误可以重试
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504')) {
      return true;
    }
    
    // 其他错误不重试
    return false;
  }
  
  /// 手动重试加载更多
  Future<bool> retryLoadMore(
    Future<bool> Function(PageRequest request) loadMoreFunction, {
    bool showErrorToast = true,
  }) async {
    LogUtil.info('手动重试加载更多');
    _retryCount = 0; // 重置重试计数
    paginationError.value = '';
    
    return await handleLoadMore(
      loadMoreFunction,
      enableRetry: true,
      showErrorToast: showErrorToast,
    );
  }
  
  /// 更新是否有更多数据的状态
  void updateHasMoreData(bool hasMore) {
    hasMoreData.value = hasMore;
    LogUtil.debug('更新hasMoreData状态: $hasMore');
  }
  
  /// 获取分页状态摘要
  String getPaginationStatusSummary() {
    return '页码: ${currentPageRequest.page}, '
           '加载中: ${isLoadingMore.value}, '
           '有更多: ${hasMoreData.value}, '
           '重试次数: $_retryCount, '
           '错误: ${paginationError.value.isEmpty ? "无" : paginationError.value}';
  }
  
  /// 清理分页资源
  void disposePagination() {
    isLoadingMore.close();
    hasMoreData.close();
    paginationError.close();
    LogUtil.debug('分页资源已清理');
  }
}
