import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/common/utils/logger.dart';

/// 虚拟化消息列表组件
/// 
/// 仅渲染可见区域的消息，提高长对话的性能
class VirtualizedMessageList extends StatefulWidget {
  /// 消息列表
  final List<Message> messages;
  
  /// 是否反转列表（从下往上排列）
  final bool reverse;
  
  /// 滚动控制器
  final ScrollController? controller;
  
  /// 消息项构建器
  final Widget Function(BuildContext, Message) itemBuilder;
  
  /// 加载中指示器构建器
  final Widget Function(BuildContext)? loadingBuilder;
  
  /// AI回复中指示器构建器
  final Widget Function(BuildContext)? aiTypingBuilder;
  
  /// 是否正在加载更多
  final bool isLoadingMore;
  
  /// AI是否正在回复
  final bool isAiTyping;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 滚动物理特性
  final ScrollPhysics? physics;
  
  /// 估计的消息项高度
  final double estimatedItemHeight;
  
  /// 加载更多触发回调
  final VoidCallback? onLoadMore;
  
  /// 下拉刷新回调
  final Future<void> Function()? onRefresh;

  const VirtualizedMessageList({
    Key? key,
    required this.messages,
    required this.itemBuilder,
    this.reverse = true,
    this.controller,
    this.loadingBuilder,
    this.aiTypingBuilder,
    this.isLoadingMore = false,
    this.isAiTyping = false,
    this.padding,
    this.physics,
    this.estimatedItemHeight = 80.0,
    this.onLoadMore,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<VirtualizedMessageList> createState() => _VirtualizedMessageListState();
}

class _VirtualizedMessageListState extends State<VirtualizedMessageList> {
  // 可见消息的起始索引
  int _firstVisibleIndex = 0;
  
  // 可见消息的结束索引
  int _lastVisibleIndex = 20; // 初始值设置为较大值，确保首次渲染时显示足够的消息
  
  // 缓存区大小（屏幕上下额外渲染的项数）
  final int _cacheExtent = 5; // 减小缓存区大小，优化性能
  
  // 使用PageStorageKey保持滚动位置
  final PageStorageKey _pageStorageKey = const PageStorageKey('virtualized_messages');
  
  // 视口高度
  double _viewportHeight = 0;
  
  // 上次滚动位置
  double _lastScrollOffset = 0;
  
  // 是否已初始化
  bool _initialized = false;
  
  // 是否禁用虚拟化（短列表不需要虚拟化）
  bool _disableVirtualization = false;
  
  @override
  void initState() {
    super.initState();
    
    // 添加滚动监听
    widget.controller?.addListener(_updateVisibleItems);
    
    // 初始化可见项索引
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // 对于短列表，禁用虚拟化
      if (widget.messages.length < 50) {
        _disableVirtualization = true;
      }
      
      _updateVisibleItems();
      _initialized = true;
    });
  }
  
  @override
  void didUpdateWidget(VirtualizedMessageList oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果消息列表发生变化，更新可见项
    if (oldWidget.messages.length != widget.messages.length) {
      // 对于短列表，禁用虚拟化
      if (widget.messages.length < 50) {
        _disableVirtualization = true;
      } else {
        _disableVirtualization = false;
      }
      
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _updateVisibleItems();
      });
    }
    
    // 如果控制器变化，更新监听器
    if (oldWidget.controller != widget.controller) {
      oldWidget.controller?.removeListener(_updateVisibleItems);
      widget.controller?.addListener(_updateVisibleItems);
    }
  }
  
  @override
  void dispose() {
    widget.controller?.removeListener(_updateVisibleItems);
    super.dispose();
  }
  
  // 更新可见项索引
  void _updateVisibleItems() {
    if (!mounted || widget.controller == null || !widget.controller!.hasClients) {
      return;
    }
    
    // 如果禁用虚拟化，显示所有项
    if (_disableVirtualization) {
      setState(() {
        _firstVisibleIndex = 0;
        _lastVisibleIndex = widget.messages.length - 1;
      });
      return;
    }
    
    // 获取视口信息
    final viewportHeight = widget.controller!.position.viewportDimension;
    final scrollOffset = widget.controller!.offset;
    
    // 如果视口高度为0，延迟更新
    if (viewportHeight == 0) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _updateVisibleItems();
      });
      return;
    }
    
    // 保存视口高度
    _viewportHeight = viewportHeight;
    
    // 计算可见项的索引范围
    final itemCount = widget.messages.length;
    final estimatedItemHeight = widget.estimatedItemHeight;
    
    // 计算可见项的索引范围
    int firstVisibleIndex;
    int lastVisibleIndex;
    
    if (widget.reverse) {
      // 反转列表（从下往上）的情况下，scrollOffset=0表示最新消息（列表底部）
      // 最新消息在messages列表的末尾，最旧消息在列表开头
      
      // 计算第一个可见项的索引（从列表末尾开始计算）
      int visibleItemsCount = (viewportHeight / estimatedItemHeight).ceil();
      int firstVisibleFromBottom = (scrollOffset / estimatedItemHeight).floor();
      
      // 在反转列表中，最新的消息（列表末尾）对应于视图底部
      // 所以第一个可见项是从列表末尾往前数
      firstVisibleIndex = Math.max(0, itemCount - firstVisibleFromBottom - visibleItemsCount);
      lastVisibleIndex = Math.min(itemCount - 1, itemCount - firstVisibleFromBottom - 1);
      
      // 确保索引顺序正确（firstVisibleIndex <= lastVisibleIndex）
      if (firstVisibleIndex > lastVisibleIndex) {
        int temp = firstVisibleIndex;
        firstVisibleIndex = lastVisibleIndex;
        lastVisibleIndex = temp;
      }
    } else {
      // 正常列表（从上往下）的情况
      firstVisibleIndex = (scrollOffset / estimatedItemHeight).floor();
      lastVisibleIndex = ((scrollOffset + viewportHeight) / estimatedItemHeight).ceil();
    }
    
    // 添加缓存区
    firstVisibleIndex = Math.max(0, firstVisibleIndex - _cacheExtent);
    lastVisibleIndex = Math.min(itemCount - 1, lastVisibleIndex + _cacheExtent);
    
    LogUtil.debug('虚拟列表可见项: $firstVisibleIndex-$lastVisibleIndex, 总数: $itemCount, 滚动位置: $scrollOffset');
    
    // 如果可见项范围变化，更新状态
    if (_firstVisibleIndex != firstVisibleIndex || _lastVisibleIndex != lastVisibleIndex) {
      setState(() {
        _firstVisibleIndex = firstVisibleIndex;
        _lastVisibleIndex = lastVisibleIndex;
      });
    }
    
    // 更新上次滚动位置
    _lastScrollOffset = scrollOffset;
  }
  
  // 获取实际消息索引
  int _getActualMessageIndex(int index) {
    final int messageCount = widget.messages.length;
    final int loadingOffset = widget.isLoadingMore ? 1 : 0;
    
    if (widget.reverse) {
      // 反转列表的情况下，视图中的第一项对应于消息列表的最后一项
      return messageCount - 1 - index;
    } else {
      // 正常列表的情况下
      return index - (widget.isLoadingMore && !widget.reverse ? loadingOffset : 0);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final messages = widget.messages;
    
    // 如果消息列表为空，返回空视图
    if (messages.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // 计算列表项总数
    final int totalItems = messages.length +
      (widget.isLoadingMore ? 1 : 0) +
      (widget.isAiTyping ? 1 : 0);
    
    // 构建基础列表
    Widget child = ListView.builder(
      key: _pageStorageKey, // 使用PageStorageKey保持滚动位置
      controller: widget.controller,
      reverse: widget.reverse,
      padding: widget.padding,
      // 使用更合适的滚动物理特性，确保下拉刷新能正确触发
      physics: widget.physics ?? 
        (widget.onRefresh != null 
          ? const AlwaysScrollableScrollPhysics()
          : const ClampingScrollPhysics()),
      itemCount: totalItems,
      // 不使用itemExtent，因为消息高度可变
      // itemExtent: widget.estimatedItemHeight,
      itemBuilder: (context, index) {
        // 在反转列表中，处理不同类型的项目（加载更多、AI回复中、普通消息）
        if (widget.reverse) {
          // AI回复气泡 (显示在列表底部，反转后是第一个)
          if (widget.isAiTyping && index == 0) {
            return widget.aiTypingBuilder?.call(context) ?? const SizedBox.shrink();
          }
          
          // 加载更多指示器 (在AI回复后显示)
          if (widget.isLoadingMore && index == (widget.isAiTyping ? 1 : 0)) {
            return widget.loadingBuilder?.call(context) ?? const SizedBox.shrink();
          }
          
          // 调整消息索引
          final int messageOffset = (widget.isLoadingMore ? 1 : 0) + (widget.isAiTyping ? 1 : 0);
          final actualIndex = index - messageOffset;
          
          // 检查索引是否有效
          if (actualIndex < 0 || actualIndex >= messages.length) {
            return const SizedBox.shrink();
          }
          
          // 计算正确的消息索引
          final messageIndex = messages.length - 1 - actualIndex;
          
          if (messageIndex < 0 || messageIndex >= messages.length) {
            return const SizedBox.shrink();
          }
          
          final message = messages[messageIndex];
          
          // 检查是否在可见范围内
          if (_initialized && !_disableVirtualization && 
              (messageIndex < _firstVisibleIndex || messageIndex > _lastVisibleIndex)) {
            // 返回占位符 - 使用透明占位符
            return SizedBox(
              height: widget.estimatedItemHeight,
              // 确保占位符也是透明的
              child: const SizedBox.shrink(),
            );
          }
          
          // 构建消息项
          return widget.itemBuilder(context, message);
        }
        // 非反转列表
        else {
          // 消息项
          if (index < messages.length) {
            final message = messages[index];
            
            // 检查是否在可见范围内
            if (_initialized && !_disableVirtualization && 
                (index < _firstVisibleIndex || index > _lastVisibleIndex)) {
              // 返回占位符 - 使用透明占位符
              return SizedBox(
                height: widget.estimatedItemHeight,
                // 确保占位符也是透明的
                child: const SizedBox.shrink(),
              );
            }
            
            // 构建消息项
            return widget.itemBuilder(context, message);
          }
          // 加载更多指示器
          else if (widget.isLoadingMore && index == messages.length) {
            return widget.loadingBuilder?.call(context) ?? const SizedBox.shrink();
          }
          // AI回复气泡
          else if (widget.isAiTyping && index == messages.length + (widget.isLoadingMore ? 1 : 0)) {
            return widget.aiTypingBuilder?.call(context) ?? const SizedBox.shrink();
          }
          
          return const SizedBox.shrink();
        }
      },
    );

    // 包装在Container中确保背景透明
    child = Container(
      color: Colors.transparent,
      child: child,
    );
    
    // 如果需要下拉刷新，添加RefreshIndicator
    if (widget.onRefresh != null) {
      child = RefreshIndicator(
        onRefresh: () {
          LogUtil.debug('RefreshIndicator - 触发下拉刷新');
          return widget.onRefresh!();
        },
        color: Colors.white70, // 半透明白色，与骨架屏风格一致
        backgroundColor: Colors.grey.withOpacity(0.2), // 半透明灰色背景
        displacement: 40,
        edgeOffset: 20,
        // 确保在反转列表中也能正常工作
        triggerMode: RefreshIndicatorTriggerMode.anywhere,
        child: child,
      );
    }
    
    return child;
  }
}

// 提供Math类，避免与dart:math冲突
class Math {
  static int max(int a, int b) => a > b ? a : b;
  static int min(int a, int b) => a < b ? a : b;
} 