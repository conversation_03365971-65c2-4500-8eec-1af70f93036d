import 'package:flutter/material.dart';
import 'package:rolio/common/constants/colors_constants.dart';

class AppTabBar extends StatelessWidget {
  final List<String> tabs;
  final int selectedIndex;
  final Function(int)? onTabSelected;
  
  const AppTabBar({
    Key? key,
    required this.tabs,
    required this.selectedIndex,
    this.onTabSelected,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(
          tabs.length,
          (index) => _buildTabItem(tabs[index], index == selectedIndex, 
            () => onTabSelected?.call(index)),
        ),
      ),
    );
  }
  
  Widget _buildTabItem(String title, bool isSelected, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 20),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
        decoration: isSelected 
            ? const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.primary,
                    width: 2,
                  ),
                ),
              ) 
            : null,
        child: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade400,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
} 