import 'package:intl/intl.dart';

/// 日期时间相关常量
class DateTimeConstants {
  /// 默认使用UTC+0时区
  static const int TIMEZONE_OFFSET = 0;
  
  /// ISO8601标准格式
  static final DateFormat ISO_8601_FORMAT = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
  
  /// 消息显示格式
  static final DateFormat MESSAGE_TIME_FORMAT = DateFormat('HH:mm');
  
  /// 日期显示格式
  static final DateFormat DATE_FORMAT = DateFormat('yyyy-MM-dd');
  
  /// 时间显示格式
  static final DateFormat TIME_FORMAT = DateFormat('HH:mm:ss');
  
  /// 日期时间显示格式
  static final DateFormat DATETIME_FORMAT = DateFormat('yyyy-MM-dd HH:mm:ss');
  
  /// 最小有效年份
  static const int MIN_VALID_YEAR = 2000;
  
  /// 最大有效年份（当前年份+1）
  static int get MAX_VALID_YEAR => DateTime.now().year + 1;
} 