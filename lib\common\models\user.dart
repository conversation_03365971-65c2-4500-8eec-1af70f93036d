/// Firebase Auth用户模型
class User {
  /// 构造函数
  User({
    required this.uid,
    this.email,
    required this.userName,
    this.photoURL,
    this.isAnonymous = false,
    this.provider,
  });

  /// 用户唯一标识符
  final String uid;
  
  /// 用户邮箱
  final String? email;
  
  /// 用户显示名称
  final String userName;
  
  /// 用户头像URL
  final String? photoURL;
  
  /// 是否为匿名用户
  final bool isAnonymous;
  
  /// 登录提供商(google, facebook等)
  final String? provider;

  /// 将用户数据转换为Map
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'uid': uid,
      'email': email,
      'userName': userName,
      'photoURL': photoURL,
      'isAnonymous': isAnonymous,
      'provider': provider,
    };
  }

  /// 从Map创建用户对象
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      uid: map['uid'] as String,
      email: map['email'] != null ? map['email'] as String : null,
      userName: map['userName'] as String,
      photoURL: map['photoURL'] != null ? map['photoURL'] as String : null,
      isAnonymous: map['isAnonymous'] as bool? ?? false,
      provider: map['provider'] != null ? map['provider'] as String : null,
    );
  }

  /// 从Firebase User创建用户对象
  factory User.fromFirebaseUser(dynamic firebaseUser) {
    return User(
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      userName: firebaseUser.displayName ?? 'User',
      photoURL: firebaseUser.photoURL,
      isAnonymous: firebaseUser.isAnonymous,
      provider: firebaseUser.providerData.isNotEmpty 
          ? firebaseUser.providerData[0].providerId 
          : null,
    );
  }

  @override
  String toString() {
    return 'User(uid: $uid, email: $email, userName: $userName, photoURL: $photoURL, isAnonymous: $isAnonymous, provider: $provider)';
  }
} 