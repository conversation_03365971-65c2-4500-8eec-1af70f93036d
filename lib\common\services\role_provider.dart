import 'package:get/get.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/interfaces/session_provider.dart';

/// 角色服务
/// 
/// 提供角色相关功能的委托实现
/// 实现IRoleProvider接口，并将所有调用委托给注入的具体实现
class RoleProvider extends GetxService implements IRoleProvider {
  // 委托的角色提供者实现
  final IRoleProvider _delegate;
  
  // 构造函数，需要提供具体的委托实现
  RoleProvider({required IRoleProvider delegate}) : _delegate = delegate {
    LogUtil.info('RoleProvider: 已注入委托实现');
  }
  
  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('RoleProvider初始化完成');
  }
  
  // === IRoleProvider接口实现，全部委托给具体实现 ===
  
  @override
  Future<AiRole?> getRoleById(int roleId) => _delegate.getRoleById(roleId);
  
  @override
  Stream<List<AiRole>> getRoles() => _delegate.getRoles();
  
  @override
  Future<String?> getAvatarUrlById(int roleId) => _delegate.getAvatarUrlById(roleId);
  
  @override
  Future<String?> getCoverUrlById(int roleId) => _delegate.getCoverUrlById(roleId);
  
  @override
  Future<String?> getRoleNameById(int roleId) => _delegate.getRoleNameById(roleId);
  
  @override
  Future<AiRole?> getNextRecommendRole(int currentRoleId) => 
      _delegate.getNextRecommendRole(currentRoleId);
  
  @override
  Future<AiRole?> getPreviousRecommendRole(int currentRoleId) => 
      _delegate.getPreviousRecommendRole(currentRoleId);
  
  @override
  Future<AiRole?> getNextSessionRole(int currentRoleId) => 
      _delegate.getNextSessionRole(currentRoleId);
  
  @override
  Future<AiRole?> getPreviousSessionRole(int currentRoleId) => 
      _delegate.getPreviousSessionRole(currentRoleId);
  
  /// 获取角色描述 - 非接口方法，但提供便利
  Future<String?> getDescriptionById(int roleId) async {
    try {
      final role = await _delegate.getRoleById(roleId);
      return role?.description ?? '这是一个AI助手';
    } catch (e) {
      LogUtil.error('获取角色描述失败: $e');
      return '这是一个AI助手';
    }
  }
  
  /// 更新角色的会话ID
  Future<void> updateRoleConversationId(int roleId, int conversationId) async {
    try {
      // 获取当前角色
      final role = await getRoleById(roleId);
      
      if (role != null) {
        // 如果委托实现支持更新角色会话ID的方法，则调用它
        if (_delegate is dynamic && 
            (_delegate as dynamic).updateRoleConversationId != null) {
          try {
            final updatedRole = role.copyWith(conversationId: conversationId);
            await (_delegate as dynamic).updateRoleConversationId(
                roleId, conversationId, updatedRole);
            LogUtil.info('已更新角色会话ID: roleId=$roleId, conversationId=$conversationId');
            return;
          } catch (e) {
            LogUtil.error('委托更新角色会话ID失败: $e');
          }
        }
        
        LogUtil.warn('委托不支持更新角色会话ID，无法更新');
      } else {
        LogUtil.warn('无法更新角色会话ID：未找到角色 ID=$roleId');
      }
    } catch (e) {
      LogUtil.error('更新角色会话ID失败: $e');
    }
  }

  /// 安全地获取对象的属性值
  dynamic _getPropertySafely(dynamic obj, String propertyName) {
    try {
      // 使用dart:mirrors是不推荐的，我们尝试使用更直接的方法
      
      // 尝试直接访问属性（如果对象支持[]操作符）
      if (obj is Map) {
        return obj[propertyName];
      }
      
      // 尝试通过getter方法访问
      switch (propertyName) {
        case 'id': 
          return obj.id;
        case 'aiRoleId': 
          return obj.aiRoleId;
        case 'title': 
          return obj.title;
        case 'avatarUrl': 
          return obj.avatarUrl;
        case 'coverUrl': 
          return obj.coverUrl;
        case 'description': 
          return obj.description;
        default:
          return null;
      }
    } catch (e) {
      // 访问失败，返回null
      return null;
    }
  }

  /// 获取会话角色列表
  ///
  /// 返回所有会话角色，如果获取失败则返回空列表
  Future<List<AiRole>> getSessionRoles() async {
    try {
      // 尝试从SessionService获取会话角色列表
      if (Get.isRegistered<ISessionProvider>()) {
        final sessionProvider = Get.find<ISessionProvider>();

        try {
          // 先检查是否有现有的会话数据
          List<dynamic> sessions = sessionProvider.sessions.cast<dynamic>();

          // 如果没有现有数据，尝试异步获取
          if (sessions.isEmpty) {
            LogUtil.debug('会话列表为空，尝试异步获取');
            try {
              // 设置超时时间，避免无限等待
              final stream = sessionProvider.getSessions();
              sessions = await stream.first.timeout(
                const Duration(seconds: 3),
                onTimeout: () {
                  LogUtil.warn('获取会话列表超时，使用空列表');
                  return <dynamic>[];
                },
              );
            } catch (e) {
              LogUtil.warn('获取会话列表失败: $e');
              sessions = [];
            }
          } else {
            LogUtil.debug('使用现有会话列表，数量: ${sessions.length}');
          }
          
          if (sessions.isNotEmpty) {
            // 将会话列表转换为角色列表
            final roles = <AiRole>[];
            
            for (final session in sessions) {
              try {
                // 处理不同类型的会话对象
                if (session is Map<String, dynamic>) {
                  // 如果是Map类型，尝试转换为AiRole
                  final role = AiRole(
                    id: session['aiRoleId'] ?? session['ai_role_id'] ?? 0,
                    name: session['title'] ?? session['name'] ?? 'AI助手',
                    avatarUrl: session['avatarUrl'] ?? session['avatar_url'] ?? '',
                    coverUrl: session['coverUrl'] ?? session['cover_url'] ?? '',
                    description: session['description'] ?? '',
                    tags: [],
                    position: 0,
                    conversationId: session['id'] ?? session['conversationId'] ?? session['conversation_id'] ?? 0,
                  );
                  roles.add(role);
                } else if (session is AiRole) {
                  // 如果已经是AiRole类型，直接添加
                  roles.add(session);
                } else {
                  // 处理Session类型或其他类型
                  try {
                    // 尝试通过属性访问
                    final dynamic id = _getPropertySafely(session, 'id');
                    final dynamic aiRoleId = _getPropertySafely(session, 'aiRoleId');
                    final dynamic title = _getPropertySafely(session, 'title');
                    final dynamic avatarUrl = _getPropertySafely(session, 'avatarUrl');
                    final dynamic coverUrl = _getPropertySafely(session, 'coverUrl');
                    final dynamic description = _getPropertySafely(session, 'description');
                    
                    // 检查是否获取到了必要的属性
                    if (id != null && aiRoleId != null) {
                      final role = AiRole(
                        id: aiRoleId as int,
                        name: (title as String?) ?? 'AI助手',
                        avatarUrl: (avatarUrl as String?) ?? '',
                        coverUrl: (coverUrl as String?) ?? '',
                        description: (description as String?) ?? '',
                        tags: [],
                        position: 0,
                        conversationId: id as int,
                      );
                      roles.add(role);
                    }
                  } catch (e) {
                    LogUtil.error('通过属性访问获取会话属性失败: $e');
                  }
                }
              } catch (e) {
                LogUtil.error('处理单个会话对象失败: $e');
                // 继续处理下一个会话对象
              }
            }
            
            if (roles.isNotEmpty) {
              LogUtil.debug('从SessionService获取会话角色列表: ${roles.length}个角色');
              return roles;
            }
          }
        } catch (e) {
          LogUtil.error('从SessionService获取会话列表失败: $e');
        }
      }
      
      // 如果无法获取，返回空列表
      LogUtil.warn('无法从SessionService获取会话角色列表，返回空列表');
      return [];
    } catch (e) {
      LogUtil.error('获取会话角色列表失败: $e');
      return [];
    }
  }
  
  /// 获取下一个角色（统一入口）
  Future<AiRole?> getNextRole(int currentRoleId, bool fromSessionsList) async {
    if (fromSessionsList) {
      // 从会话列表获取下一个角色
      LogUtil.info('从会话列表获取下一个角色');
      
      // 获取会话角色列表
      final sessionRoles = await getSessionRoles();
      if (sessionRoles.isNotEmpty) {
        // 查找当前角色在列表中的位置
        final currentIndex = sessionRoles.indexWhere((role) => role.id == currentRoleId);
        
        // 如果找不到当前角色，返回第一个角色
        if (currentIndex == -1) {
          LogUtil.debug('在会话角色列表中未找到当前角色ID: $currentRoleId，返回第一个角色');
          return sessionRoles.isNotEmpty ? sessionRoles[0] : null;
        }
        
        // 获取下一个角色，如果是最后一个则循环到第一个
        final nextIndex = (currentIndex + 1) % sessionRoles.length;
        LogUtil.debug('当前角色索引: $currentIndex, 下一个角色索引: $nextIndex, 总数: ${sessionRoles.length}');
        return sessionRoles[nextIndex];
      } else {
        // 如果会话列表为空，尝试从推荐列表获取
        LogUtil.warn('会话角色列表为空，回退到使用推荐列表');
        return await getNextRecommendRole(currentRoleId);
      }
    } else {
      // 从推荐列表获取下一个角色
      return await getNextRecommendRole(currentRoleId);
    }
  }
  
  /// 获取上一个角色（统一入口）
  Future<AiRole?> getPreviousRole(int currentRoleId, bool fromSessionsList) {
    if (fromSessionsList) {
      // 从会话列表获取上一个角色
      LogUtil.info('从会话列表获取上一个角色');
      
      // 获取会话角色列表
      return getSessionRoles().then((sessionRoles) {
        if (sessionRoles.isNotEmpty) {
          // 查找当前角色在列表中的位置
          final currentIndex = sessionRoles.indexWhere((role) => role.id == currentRoleId);
          
          // 如果找不到当前角色，返回最后一个角色
          if (currentIndex == -1) {
            LogUtil.debug('在会话角色列表中未找到当前角色ID: $currentRoleId，返回最后一个角色');
            return sessionRoles.isNotEmpty ? sessionRoles.last : null;
          }
          
          // 获取上一个角色，如果是第一个则循环到最后一个
          final previousIndex = (currentIndex - 1 + sessionRoles.length) % sessionRoles.length;
          LogUtil.debug('当前角色索引: $currentIndex, 上一个角色索引: $previousIndex, 总数: ${sessionRoles.length}');
          return sessionRoles[previousIndex];
        } else {
          // 如果会话列表为空，尝试从推荐列表获取
          LogUtil.warn('会话角色列表为空，回退到使用推荐列表');
          return getPreviousRecommendRole(currentRoleId);
        }
      });
    } else {
      // 从推荐列表获取上一个角色
      return getPreviousRecommendRole(currentRoleId);
    }
  }

  /// 根据会话ID重置角色的会话ID
  ///
  /// 当会话被删除时，查找所有引用该会话ID的角色并重置它们的会话ID为0
  Future<int> resetRoleConversationIdByConversationId(int conversationId) async {
    try {
      LogUtil.info('开始查找并重置引用会话ID: $conversationId 的所有角色');
      
      // 获取所有角色 - 避免使用Stream，直接使用同步方法
      List<AiRole> roles = [];
      try {
        LogUtil.debug('正在获取角色列表...');
        
        // 优先尝试使用getRolesList同步方法（如果委托支持）
        if (_delegate is dynamic && 
            (_delegate as dynamic).getRolesList != null) {
          LogUtil.debug('使用委托的getRolesList同步方法获取角色列表');
          try {
            roles = await (_delegate as dynamic).getRolesList();
            LogUtil.debug('通过getRolesList获取到角色列表，数量: ${roles.length}');
          } catch (e) {
            LogUtil.warn('getRolesList方法调用失败: $e，回退到Stream方式');
            roles = [];
          }
        }
        
        // 如果同步方法失败或不支持，使用Stream方式但加上超时保护
        if (roles.isEmpty) {
          LogUtil.debug('回退到使用Stream方式获取角色列表');
          try {
            final rolesStream = _delegate.getRoles();
            
            // 使用timeout避免无限等待，最多等待3秒
            final streamResult = await rolesStream.timeout(
              const Duration(seconds: 3),
              onTimeout: (sink) {
                LogUtil.warn('获取角色Stream超时，返回空列表');
                sink.add(<AiRole>[]);
              },
            ).first;
            
            roles = streamResult;
            LogUtil.debug('通过Stream获取到角色列表，数量: ${roles.length}');
          } catch (e) {
            LogUtil.error('Stream方式获取角色列表失败: $e');
            roles = [];
          }
        }
        
        LogUtil.debug('最终获取角色列表，总数: ${roles.length}');
      } catch (e) {
        LogUtil.error('获取角色列表失败: $e');
        return 0;
      }
      
      if (roles.isEmpty) {
        LogUtil.warn('角色列表为空，无需重置');
        return 0;
      }
      
      // 查找引用该会话ID的角色
      final matchingRoles = roles.where((role) => role.conversationId == conversationId).toList();
      LogUtil.debug('找到 ${matchingRoles.length} 个角色引用会话ID: $conversationId');
      
      if (matchingRoles.isEmpty) {
        LogUtil.warn('未找到引用会话ID: $conversationId 的角色');
        return 0;
      }
      
      // 重置每个角色的会话ID
      int updatedCount = 0;
      for (final role in matchingRoles) {
        try {
          LogUtil.debug('正在重置角色会话ID: roleId=${role.id}, name=${role.name}');
          
          // 如果委托实现支持更新角色会话ID的方法，则调用它
          if (_delegate is dynamic && 
              (_delegate as dynamic).updateRoleConversationId != null) {
            final updatedRole = role.copyWith(conversationId: 0);
            await (_delegate as dynamic).updateRoleConversationId(
                role.id, 0, updatedRole);
            LogUtil.info('已重置角色会话ID: roleId=${role.id}, name=${role.name}, oldConversationId=$conversationId');
            updatedCount++;
          } else {
            LogUtil.warn('委托实现不支持updateRoleConversationId方法，跳过角色: ${role.id}');
          }
        } catch (e) {
          LogUtil.error('重置角色会话ID失败: roleId=${role.id}, error=$e');
        }
      }
      
      LogUtil.info('共重置 $updatedCount 个角色的会话ID');
      return updatedCount;
    } catch (e) {
      LogUtil.error('重置角色会话ID失败: $e');
      return 0;
    }
  }
}