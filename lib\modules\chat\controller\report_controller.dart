import 'package:get/get.dart';
import 'package:rolio/common/enums/report_reason.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/chat/service/report_service.dart';
import 'package:rolio/modules/chat/view/report_page.dart';
import "package:rolio/common/services/firebase_analytics_service.dart";

/// 举报控制器
///
/// 负责管理举报相关的UI状态和业务逻辑
class ReportController extends GetxController {
  final ReportService _reportService;

  // 加载状态
  final RxBool isSubmitting = RxBool(false);

  // 最后一次提交时间，用于防止重复提交
  DateTime? _lastSubmitTime;

  // 重复提交最短间隔(秒)
  static const int _minSubmitIntervalSeconds = 3;

  ReportController({
    ReportService? reportService,
  }) : _reportService = reportService ?? Get.find<ReportService>();

  /// 提交举报
  ///
  /// 这个方法使用独立的HTTP请求提交举报，不依赖于WebSocket连接状态
  /// 同时实现了本地防重复提交的逻辑
  Future<bool> submitReport({
    required int roleId,
    required ReportReason reason,
    required String description,
  }) async {
    try {
      // 检查是否是重复提交(短时间内)
      if (_lastSubmitTime != null) {
        final timeSinceLastSubmit =
            DateTime.now().difference(_lastSubmitTime!).inSeconds;
        if (timeSinceLastSubmit < _minSubmitIntervalSeconds) {
          LogUtil.warn('短时间内重复提交举报，忽略本次请求');
          return false;
        }
      }

      // 如果已经在提交中，返回false
      if (isSubmitting.value) {
        LogUtil.warn('已有举报提交正在进行中，忽略本次请求');
        return false;
      }

      // 设置加载状态
      isSubmitting.value = true;

      LogUtil.info('提交举报请求，不依赖于WebSocket连接');

      // 记录本次提交时间
      _lastSubmitTime = DateTime.now();

      // 调用服务层提交举报
      final result = await _reportService.submitReport(
        roleId: roleId,
        reason: reason,
        description: description,
      );

      FirebaseAnalyticsService.logEvent(
          FirebaseAnalyticsEvent.report_msg, "role:$roleId;reason:$reason");

      return result;
    } catch (e) {
      LogUtil.error('举报提交控制器异常: $e');
      return false;
    } finally {
      // 无论成功与否，都重置加载状态
      isSubmitting.value = false;
    }
  }

  /// 导航到举报页面
  void navigateToReportPage({
    required int roleId,
    required String roleName,
  }) {
    // 打开举报页面，使用自然的页面过渡动画
    Get.to(
      () => ReportPage(
        roleId: roleId,
        roleName: roleName,
      ),
      transition: Transition.rightToLeft, // 右滑入动画
      duration: const Duration(milliseconds: 300), // 动画持续时间
    );
  }
}
