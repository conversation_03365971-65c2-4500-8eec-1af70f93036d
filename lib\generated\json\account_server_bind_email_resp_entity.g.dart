import 'package:rolio/generated/json/base/json_convert_content.dart';
import 'package:rolio/modules/login/model/account_server_bind_email_resp_entity.dart';

AccountServerBindEmailRespEntity $AccountServerBindEmailRespEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerBindEmailRespEntity accountServerBindEmailRespEntity =
      AccountServerBindEmailRespEntity();
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    accountServerBindEmailRespEntity.status = status;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    accountServerBindEmailRespEntity.userId = userId;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    accountServerBindEmailRespEntity.email = email;
  }
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    accountServerBindEmailRespEntity.token = token;
  }
  return accountServerBindEmailRespEntity;
}

Map<String, dynamic> $AccountServerBindEmailRespEntityToJson(
    AccountServerBindEmailRespEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['userId'] = entity.userId;
  data['email'] = entity.email;
  data['token'] = entity.token;
  return data;
}

extension AccountServerBindEmailRespEntityExtension
    on AccountServerBindEmailRespEntity {
  AccountServerBindEmailRespEntity copyWith({
    int? status,
    int? userId,
    String? email,
    String? token,
  }) {
    return AccountServerBindEmailRespEntity()
      ..status = status ?? this.status
      ..userId = userId ?? this.userId
      ..email = email ?? this.email
      ..token = token ?? this.token;
  }
}
