import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/data_validator.dart';
import 'package:rolio/common/models/validation_result.dart';

/// AI角色模型
class AiRole {
  /// 角色ID
  final int id;
  
  /// 角色名称
  final String name;
  
  /// 角色头像URL
  final String avatarUrl;
  
  /// 角色封面URL
  final String coverUrl;
  
  /// 角色描述
  final String description;
  
  /// 角色标签
  final List<String> tags;
  
  /// 角色位置（排序用）
  final int position;
  
  /// 角色问候语
  final String? greeting;
  
  /// 角色语气标签
  final List<String> toneTags;
  
  /// 角色专长
  final List<String> specialties;
  
  /// 是否为自定义角色
  final bool isCustom;
  
  /// 会话ID，如果为null表示没有对话过
  final int? conversationId;
  
  /// 是否被收藏
  final bool isFavorited;
  
  /// 收藏时间
  final DateTime? favoritedAt;

  /// 角色的对话数量
  final String conversationCount;
  
  /// 角色的消息数量
  final String chatCount;
  
  /// 构造函数
  AiRole({
    required this.id,
    required this.name,
    required this.avatarUrl,
    required this.coverUrl,
    required this.description,
    this.tags = const [],
    this.position = 0,
    this.greeting,
    this.toneTags = const [],
    this.specialties = const [],
    this.isCustom = false,
    this.conversationId,
    this.isFavorited = false,
    this.favoritedAt,
    this.conversationCount = '0',
    this.chatCount = '0',
  });
  
  /// 从JSON创建
  factory AiRole.fromJson(Map<String, dynamic> json) {
    try {
      
      // 使用DataValidator进行数据验证和安全获取
      final roleId = DataValidator.safeGetInt(json, 'id');
      final nameValue = DataValidator.safeGetString(json, 'name');
      final avatarUrlValue = DataValidator.safeGetString(json, 'avatar_url');
      final coverUrlValue = DataValidator.safeGetString(json, 'cover_url');
      final descriptionValue = DataValidator.safeGetString(json, 'description');
      final positionValue = DataValidator.safeGetInt(json, 'position');
      final isCustomValue = DataValidator.safeGetBool(json, 'is_custom');
      final conversationIdValue = json['conversation_id'] is int ? json['conversation_id'] as int : null;
      final isFavoritedValue = DataValidator.safeGetBool(json, 'is_favorited');
      
      // 获取对话数和消息数
      final conversationCountValue = DataValidator.safeGetString(json, 'conversation_count', defaultValue: '0');
      final chatCountValue = DataValidator.safeGetString(json, 'chat_count', defaultValue: '0');
      
      // 处理收藏时间
      DateTime? favoritedAtValue;
      if (json['favorited_at'] != null) {
        try {
          favoritedAtValue = DateTime.parse(json['favorited_at'].toString());
        } catch (e) {
          LogUtil.warn('解析收藏时间失败: ${json['favorited_at']}');
        }
      }
      
      // 获取列表字段
      final tagsList = DataValidator.safeGetStringList(json, 'tags');
      final toneTagsList = DataValidator.safeGetStringList(json, 'tone_tags');
      final specialtiesList = DataValidator.safeGetStringList(json, 'specialties');
      
      // 验证致命错误字段（核心字段）
      final fatalValidationResults = [
        DataValidator.validateRequiredFatal('id', roleId),
        DataValidator.validateRequiredFatal('name', nameValue),
        DataValidator.validateInt('id', roleId, min: 1, level: ValidationErrorLevel.FATAL),
      ];
      
      // 验证警告级别字段（非核心字段）
      final warningValidationResults = [
        DataValidator.validateRequiredWarning('avatar_url', avatarUrlValue),
        DataValidator.validateRequiredWarning('cover_url', coverUrlValue),
        DataValidator.validateRequiredWarning('description', descriptionValue),
        DataValidator.validateString('name', nameValue, minLength: 1, maxLength: 50),
        DataValidator.validateUrl('avatar_url', avatarUrlValue),
        DataValidator.validateUrl('cover_url', coverUrlValue),
        DataValidator.validateString('description', descriptionValue, maxLength: 500),
      ];
      
      // 合并验证结果
      final fatalResult = ValidationResult.merge(fatalValidationResults);
      final warningResult = ValidationResult.merge(warningValidationResults);
      final allResults = [...fatalValidationResults, ...warningValidationResults];
      final validationResult = ValidationResult.merge(allResults);
      
      // 如果有致命错误，记录错误并返回默认角色
      if (fatalResult.hasFatalError) {
        LogUtil.error('AI角色数据存在致命错误，返回默认角色: ${fatalResult.fatalErrorMessage}');
        return AiRole(
          id: 0,
          name: '数据无效',
          avatarUrl: '',
          coverUrl: '',
          description: '数据验证失败(致命错误): ${fatalResult.fatalErrorMessage}',
          tags: [],
          position: 0,
        );
      }
      
      // 如果只有警告级别错误，记录警告但继续创建角色
      if (!validationResult.isValid) {
        LogUtil.warn('AI角色数据存在警告级别问题: ${warningResult.warningMessage}');
      }
      
      final role = AiRole(
        id: roleId,
        name: nameValue,
        avatarUrl: avatarUrlValue,
        coverUrl: coverUrlValue,
        description: descriptionValue,
        tags: tagsList,
        position: positionValue,
        greeting: json['greeting']?.toString(),
        toneTags: toneTagsList,
        specialties: specialtiesList,
        isCustom: isCustomValue,
        conversationId: conversationIdValue,
        isFavorited: isFavoritedValue,
        favoritedAt: favoritedAtValue,
        conversationCount: conversationCountValue,
        chatCount: chatCountValue,
      );
      
      LogUtil.debug('成功创建AI角色: ID=${role.id}, 名称=${role.name}');
      return role;
    } catch (e) {
      LogUtil.error('解析AI角色数据失败(致命错误): $e');
      // 返回一个默认的AI角色，避免应用崩溃
      return AiRole(
        id: 0,
        name: '解析错误',
        avatarUrl: '',
        coverUrl: '',
        description: '数据解析失败(致命错误)',
        tags: [],
        position: 0,
      );
    }
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar_url': avatarUrl,
      'cover_url': coverUrl,
      'description': description,
      'tags': tags,
      'position': position,
      'greeting': greeting,
      'tone_tags': toneTags,
      'specialties': specialties,
      'is_custom': isCustom,
      'conversation_id': conversationId,
      'is_favorited': isFavorited,
      'favorited_at': favoritedAt?.toIso8601String(),
      'conversation_count': conversationCount,
      'chat_count': chatCount,
    };
  }
  
  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'avatar_url': avatarUrl,
      'cover_url': coverUrl,
      'description': description,
      'tags': tags,
      'position': position,
      'greeting': greeting,
      'tone_tags': toneTags,
      'specialties': specialties,
      'is_custom': isCustom,
      'conversation_id': conversationId,
      'is_favorited': isFavorited,
      'favorited_at': favoritedAt?.toIso8601String(),
      'conversation_count': conversationCount,
      'chat_count': chatCount,
    };
  }
  
  /// 创建副本
  AiRole copyWith({
    int? id,
    String? name,
    String? avatarUrl,
    String? coverUrl,
    String? description,
    List<String>? tags,
    int? position,
    String? greeting,
    List<String>? toneTags,
    List<String>? specialties,
    bool? isCustom,
    int? conversationId,
    bool? isFavorited,
    DateTime? favoritedAt,
    String? conversationCount,
    String? chatCount,
  }) {
    return AiRole(
      id: id ?? this.id,
      name: name ?? this.name,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      coverUrl: coverUrl ?? this.coverUrl,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      position: position ?? this.position,
      greeting: greeting ?? this.greeting,
      toneTags: toneTags ?? this.toneTags,
      specialties: specialties ?? this.specialties,
      isCustom: isCustom ?? this.isCustom,
      conversationId: conversationId ?? this.conversationId,
      isFavorited: isFavorited ?? this.isFavorited,
      favoritedAt: favoritedAt ?? this.favoritedAt,
      conversationCount: conversationCount ?? this.conversationCount,
      chatCount: chatCount ?? this.chatCount,
    );
  }
  
  /// 验证角色数据是否有效
  ValidationResult validate() {
    final validationResults = [
      DataValidator.validateRequired('id', id),
      DataValidator.validateRequired('name', name),
      DataValidator.validateRequired('avatarUrl', avatarUrl),
      DataValidator.validateRequired('coverUrl', coverUrl),
      DataValidator.validateRequired('description', description),
      
      DataValidator.validateInt('id', id, min: 1),
      DataValidator.validateString('name', name, minLength: 1, maxLength: 50),
      DataValidator.validateUrl('avatarUrl', avatarUrl),
      DataValidator.validateUrl('coverUrl', coverUrl),
      DataValidator.validateString('description', description, maxLength: 500),
    ];
    
    return ValidationResult.merge(validationResults);
  }
}