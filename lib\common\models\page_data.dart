/// 分页数据模型
/// 
/// 用于标准化分页请求和响应，提高类型安全性
/// 统一的分页数据模型
class PageData<T> {
  /// 当前页码
  final int page;
  
  /// 每页数量
  final int size;
  
  /// 总记录数
  final int total;
  
  /// 数据列表
  final List<T> items;
  
  /// 总页数
  int get totalPages => (total / size).ceil();
  
  /// 是否有下一页
  bool get hasNext => items.isNotEmpty && (page * size) < total;
  
  /// 是否有上一页
  bool get hasPrevious => page > 1;
  
  /// 构造函数
  const PageData({
    required this.page,
    required this.size,
    required this.total,
    required this.items,
  });
  
  /// 从JSON创建
  factory PageData.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    // 解析分页元数据
    final page = json['page'] as int? ?? 1;
    final size = json['size'] as int? ?? 10;
    final total = json['total'] as int? ?? 0;
    
    // 解析数据列表
    final itemsJson = json['items'] as List<dynamic>? ?? [];
    final items = itemsJson
        .map((item) => item is Map<String, dynamic> ? fromJsonT(item) : null)
        .where((item) => item != null)
        .cast<T>()
        .toList();
    
    return PageData<T>(
      page: page,
      size: size,
      total: total,
      items: items,
    );
  }
  
  /// 创建空分页数据
  factory PageData.empty() {
    return PageData<T>(
      page: 1,
      size: 10,
      total: 0,
      items: [],
    );
  }
  

  
  /// 转换为JSON
  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'page': page,
      'size': size,
      'total': total,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrevious': hasPrevious,
      'items': items.map((item) => toJsonT(item)).toList(),
    };
  }
  
  /// 通用的toJson方法，用于缓存序列化
  Map<String, dynamic> toJsonMap() {
    // 如果T是基本类型，直接使用简单序列化
    if (T == String || T == int || T == double || T == bool) {
      return {
        'page': page,
        'size': size,
        'total': total,
        'totalPages': totalPages,
        'hasNext': hasNext,
        'hasPrevious': hasPrevious,
        'items': items,
      };
    }
    
    // 尝试将每个项目转换为Map
    final serializedItems = items.map((item) {
      if (item == null) return null;
      
      // 检查item是否有toJson或toMap方法
      if (item is Map) {
        return item;
      } else if (item is List) {
        return item;
      } else {
        try {
          // 动态调用toJson或toMap方法
          final dynamic instance = item;
          if (instance != null) {
            if (instance.runtimeType.toString().contains('Session')) {
              // 特殊处理Session类型
              return (instance as dynamic).toMap();
            }
            
            // 尝试调用toJson
            if (instance.toJson != null && instance.toJson is Function) {
              return instance.toJson();
            }
            
            // 尝试调用toMap
            if (instance.toMap != null && instance.toMap is Function) {
              return instance.toMap();
            }
          }
        } catch (e) {
          print('序列化项目失败: $e');
        }
      }
      
      // 如果无法序列化，返回字符串表示
      return {'toString': item.toString()};
    }).toList();
    
    return {
      'page': page,
      'size': size,
      'total': total,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrevious': hasPrevious,
      'items': serializedItems,
    };
  }
  
  /// 复制并修改
  PageData<T> copyWith({
    int? page,
    int? size,
    int? total,
    List<T>? items,
  }) {
    return PageData<T>(
      page: page ?? this.page,
      size: size ?? this.size,
      total: total ?? this.total,
      items: items ?? this.items,
    );
  }
  
  /// 合并两个分页结果（用于加载更多场景）
  PageData<T> merge(PageData<T> other) {
    if (other.page <= page) {
      // 如果新结果的页码小于等于当前页码，直接返回新结果
      return other;
    }
    
    // 合并项目列表
    final mergedItems = [...items, ...other.items];
    
    // 创建新的分页结果
    return PageData<T>(
      items: mergedItems,
      total: other.total, // 使用最新的总数
      page: other.page,
      size: size,
    );
  }
} 