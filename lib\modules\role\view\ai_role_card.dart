import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/message_constants.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';
import 'package:rolio/widgets/ai_avatar.dart';

class AiRoleCard extends StatefulWidget {
  final AiRole role;
  final VoidCallback onTap;
  
  const AiRoleCard({
    Key? key,
    required this.role,
    required this.onTap,
  }) : super(key: key);
  
  @override
  State<AiRoleCard> createState() => _AiRoleCardState();
}

class _AiRoleCardState extends State<AiRoleCard> with SingleTickerProviderStateMixin {
  // 卡片缩放动画控制器
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  
  // 是否正在按下
  bool _isPressed = false;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    
    // 创建缩放动画
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeInOut,
      ),
    );
  }
  
  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    // 获取推荐控制器，用于检查图片是否已预加载
    final RecommendController controller = Get.find<RecommendController>();
    final bool isAvatarPreloaded = controller.isImagePreloaded(widget.role.avatarUrl);
    final bool isCoverPreloaded = controller.isImagePreloaded(widget.role.coverUrl);
    
    // 使用AnimatedBuilder实现缩放效果
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        child: GestureDetector(
          onTapDown: (_) {
            if (!_isPressed) {
              _isPressed = true;
              _scaleController.forward();
            }
          },
          onTapUp: (_) {
            if (_isPressed) {
              _isPressed = false;
              _scaleController.reverse().then((_) {
                widget.onTap();
              });
            }
          },
          onTapCancel: () {
            if (_isPressed) {
              _isPressed = false;
              _scaleController.reverse();
            }
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(StringsConsts.recommendCardBorderRadius),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                splashColor: Colors.white.withOpacity(0.1),
                highlightColor: Colors.transparent,
                borderRadius: BorderRadius.circular(StringsConsts.recommendCardBorderRadius),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(StringsConsts.recommendCardBorderRadius),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // 背景图片 
                      _buildCoverImage(isCoverPreloaded),
                      
                      // 渐变遮罩 - 增强文字可读性
                      Positioned.fill(
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.2),
                                Colors.black.withOpacity(0.5),
                                Colors.black.withOpacity(0.8),
                              ],
                              stops: const [0.5, 0.7, 0.8, 1.0],
                            ),
                          ),
                        ),
                      ),
                      
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 头像和名字放在一行
                              Row(
                                children: [
                                  // 头像
                                  Hero(
                                    tag: 'role_avatar_${widget.role.id}',
                                    child: AiAvatar(
                                      characterId: widget.role.id,
                                      avatarUrl: widget.role.avatarUrl,
                                      size: 32.0,
                                      borderColor: Colors.white,
                                      borderWidth: 2.0,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  // 名字
                                  Expanded(
                                    child: Text(
                                      widget.role.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: Colors.white,
                                        shadows: [
                                          Shadow(
                                            offset: Offset(0, 1),
                                            blurRadius: 3.0,
                                            color: Color.fromARGB(150, 0, 0, 0),
                                          ),
                                        ],
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // 描述
                              Text(
                                widget.role.description,
                                maxLines: 4, // 显示4行描述
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: Colors.white70,
                                  height: 1.3,
                                  shadows: [
                                    Shadow(
                                      offset: Offset(0, 1),
                                      blurRadius: 2.0,
                                      color: Color.fromARGB(100, 0, 0, 0),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 构建标签
  Widget _buildTags() {
    return SizedBox(
      height: 24,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: widget.role.tags.length > 3 ? 3 : widget.role.tags.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white24,
                width: 0.5,
              ),
            ),
            child: Text(
              widget.role.tags[index],
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// 构建封面图片
  Widget _buildCoverImage(bool isPreloaded) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
      ),
      child: isPreloaded 
        ? _buildOptimizedImage() 
        : _buildStandardImage(),
    );
  }
  
  /// 构建优化的图片（预加载情况）
  Widget _buildOptimizedImage() {
    return CachedNetworkImage(
      imageUrl: widget.role.coverUrl,
      fit: BoxFit.cover, // 确保图片填充整个区域而不变形
      fadeInDuration: const Duration(milliseconds: 100),
      placeholder: (context, url) => Container(
        color: Colors.grey[900],
      ),
      errorWidget: _buildErrorWidget,
    );
  }
  
  /// 构建标准的图片（未预加载情况）
  Widget _buildStandardImage() {
    return CachedNetworkImage(
      imageUrl: widget.role.coverUrl,
      fit: BoxFit.cover, // 确保图片填充整个区域而不变形
      placeholder: (context, url) => Container(
        color: Colors.grey[900],
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: Colors.white,
          ),
        ),
      ),
      errorWidget: _buildErrorWidget,
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
    );
  }
  
  /// 构建错误显示组件
  Widget _buildErrorWidget(BuildContext context, String url, dynamic error) {
    return Container(
      color: Colors.grey[800],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.smart_toy_outlined, 
            size: 48, 
            color: Colors.white
          ),
          const SizedBox(height: 8),
          Text(
            MessageConstants.roleImageLoadFailedMessage,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}