import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/role/binding/recommend_binding.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';
import 'package:rolio/modules/sessions/binding/sessions_binding.dart';
import 'package:rolio/modules/sessions/controller/sessions_controller.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/modules/user/controller/user_controller.dart';

/// 主页控制器
///
/// 管理底部导航栏和标签页切换
class HomeController extends GetxController
    with WidgetsBindingObserver {
  // 聊天服务 - 可选依赖
  IChatService? _chatService;
  
  // 当前选中的标签页索引
  final RxInt currentTabIndex = 0.obs;
  
  // 文本控制器
  late final TextEditingController messageController;
  
  // 获取消息列表的getter
  RxList<Message> get messages => _chatService?.messages ?? <Message>[].obs;
  
  // 子控制器实例 - 使用延迟加载
  RecommendController? _recommendController;
  SessionsController? _sessionsController;
  
  // 延迟加载的控制器访问器
  RecommendController get recommendController {
    if (_recommendController == null) {
      _loadRecommendController();
    }
    return _recommendController!;
  }
  
  SessionsController get sessionsController {
    if (_sessionsController == null) {
      _loadSessionsController();
    }
    return _sessionsController!;
  }
  
  // 上次刷新会话和推荐列表的时间戳
  int _lastSessionsRefreshTime = 0;
  int _lastRecommendRefreshTime = 0;
  
  // 刷新最小间隔（毫秒）
  static const int MIN_REFRESH_INTERVAL = 5000; // 5秒，降低刷新间隔
  
  // 构造函数，不再强制依赖注入
  HomeController() {
    // 尝试获取ChatService，但不强制要求
    try {
      if (Get.isRegistered<IChatService>()) {
        _chatService = Get.find<IChatService>();
        LogUtil.debug('HomeController: 成功获取IChatService');
      } else {
        LogUtil.warn('HomeController: IChatService未注册，部分功能可能不可用');
      }
    } catch (e) {
      LogUtil.warn('HomeController: 获取IChatService失败: $e');
    }
  }
  
  // 加载RecommendController
  void _loadRecommendController() {
    try {
      _recommendController = Get.find<RecommendController>();
      LogUtil.debug('HomeController: 成功获取已注册的RecommendController');
    } catch (e) {
      LogUtil.info('HomeController: RecommendController未注册，正在按需加载');
      // 如果未注册，则按需加载
      final binding = RecommendBinding();
      binding.dependencies();
      _recommendController = Get.find<RecommendController>();
      LogUtil.debug('HomeController: 已按需加载RecommendController');
    }
  }
  
  // 加载SessionsController
  void _loadSessionsController() {
    try {
      _sessionsController = Get.find<SessionsController>();
      LogUtil.debug('HomeController: 成功获取已注册的SessionsController');
    } catch (e) {
      LogUtil.info('HomeController: SessionsController未注册，正在按需加载');
      // 如果未注册，则按需加载
      final binding = SessionsBinding();
      binding.dependencies();
      _sessionsController = Get.find<SessionsController>();
      LogUtil.debug('HomeController: 已按需加载SessionsController');
    }
  }
  
  @override
  void onInit() {
    super.onInit();
    LogUtil.info('HomeController.onInit()');
    
    WidgetsBinding.instance.addObserver(this);
    
    // 初始化文本控制器
    messageController = TextEditingController();
    
    // 监听文本变化，发送正在输入状态
    messageController.addListener(_onTextChanged);
    
    // 初始加载数据 - 避免重复加载，确保每种数据只加载一次
    _initializeData();
  }
  
  // 初始化数据
  void _initializeData() {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // 加载推荐角色列表 - 按需加载RecommendController
    try {
      if (recommendController.recommendService.recommendedRoles.isEmpty) {
        LogUtil.debug('初始化HomeController: 推荐列表为空，开始加载');
        recommendController.loadRecommendedRoles();
      } else if (now - _lastRecommendRefreshTime >= MIN_REFRESH_INTERVAL) {
        LogUtil.debug('初始化HomeController: 推荐列表已有${recommendController.recommendService.recommendedRoles.length}个角色，刷新数据');
        // 只有当推荐列表数量少于5个时才刷新
        if (recommendController.recommendService.recommendedRoles.length < 5) {
          recommendController.refreshRecommendedRoles();
        } else {
          LogUtil.debug('初始化HomeController: 推荐列表数据充足，跳过刷新');
        }
      } else {
        LogUtil.debug('初始化HomeController: 推荐列表刷新间隔过短，跳过刷新');
      }
      
      _lastRecommendRefreshTime = now;
    } catch (e) {
      LogUtil.error('初始化推荐列表失败: $e');
    }
    
    // 初始化RoleService中的角色缓存，确保角色切换功能正常
    _initializeRoleService();
    
    // 加载会话列表 - 按需加载SessionsController
    try {
      sessionsController.loadSessions();
      _lastSessionsRefreshTime = now;
    } catch (e) {
      LogUtil.error('初始化会话列表失败: $e');
    }
  }
  
  // 初始化RoleService角色缓存
  void _initializeRoleService() {
    try {
      // 获取RoleService实例
      final roleService = Get.find<RoleProvider>();
      
      // 主动调用一次角色列表获取，预热缓存
      LogUtil.debug('预热RoleService角色缓存');
      roleService.getNextRole(1, false).then((role) {
        if (role != null) {
          LogUtil.debug('成功预热RoleService缓存，获取到角色: ${role.name} (ID: ${role.id})');
        } else {
          LogUtil.warn('RoleService缓存预热返回空角色');
        }
      }).catchError((e) {
        LogUtil.error('RoleService缓存预热失败: $e');
        // 使用ErrorHandler处理异常，但不显示给用户
        ErrorHandler.handleException(e, 
          message: 'failed to initialize role service',
          showSnackbar: false
        );
      });
    } catch (e) {
      LogUtil.error('初始化RoleService失败: $e');
      // 使用ErrorHandler处理异常，但不显示给用户
      ErrorHandler.handleException(e, 
        message: 'failed to initialize role service',
        showSnackbar: false
      );
    }
  }
  
  // 切换标签页的方法
  void changeTabIndex(int index) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final previousIndex = currentTabIndex.value;
    currentTabIndex.value = index;
    
    // 如果切换到了不同的标签页，总是刷新数据
    bool isTabChanged = previousIndex != index;
    
    // 当切换到会话标签页时，刷新会话列表
    if (index == 1) {
      // 如果是切换标签或者超过刷新间隔，则刷新数据
      if (isTabChanged || now - _lastSessionsRefreshTime >= MIN_REFRESH_INTERVAL) {
        LogUtil.debug('切换到会话标签页，刷新会话列表');
        try {
          sessionsController.refreshSessions();
          _lastSessionsRefreshTime = now;
        } catch (e) {
          LogUtil.error('刷新会话列表失败: $e');
        }
      } else {
        LogUtil.debug('会话列表刷新间隔过短，跳过刷新');
      }
    } 
    // 当切换到推荐标签页时，刷新推荐列表
    else if (index == 0) {
      // 如果是切换标签或者超过刷新间隔，则刷新数据
      if (isTabChanged || now - _lastRecommendRefreshTime >= MIN_REFRESH_INTERVAL) {
        LogUtil.debug('切换到推荐标签页，刷新推荐列表');
        try {
          recommendController.refreshRecommendedRoles();
          _lastRecommendRefreshTime = now;
        } catch (e) {
          LogUtil.error('刷新推荐列表失败: $e');
        }
      } else {
        LogUtil.debug('推荐列表刷新间隔过短，跳过刷新');
      }
    }
    // 当切换到用户标签页时，无需特殊处理
    else if (index == 2) {
      LogUtil.debug('切换到用户标签页');
    }
  }
  
  // 文本变化监听
  void _onTextChanged() {
    // 当文本框有内容时，发送正在输入状态
    if (_chatService != null) {
      final isTyping = messageController.text.isNotEmpty;
      _chatService!.sendTypingStatus(isTyping);
    }
  }
  
  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    messageController.dispose();
    super.onClose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        // 应用恢复到前台，按需刷新数据
        final now = DateTime.now().millisecondsSinceEpoch;
        
        // 判断是否需要刷新推荐列表
        if (now - _lastRecommendRefreshTime >= MIN_REFRESH_INTERVAL) {
          try {
            // 只有当推荐列表为空或数量少于5个时才刷新
            if (recommendController.recommendService.recommendedRoles.isEmpty || 
                recommendController.recommendService.recommendedRoles.length < 5) {
              LogUtil.debug('应用恢复前台，刷新推荐列表');
              recommendController.refreshRecommendedRoles();
              _lastRecommendRefreshTime = now;
            } else {
              LogUtil.debug('应用恢复前台，推荐列表数据充足，跳过刷新');
            }
          } catch (e) {
            LogUtil.error('恢复前台刷新推荐列表失败: $e');
          }
        }
        
        // 判断是否需要刷新会话列表
        if (now - _lastSessionsRefreshTime >= MIN_REFRESH_INTERVAL) {
          try {
            LogUtil.debug('应用恢复前台，刷新会话列表');
            sessionsController.refreshSessions();
            _lastSessionsRefreshTime = now;
          } catch (e) {
            LogUtil.error('恢复前台刷新会话列表失败: $e');
          }
        }
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // 应用进入后台，可以在这里处理一些清理工作
        break;
    }
  }
  
  // 发送消息
  void sendMessage() {
    if (_chatService == null || messageController.text.trim().isEmpty) return;
    
    try {
      _chatService!.sendMessage(messageController.text.trim());
      messageController.clear();
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      
      // 显示普通错误提示
      ErrorHandler.handleException(
        e,
        message: 'Failed to send message',
        showSnackbar: true,
      );
    }
  }
  
  // 强制刷新当前标签页的数据
  void forceRefreshCurrentTab() {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // 根据当前标签页刷新对应的数据
    if (currentTabIndex.value == 1) {
      try {
        LogUtil.debug('强制刷新会话列表');
        sessionsController.refreshSessions();
        _lastSessionsRefreshTime = now;
      } catch (e) {
        LogUtil.error('强制刷新会话列表失败: $e');
      }
    } else if (currentTabIndex.value == 0) {
      try {
        LogUtil.debug('强制刷新推荐列表');
        recommendController.forceRefreshRecommendedRoles();
        _lastRecommendRefreshTime = now;
      } catch (e) {
        LogUtil.error('强制刷新推荐列表失败: $e');
      }
    } else if (currentTabIndex.value == 2) {
      try {
        LogUtil.debug('强制刷新用户页');
        // 如果用户页控制器有刷新方法，可以在这里调用
        if (Get.isRegistered<UserController>()) {
          final userController = Get.find<UserController>();
          userController.refreshUserInfo();
        }
      } catch (e) {
        LogUtil.error('强制刷新用户页失败: $e');
      }
    }
  }
  
  /// 打开搜索页面
  void goToSearch() {
    LogUtil.debug('打开搜索页面');
    RouterManager.navigateTo(Routes.searchScreen);
  }
}
