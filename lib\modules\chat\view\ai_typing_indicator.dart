import 'package:flutter/material.dart';
import 'dart:math' as Math;

/// AI回复时的三点动画指示器
class AITypingIndicator extends StatefulWidget {
  const AITypingIndicator({Key? key}) : super(key: key);

  @override
  _AITypingIndicatorState createState() => _AITypingIndicatorState();
}

class _AITypingIndicatorState extends State<AITypingIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    // 创建一个循环动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    )..repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildAnimatedDot(0),
            const SizedBox(width: 4),
            _buildAnimatedDot(0.2),
            const SizedBox(width: 4),
            _buildAnimatedDot(0.4),
          ],
        );
      },
    );
  }
  
  Widget _buildAnimatedDot(double delay) {
    final double animationValue = (_controller.value + delay) % 1.0;
    
    // 创建更平滑的上下移动效果
    final double translateY = -4 * Math.sin(animationValue * Math.pi);
    final double opacity = 0.4 + 0.6 * Math.sin(animationValue * Math.pi);
    
    return Transform.translate(
      offset: Offset(0, translateY),
      child: Container(
        width: 6,
        height: 6,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(opacity), // 改为白色
          shape: BoxShape.circle,
        ),
      ),
    );
  }
} 