import 'package:rolio/generated/json/base/json_convert_content.dart';
import 'package:rolio/modules/login/model/account_server_bind_email_req_entity.dart';

AccountServerBindEmailReqEntity $AccountServerBindEmailReqEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerBindEmailReqEntity accountServerBindEmailReqEntity =
      AccountServerBindEmailReqEntity();
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    accountServerBindEmailReqEntity.userId = userId;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    accountServerBindEmailReqEntity.email = email;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    accountServerBindEmailReqEntity.code = code;
  }
  return accountServerBindEmailReqEntity;
}

Map<String, dynamic> $AccountServerBindEmailReqEntityToJson(
    AccountServerBindEmailReqEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['email'] = entity.email;
  data['code'] = entity.code;
  return data;
}

extension AccountServerBindEmailReqEntityExtension
    on AccountServerBindEmailReqEntity {
  AccountServerBindEmailReqEntity copyWith({
    int? userId,
    String? email,
    String? code,
  }) {
    return AccountServerBindEmailReqEntity()
      ..userId = userId ?? this.userId
      ..email = email ?? this.email
      ..code = code ?? this.code;
  }
}
