import 'package:rolio/generated/json/base/json_convert_content.dart';
import "package:rolio/modules/login/model/account_server_register_resp_entity.dart";

AccountServerRegisterRespEntity $AccountServerRegisterRespEntityFromJson(
    Map<String, dynamic> json) {
  final AccountServerRegisterRespEntity accountServerRegisterRespEntity =
      AccountServerRegisterRespEntity();
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    accountServerRegisterRespEntity.token = token;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    accountServerRegisterRespEntity.userId = userId;
  }
  final int? userType = jsonConvert.convert<int>(json['userType']);
  if (userType != null) {
    accountServerRegisterRespEntity.userType = userType;
  }
  return accountServerRegisterRespEntity;
}

Map<String, dynamic> $AccountServerRegisterRespEntityToJson(
    AccountServerRegisterRespEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['token'] = entity.token;
  data['userId'] = entity.userId;
  data['userType'] = entity.userType;
  return data;
}

extension AccountServerRegisterRespEntityExtension
    on AccountServerRegisterRespEntity {
  AccountServerRegisterRespEntity copyWith({
    String? token,
    int? userId,
    int? userType,
  }) {
    return AccountServerRegisterRespEntity()
      ..token = token ?? this.token
      ..userId = userId ?? this.userId
      ..userType = userType ?? this.userType;
  }
}
