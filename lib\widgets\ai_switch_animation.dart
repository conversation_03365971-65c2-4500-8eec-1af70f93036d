import 'package:flutter/material.dart';

/// AI助手切换动画
/// 
/// 提供左右滑动切换AI助手时的过渡动画效果
class AiSwitchAnimation extends StatefulWidget {
  final Widget child;
  final bool isAnimating;
  final AnimationDirection direction;
  final VoidCallback onAnimationComplete;
  final Duration animationDuration;
  
  const AiSwitchAnimation({
    Key? key,
    required this.child,
    required this.isAnimating,
    required this.direction,
    required this.onAnimationComplete,
    this.animationDuration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  State<AiSwitchAnimation> createState() => _AiSwitchAnimationState();
}

/// 动画方向
enum AnimationDirection {
  left,  // 向左滑动（切换到下一个）
  right, // 向右滑动（切换到上一个）
}

class _AiSwitchAnimationState extends State<AiSwitchAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.addStatusListener(_handleAnimationStatus);
  }
  
  @override
  void didUpdateWidget(AiSwitchAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 当isAnimating状态变化时启动动画
    if (widget.isAnimating && !oldWidget.isAnimating) {
      _animationController.forward(from: 0.0);
    }
  }
  
  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      widget.onAnimationComplete();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            widget.direction == AnimationDirection.left
                ? -_animation.value * MediaQuery.of(context).size.width
                : _animation.value * MediaQuery.of(context).size.width,
            0,
          ),
          child: Opacity(
            opacity: 1.0 - _animation.value,
            child: widget.child,
          ),
        );
      },
    );
  }
  
  @override
  void dispose() {
    _animationController.removeStatusListener(_handleAnimationStatus);
    _animationController.dispose();
    super.dispose();
  }
} 