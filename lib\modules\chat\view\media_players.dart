import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:video_player/video_player.dart';
import 'package:rolio/common/constants/colors_constants.dart';

/// 音频播放器组件
class AudioPlayerItem extends StatefulWidget {
  const AudioPlayerItem({
    Key? key,
    required this.audioUrl,
    required this.isSender,
  }) : super(key: key);

  final String audioUrl;
  final bool isSender;

  @override
  State<AudioPlayerItem> createState() => _AudioPlayerItemState();
}

class _AudioPlayerItemState extends State<AudioPlayerItem> {
  late final AudioPlayer _audioPlayer;
  bool _isPlayingAudio = false;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      iconSize: 36.0,
      color: widget.isSender ? AppColors.white : AppColors.primary,
      constraints: const BoxConstraints(minWidth: 100.0),
      onPressed: () {
        if (!_isPlayingAudio) {
          _audioPlayer.play(UrlSource(widget.audioUrl));
        } else {
          _audioPlayer.pause();
        }

        setState(() => _isPlayingAudio = !_isPlayingAudio);
      },
      icon: Icon(_isPlayingAudio ? Icons.pause_circle : Icons.play_circle),
    );
  }
}

/// 视频播放器组件
class VideoPlayerItem extends StatefulWidget {
  const VideoPlayerItem({
    Key? key,
    required this.videoUrl,
  }) : super(key: key);

  final String videoUrl;

  @override
  State<VideoPlayerItem> createState() => _VideoPlayerItemState();
}

class _VideoPlayerItemState extends State<VideoPlayerItem> {
  late final VideoPlayerController _videoPlayerController;
  bool isPlaying = false;

  @override
  void initState() {
    super.initState();
    _videoPlayerController = VideoPlayerController.network(widget.videoUrl)
      ..initialize().then((_) {
        _videoPlayerController.setVolume(1.0);
      });
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Stack(
        children: [
          VideoPlayer(_videoPlayerController),
          Align(
            alignment: Alignment.center,
            child: IconButton(
              onPressed: () {
                if (isPlaying) {
                  _videoPlayerController.pause();
                } else {
                  _videoPlayerController.play();
                }
                setState(() {
                  isPlaying = !isPlaying;
                });
              },
              icon: isPlaying
                  ? const Opacity(
                      opacity: 0.6,
                      child: Icon(
                        Icons.pause_circle,
                        color: AppColors.white,
                        size: 36.0,
                      ),
                    )
                  : const Icon(
                      Icons.play_circle,
                      color: AppColors.white,
                      size: 36.0,
                    ),
            ),
          )
        ],
      ),
    );
  }
} 