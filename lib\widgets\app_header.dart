import 'package:flutter/material.dart';

class AppHeader extends StatelessWidget {
  final String activeTitle;
  final String inactiveTitle;
  final bool showSearch;
  
  const AppHeader({
    Key? key,
    required this.activeTitle,
    required this.inactiveTitle,
    this.showSearch = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          activeTitle,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.underline,
            decorationColor: Colors.white,
            decorationThickness: 2,
          ),
        ),
        const SizedBox(width: 20),
        Text(
          inactiveTitle,
          style: TextStyle(
            color: Colors.grey.shade400,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (showSearch) const Icon(Icons.search, color: Colors.white, size: 28),
      ],
    );
  }
} 