import 'package:get/get.dart';
import 'package:rolio/modules/login/repository/login_repository.dart';
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:rolio/modules/user/controller/user_controller.dart';
import 'package:rolio/modules/user/repository/user_repository.dart';
import 'package:rolio/modules/user/service/user_service.dart';

/// 用户模块绑定
class UserBinding extends Bindings {
  @override
  void dependencies() {
    // 注册登录相关依赖（如果尚未注册）
    if (!Get.isRegistered<LoginRepository>()) {
      Get.put(LoginRepository());
    }
    
    if (!Get.isRegistered<LoginService>()) {
      Get.put(LoginService(repository: Get.find<LoginRepository>()));
    }
    
    // 注册头像仓库和服务
    Get.lazyPut<UserAvatarRepository>(() => UserAvatarRepository());
    Get.lazyPut<UserAvatarService>(
      () => UserAvatarService(repository: Get.find<UserAvatarRepository>()),
    );
    
    // 注册用户控制器，现在同时注入LoginService和UserAvatarService
    Get.lazyPut<UserController>(
      () => UserController(
        loginService: Get.find<LoginService>(),
        avatarService: Get.find<UserAvatarService>(),
      ),
    );
  }
} 