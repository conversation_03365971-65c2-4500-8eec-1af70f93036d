import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/home/<USER>/home_controller.dart';
import 'package:rolio/modules/user/binding/user_binding.dart';

/// 主页模块绑定类
/// 
/// 负责主页模块相关的依赖注入和控制器绑定
class HomeBinding implements Bindings {
  @override
  void dependencies() {
    try {
      LogUtil.info('开始注册HomeBinding依赖...');
      
      // 注册UserBinding的依赖，确保UserController可用
      final userBinding = UserBinding();
      userBinding.dependencies();
      
      // 只注册HomeController，不主动加载其他模块的控制器
      Get.lazyPut<HomeController>(() {
        LogUtil.debug('创建HomeController实例');
      
        // HomeController将使用延迟加载方式获取其他控制器
        return HomeController();
      }, fenix: true);
      
      LogUtil.info('HomeBinding依赖注册完成');
    } catch (e) {
      LogUtil.error('HomeBinding依赖注册失败: $e');
      rethrow;
    }
  }
}